Index: biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.controller;\r\n\r\nimport com.get.common.consts.LoggerModulesConsts;\r\nimport com.get.common.consts.LoggerOptTypeConst;\r\nimport com.get.common.eunms.FileTypeEnum;\r\nimport com.get.common.result.*;\r\nimport com.get.common.utils.BeanCopyUtils;\r\nimport com.get.core.log.annotation.OperationLogger;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.mybatis.utils.ValidList;\r\n\r\nimport com.get.core.secure.UserInfo;\r\nimport com.get.core.secure.annotation.VerifyPermission;\r\nimport com.get.core.secure.utils.GetAuthInfo;\r\nimport com.get.core.secure.utils.SecureUtil;\r\nimport com.get.core.tool.utils.RequestContextUtil;\r\nimport com.get.institutioncenter.dto.MediaAndAttachedDto;\r\nimport com.get.institutioncenter.dto.NewsDto;\r\nimport com.get.institutioncenter.vo.MediaAndAttachedVo;\r\nimport com.get.institutioncenter.vo.NewsEmailTagDto;\r\nimport com.get.institutioncenter.vo.NewsVo;\r\nimport com.get.institutioncenter.service.INewsService;\r\nimport com.get.institutioncenter.dto.NewEmailToAgentDto;\r\nimport com.get.institutioncenter.dto.NewsCompanyDto;\r\nimport com.get.institutioncenter.dto.query.NewsQueryDto;\r\nimport com.get.permissioncenter.vo.tree.CompanyTreeVo;\r\nimport io.swagger.annotations.Api;\r\nimport io.swagger.annotations.ApiOperation;\r\nimport org.springframework.validation.annotation.Validated;\r\nimport org.springframework.web.bind.annotation.*;\r\n\r\nimport javax.annotation.Resource;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * <AUTHOR> * @DATE: 2020/8/19\r\n * @TIME: 14:20\r\n * @Description:\r\n **/\r\n@Api(tags = \"新闻管理\")\r\n@RestController\r\n@RequestMapping(\"/institution/news\")\r\npublic class NewsController {\r\n    @Resource\r\n    private INewsService newsService;\r\n\r\n    /**\r\n     * 详情\r\n     *\r\n     * @param id\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"配置详情接口\", notes = \"id为此条数据id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = \"学校中心/新闻管理/新闻详情\")\r\n    @GetMapping(\"/{id}\")\r\n    public ResponseBo<NewsVo> detail(@PathVariable(\"id\") Long id) {\r\n        NewsVo data = newsService.findNewsById(id);\r\n        return new ResponseBo<>(data);\r\n    }\r\n\r\n    /**\r\n     * 新增信息\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"新增接口\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/新增新闻\")\r\n    @PostMapping(\"add\")\r\n    public ResponseBo add(@RequestBody @Validated(NewsDto.Add.class) NewsDto newsDto) {\r\n        return SaveResponseBo.ok(newsService.addNews(newsDto));\r\n    }\r\n\r\n    /**\r\n     * 删除信息\r\n     *\r\n     * @param id\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"删除接口\", notes = \"id为此条数据的id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = \"学校中心/新闻管理/删除新闻\")\r\n    @PostMapping(\"delete/{id}\")\r\n    public ResponseBo delete(@PathVariable(\"id\") Long id) {\r\n        newsService.delete(id);\r\n        return DeleteResponseBo.ok();\r\n    }\r\n\r\n    /**\r\n     * 修改信息\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"修改接口\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/更新新闻\")\r\n    @PostMapping(\"update\")\r\n    public ResponseBo<NewsVo> update(@RequestBody @Validated(NewsDto.Update.class) NewsDto newsDto) {\r\n        return UpdateResponseBo.ok(newsService.updateNews(newsDto));\r\n    }\r\n\r\n    /**\r\n     * 列表数据\r\n     *\r\n     * @param page\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"列表数据\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = \"学校中心/新闻管理/查询新闻\")\r\n    @PostMapping(\"datas\")\r\n    public ResponseBo<NewsVo> datas(@RequestBody SearchBean<NewsQueryDto> page) {\r\n        List<NewsVo> datas = newsService.datas(page.getData(), page);\r\n        Page p = BeanCopyUtils.objClone(page, Page::new);\r\n        return new ListResponseBo<>(datas, p);\r\n    }\r\n\r\n    /**\r\n     * 目标类型下拉框数据\r\n     *\r\n     * @return\r\n     * @\r\n     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"目标类型下拉框数据\", notes = \"\")\r\n    @GetMapping(\"findTargetType\")\r\n    public ResponseBo findTargetType() {\r\n        List<Map<String, Object>> datas = newsService.findTargetType();\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n    /**\r\n     * 目标类型获取目标\r\n     *\r\n     * @return\r\n     * @\r\n     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"目标类型获取目标\", notes = \"\")\r\n    @PostMapping(\"findTarget\")\r\n    public ResponseBo<BaseSelectEntity> findTarget(@RequestParam String tableName) {\r\n        if(\"institution_news_bulletin_board\".equals(tableName)){\r\n            return new ResponseBo<>(null);\r\n        }\r\n        List<BaseSelectEntity> datas = newsService.findTarget(tableName);\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>\r\n     * @Description: 回显新闻和公司的关系\r\n     * @Param [id]\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"回显新闻和公司的关系\", notes = \"id为此条数据id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = \"学校中心/新闻管理/新闻和公司的关系（数据回显）\")\r\n    @GetMapping(\"getNewsRelation/{newId}\")\r\n    public ResponseBo<CompanyTreeVo> getNewsCompanyRelation(@PathVariable(\"newId\") Long id) {\r\n        List<CompanyTreeVo> newsCompanyRelation = newsService.getNewRelation(id);\r\n        return new ListResponseBo<>(newsCompanyRelation);\r\n    }\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 新闻安全配置\r\n     * @Param [validList]\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"联系人-公司安全配置\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/安全配置\")\r\n    @PostMapping(\"editNewsRelation\")\r\n    public ResponseBo editNewsRelation(@RequestBody @Validated(NewsCompanyDto.Add.class) ValidList<NewsCompanyDto> validList) {\r\n        newsService.editNewsCompany(validList);\r\n        return UpdateResponseBo.ok();\r\n    }\r\n\r\n    /**\r\n     * @param mediaAttachedVo\r\n     * @return\r\n     * @\r\n     */\r\n\r\n    @ApiOperation(value = \"保存文件\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/保存附件资料\")\r\n    @PostMapping(\"addMediaAndAttached\")\r\n    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {\r\n        return UpdateResponseBo.ok(newsService.addNewsMedia(mediaAttachedVo));\r\n    }\r\n\r\n    /**\r\n     * 查询新闻附件\r\n     *\r\n     * @param voSearchBean\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"查询新闻附件\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = \"学校中心/新闻管理/查询新闻附件\")\r\n    @PostMapping(\"getNewsMedia\")\r\n    public ResponseBo<MediaAndAttachedVo> getNewsMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {\r\n        List<MediaAndAttachedVo> newsMedia = newsService.getNewsMedia(voSearchBean.getData(), voSearchBean);\r\n        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);\r\n        return new ListResponseBo<>(newsMedia, page);\r\n    }\r\n\r\n    /**\r\n     * 所有新闻下拉框\r\n     *\r\n     * @Date 11:54 2021/7/28\r\n     * <AUTHOR>     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"所有新闻下拉框\", notes = \"\")\r\n    @GetMapping(\"getAllNewsSelect\")\r\n    public ResponseBo<BaseSelectEntity> getAllNewsSelect() {\r\n        List<BaseSelectEntity> datas = newsService.getAllNewsSelect();\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 发送新闻电邮\r\n     * @Param []\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"发送新闻电邮\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮\")\r\n    @GetMapping(\"sendNewsMail/{id}\")\r\n    public ResponseBo sendNewsMail(@PathVariable(\"id\") Long id) throws Exception {\r\n        newsService.sendNewsMail(id);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to代理\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to代理\")\r\n    @PostMapping(\"sendNewsMailtoAgent\")\r\n    public ResponseBo sendNewsMailtoAgent( @RequestBody @Validated(NewEmailToAgentDto.Update.class) NewEmailToAgentDto newEmailToAgentVo) throws Exception {\r\n        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();\r\n        UserInfo user = GetAuthInfo.getUser();\r\n        String locale = SecureUtil.getLocale();\r\n        newsService.sendNewsMailtoAgent(newEmailToAgentVo, headerMap, user, locale);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to所有代理\", notes = \"type:1Hubs/2市场\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to所有代理\")\r\n    @PostMapping(\"sendNewsMailtoAllAgent\")\r\n    public ResponseBo sendNewsMailtoAllAgent( @RequestParam Long id, @RequestParam Integer type) throws Exception {\r\n        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();\r\n        UserInfo user = GetAuthInfo.getUser();\r\n        String locale = SecureUtil.getLocale();\r\n        newsService.sendNewsMailtoAllAgent(id, headerMap, user, locale, type);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to自己\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to自己\")\r\n    @PostMapping(\"sendNewsMailtoMe\")\r\n    public ResponseBo sendNewsMailtoMe(@RequestParam Long id) throws Exception {\r\n        newsService.sendNewsMailtoMe(id);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 新闻附件类型\r\n     * @Param []\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"新闻附件类型\", notes = \"\")\r\n    @PostMapping(\"getNewsMediaTypeSelect\")\r\n    public ResponseBo getNewsMediaTypeSelect() {\r\n        return new ListResponseBo<>(FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.NEWS));\r\n    }\r\n\r\n    /**\r\n     * feign调用 查询新闻标题Map\r\n     *\r\n     * @Date 16:01 2021/6/10\r\n     * <AUTHOR>     */\r\n  /*  @ApiIgnore\r\n    @PostMapping(\"getNewsTitlesByIds\")\r\n    @VerifyPermission(IsVerify = false)\r\n    public Map<Long, String> getNewsTitlesByIds(@RequestBody Set<Long> ids)  {\r\n        return newsService.getNewsTitlesByIds(ids);\r\n    }*/\r\n\r\n    /**\r\n     * @Description: 新增邮件标签\r\n     * @param newsEmailTagVo\r\n     * @return\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"新增邮件标签\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/新增邮件标签\")\r\n    @PostMapping(\"addEmailTagName\")\r\n    public ResponseBo addEmailTagName(@RequestBody NewsEmailTagDto newsEmailTagVo) {\r\n         newsService.addNewsEmailTage(newsEmailTagVo);\r\n         return ResponseBo.ok();\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java
--- a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java	(revision 31f9e89f66b14f423362dac97fe1fdad95e24d33)
+++ b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java	(date 1755511599852)
@@ -3,7 +3,13 @@
 import com.get.common.consts.LoggerModulesConsts;
 import com.get.common.consts.LoggerOptTypeConst;
 import com.get.common.eunms.FileTypeEnum;
-import com.get.common.result.*;
+import com.get.common.result.DeleteResponseBo;
+import com.get.common.result.ListResponseBo;
+import com.get.common.result.Page;
+import com.get.common.result.ResponseBo;
+import com.get.common.result.SaveResponseBo;
+import com.get.common.result.SearchBean;
+import com.get.common.result.UpdateResponseBo;
 import com.get.common.utils.BeanCopyUtils;
 import com.get.core.log.annotation.OperationLogger;
 import com.get.core.mybatis.base.BaseSelectEntity;
@@ -17,7 +23,6 @@
 import com.get.institutioncenter.dto.MediaAndAttachedDto;
 import com.get.institutioncenter.dto.NewsDto;
 import com.get.institutioncenter.vo.MediaAndAttachedVo;
-import com.get.institutioncenter.vo.NewsEmailTagDto;
 import com.get.institutioncenter.vo.NewsVo;
 import com.get.institutioncenter.service.INewsService;
 import com.get.institutioncenter.dto.NewEmailToAgentDto;
@@ -27,7 +32,13 @@
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiOperation;
 import org.springframework.validation.annotation.Validated;
-import org.springframework.web.bind.annotation.*;
+import org.springframework.web.bind.annotation.GetMapping;
+import org.springframework.web.bind.annotation.PathVariable;
+import org.springframework.web.bind.annotation.PostMapping;
+import org.springframework.web.bind.annotation.RequestBody;
+import org.springframework.web.bind.annotation.RequestMapping;
+import org.springframework.web.bind.annotation.RequestParam;
+import org.springframework.web.bind.annotation.RestController;
 
 import javax.annotation.Resource;
 import java.util.List;
@@ -303,8 +314,7 @@
     @ApiOperation(value = "新增邮件标签", notes = "")
     @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/新增邮件标签")
     @PostMapping("addEmailTagName")
-    public ResponseBo addEmailTagName(@RequestBody NewsEmailTagDto newsEmailTagVo) {
-         newsService.addNewsEmailTage(newsEmailTagVo);
+    public ResponseBo addEmailTagName() {
          return ResponseBo.ok();
     }
 }
Index: biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.service;\r\n\r\nimport com.get.common.result.Page;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.mybatis.base.BaseService;\r\nimport com.get.core.secure.UserInfo;\r\nimport com.get.institutioncenter.dto.MediaAndAttachedDto;\r\nimport com.get.institutioncenter.dto.NewsDto;\r\nimport com.get.institutioncenter.vo.MediaAndAttachedVo;\r\nimport com.get.institutioncenter.vo.NewsEmailTagDto;\r\nimport com.get.institutioncenter.vo.NewsVo;\r\nimport com.get.institutioncenter.entity.News;\r\nimport com.get.institutioncenter.dto.NewEmailToAgentDto;\r\nimport com.get.institutioncenter.dto.NewsCompanyDto;\r\nimport com.get.institutioncenter.dto.query.NewsQueryDto;\r\nimport com.get.permissioncenter.vo.tree.CompanyTreeVo;\r\nimport com.get.remindercenter.dto.AliyunSendMailDto;\r\nimport com.get.remindercenter.vo.AliyunSendMailVo;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.Set;\r\n\r\n/**\r\n * <AUTHOR> * @DATE: 2020/8/18\r\n * @TIME: 17:41\r\n * @Description:\r\n **/\r\npublic interface INewsService extends BaseService<News> {\r\n    /**\r\n     * 列表数据\r\n     *\r\n     * @param newsVo\r\n     * @param page\r\n     * @return\r\n     */\r\n    List<NewsVo> datas(NewsQueryDto newsVo, Page page);\r\n\r\n    /**\r\n     * 保存\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    Long addNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 详情\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    NewsVo findNewsById(Long id);\r\n\r\n    /**\r\n     * 删除\r\n     *\r\n     * @param id\r\n     */\r\n    void delete(Long id);\r\n\r\n    /**\r\n     * 修改\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    NewsVo updateNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 获取目标类型\r\n     *\r\n     * @return\r\n     */\r\n    List<Map<String, Object>> findTargetType();\r\n\r\n    /**\r\n     * 通过表和id删除新闻\r\n     *\r\n     * @return\r\n     */\r\n    void deleteNewsByTableId(Long id, String tableName);\r\n\r\n    /**\r\n     * 目标详情里面的新闻列表\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    List<NewsVo> getNewsDtoByTarget(NewsDto newsDto);\r\n\r\n    /**\r\n     * 目标类型获取目标\r\n     *\r\n     * @return\r\n     */\r\n    List<BaseSelectEntity> findTarget(String tableName);\r\n\r\n    /**\r\n     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>\r\n     * @Description: 新闻公司回显\r\n     * @Param [id]\r\n     * <AUTHOR>     **/\r\n    //List<CompanyTreeVo> getNewRelation(Long id);\r\n    List<CompanyTreeVo> getNewRelation(Long id);\r\n    /**\r\n     * @return void\r\n     * @Description: 安全配置\r\n     * @Param [validList]\r\n     * <AUTHOR>     **/\r\n    void editNewsCompany(List<NewsCompanyDto> validList);\r\n\r\n    /**\r\n     * 保存附件\r\n     *\r\n     * @param mediaAttachedVo\r\n     * @return\r\n     */\r\n    List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVo);\r\n\r\n\r\n    /**\r\n     * 查询附件\r\n     *\r\n     * @param data\r\n     * @param page\r\n     * @return\r\n     * @\r\n     */\r\n    List<MediaAndAttachedVo> getNewsMedia(MediaAndAttachedDto data, Page page);\r\n\r\n    /**\r\n     * 所有新闻下拉框\r\n     *\r\n     * @Date 11:54 2021/7/28\r\n     * <AUTHOR>     */\r\n    List<BaseSelectEntity> getAllNewsSelect();\r\n\r\n    /**\r\n     * feign调用 查询新闻标题Map\r\n     *\r\n     * @Date 12:40 2021/7/28\r\n     * <AUTHOR>     */\r\n    Map<Long, String> getNewsTitlesByIds(Set<Long> ids);\r\n\r\n    /**\r\n     * 发送新闻电邮通知\r\n     * @param id\r\n     */\r\n    void sendNewsMail(Long id) throws Exception;\r\n\r\n    Boolean checkNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 发送新闻电邮通知to代理\r\n     *\r\n     * @param newEmailToAgentDto\r\n     * @param headerMap\r\n     * @param user\r\n     * @param locale\r\n     */\r\n    void sendNewsMailtoAgent(NewEmailToAgentDto newEmailToAgentDto, Map<String, String> headerMap, UserInfo user, String locale);\r\n\r\n    /**\r\n     * 发送新闻电邮to所有代理\r\n     *\r\n     * @param id\r\n     * @param headerMap\r\n     * @param user\r\n     * @param locale\r\n     * @param type\r\n     */\r\n    void sendNewsMailtoAllAgent(Long id, Map<String, String> headerMap, UserInfo user, String locale, Integer type);\r\n\r\n    /**\r\n     * 发送新闻电邮to自己\r\n     * @param id\r\n     */\r\n    void sendNewsMailtoMe(Long id);\r\n\r\n    /**\r\n     * 为某一个新闻绑定Tag标签（阿里云服务统计数据用）\r\n     * @param newsEmailTagDto\r\n     * @return\r\n     */\r\n    Long addNewsEmailTage(NewsEmailTagDto newsEmailTagDto);\r\n\r\n    /**\r\n     *\r\n     * @param id 新闻id\r\n     * @param type 发送类型 0不区分/1hubs/2市场\r\n     * @return  获取新闻模板\r\n     */\r\n    AliyunSendMailDto getNewEmailTmpelte(Long id, Integer type);\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java
--- a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java	(revision 31f9e89f66b14f423362dac97fe1fdad95e24d33)
+++ b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java	(date 1755511629448)
@@ -6,8 +6,8 @@
 import com.get.core.secure.UserInfo;
 import com.get.institutioncenter.dto.MediaAndAttachedDto;
 import com.get.institutioncenter.dto.NewsDto;
+import com.get.institutioncenter.dto.NewsEmailTagDto;
 import com.get.institutioncenter.vo.MediaAndAttachedVo;
-import com.get.institutioncenter.vo.NewsEmailTagDto;
 import com.get.institutioncenter.vo.NewsVo;
 import com.get.institutioncenter.entity.News;
 import com.get.institutioncenter.dto.NewEmailToAgentDto;
Index: biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.vo;\r\n\r\nimport io.swagger.annotations.ApiModelProperty;\r\nimport lombok.AllArgsConstructor;\r\nimport lombok.Data;\r\nimport lombok.NoArgsConstructor;\r\n\r\n/**\r\n * <AUTHOR> * @date 2024/10/18 14:11\r\n */\r\n@Data\r\npublic class NewsEmailTagDto {\r\n    /**\r\n     * 新闻Id\r\n     */\r\n    @ApiModelProperty(value = \"新闻Id\")\r\n    private Long fkNewsId;\r\n\r\n    /**\r\n     * 标签名称\r\n     */\r\n    @ApiModelProperty(value = \"标签名\")\r\n    private String tagName;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java b/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java
--- a/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java	(revision 31f9e89f66b14f423362dac97fe1fdad95e24d33)
+++ b/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java	(date 1755511629445)
@@ -1,4 +1,4 @@
-package com.get.institutioncenter.vo;
+package com.get.institutioncenter.dto;
 
 import io.swagger.annotations.ApiModelProperty;
 import lombok.AllArgsConstructor;
Index: biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.salecenter.service.impl;\r\n\r\nimport cn.hutool.core.util.ArrayUtil;\r\nimport cn.hutool.core.util.URLUtil;\r\nimport com.alibaba.fastjson.JSONObject;\r\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\r\nimport com.baomidou.mybatisplus.core.metadata.IPage;\r\nimport com.baomidou.mybatisplus.core.toolkit.ObjectUtils;\r\nimport com.baomidou.mybatisplus.core.toolkit.Wrappers;\r\nimport com.get.common.consts.AESConstant;\r\nimport com.get.common.consts.CacheKeyConstants;\r\nimport com.get.common.consts.LoggerModulesConsts;\r\nimport com.get.common.consts.SaleCenterConstant;\r\nimport com.get.common.eunms.FileTypeEnum;\r\nimport com.get.common.eunms.ProjectExtraEnum;\r\nimport com.get.common.eunms.ProjectKeyEnum;\r\nimport com.get.common.eunms.TableEnum;\r\nimport com.get.common.result.Page;\r\nimport com.get.common.utils.AESUtils;\r\nimport com.get.common.utils.BeanCopyUtils;\r\nimport com.get.common.utils.LocaleMessageUtils;\r\nimport com.get.core.log.exception.GetServiceException;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.mybatis.base.UtilService;\r\nimport com.get.core.mybatis.service.impl.GetServiceImpl;\r\nimport com.get.core.mybatis.support.GetCondition;\r\nimport com.get.core.mybatis.utils.PageUtil;\r\nimport com.get.core.redis.cache.GetRedis;\r\nimport com.get.core.secure.utils.SecureUtil;\r\nimport com.get.core.start.constant.AppConstant;\r\nimport com.get.core.tool.api.Result;\r\nimport com.get.core.tool.utils.CollectionUtil;\r\nimport com.get.core.tool.utils.GeneralTool;\r\nimport com.get.filecenter.dto.FileDto;\r\nimport com.get.filecenter.dto.SaleFileDto;\r\nimport com.get.filecenter.feign.IFileCenterClient;\r\nimport com.get.filecenter.vo.FileVo;\r\nimport com.get.financecenter.feign.IFinanceCenterClient;\r\nimport com.get.institutioncenter.feign.IInstitutionCenterClient;\r\nimport com.get.institutioncenter.vo.AreaCountryVo;\r\nimport com.get.partnercenter.dto.RegisterPartnerUserDto;\r\nimport com.get.partnercenter.enums.MailTemplateTypeEnum;\r\nimport com.get.partnercenter.feign.IPartnerCenterClient;\r\nimport com.get.partnercenter.vo.RegisterPartnerUserVo;\r\nimport com.get.permissioncenter.feign.IPermissionCenterClient;\r\nimport com.get.permissioncenter.vo.ConfigVo;\r\nimport com.get.permissioncenter.vo.StaffVo;\r\nimport com.get.registrationcenter.entity.User;\r\nimport com.get.registrationcenter.feign.IRegistrationCenterClient;\r\nimport com.get.remindercenter.dto.RemindTaskDto;\r\nimport com.get.remindercenter.enums.EmailTemplateEnum;\r\nimport com.get.remindercenter.feign.IReminderCenterClient;\r\nimport com.get.salecenter.dao.newissue.NewIssueStudentMapper;\r\nimport com.get.salecenter.dao.newissue.NewIssueUserAgentMapper;\r\nimport com.get.salecenter.dao.newissue.newIssueUserSuperiorMapper;\r\nimport com.get.salecenter.dao.sale.AgentContractAccountMapper;\r\nimport com.get.salecenter.dao.sale.AgentContractCompanyMapper;\r\nimport com.get.salecenter.dao.sale.AgentContractMapper;\r\nimport com.get.salecenter.dao.sale.AppAgentContactPersonMapper;\r\nimport com.get.salecenter.dao.sale.AppAgentMapper;\r\nimport com.get.salecenter.dao.sale.ContactPersonCompanyMapper;\r\nimport com.get.salecenter.dao.sale.RStudentIssueStudentMapper;\r\nimport com.get.salecenter.dto.AgentContractAccountDto;\r\nimport com.get.salecenter.dto.AgentContractDto;\r\nimport com.get.salecenter.dto.AgentContractRenewalDto;\r\nimport com.get.salecenter.dto.AgentDto;\r\nimport com.get.salecenter.dto.AgentIdCardDto;\r\nimport com.get.salecenter.dto.AppAgentAddDto;\r\nimport com.get.salecenter.dto.AppAgentApproveCommentDto;\r\nimport com.get.salecenter.dto.AppAgentChangeDataDto;\r\nimport com.get.salecenter.dto.AppAgentContactPersonAddDto;\r\nimport com.get.salecenter.dto.AppAgentContactPersonListDto;\r\nimport com.get.salecenter.dto.AppAgentContractAccountAddDto;\r\nimport com.get.salecenter.dto.AppAgentContractAccountListDto;\r\nimport com.get.salecenter.dto.AppAgentDto;\r\nimport com.get.salecenter.dto.AppAgentListDto;\r\nimport com.get.salecenter.dto.AppAgentRenewalUpdateDto;\r\nimport com.get.salecenter.dto.AppAgentUpdateDto;\r\nimport com.get.salecenter.dto.ContactPersonDto;\r\nimport com.get.salecenter.dto.EmailSendContext;\r\nimport com.get.salecenter.dto.MediaAndAttachedDto;\r\nimport com.get.salecenter.dto.StudentAgentBindingDto;\r\nimport com.get.salecenter.entity.Agent;\r\nimport com.get.salecenter.entity.AgentCompany;\r\nimport com.get.salecenter.entity.AgentContract;\r\nimport com.get.salecenter.entity.AgentContractAccount;\r\nimport com.get.salecenter.entity.AgentStaff;\r\nimport com.get.salecenter.entity.AppAgent;\r\nimport com.get.salecenter.entity.AppAgentContactPerson;\r\nimport com.get.salecenter.entity.AppAgentContractAccount;\r\nimport com.get.salecenter.entity.ContactPersonType;\r\nimport com.get.salecenter.entity.NewIssueStudent;\r\nimport com.get.salecenter.entity.NewIssueUserAgent;\r\nimport com.get.salecenter.entity.NewIssueUserSuperior;\r\nimport com.get.salecenter.entity.RStudentIssueStudent;\r\nimport com.get.salecenter.entity.SaleContactPerson;\r\nimport com.get.salecenter.entity.SaleContactPersonCompany;\r\nimport com.get.salecenter.entity.SaleMediaAndAttached;\r\nimport com.get.salecenter.entity.StaffBdCode;\r\nimport com.get.salecenter.enums.AgentAppFromEnum;\r\nimport com.get.salecenter.enums.AgentAppTypeEnum;\r\nimport com.get.salecenter.enums.AgentContractApprovalStatusEnum;\r\nimport com.get.salecenter.enums.AppAgentStatusEnum;\r\nimport com.get.salecenter.enums.ContactPersonTypeEnum;\r\nimport com.get.salecenter.enums.ContractTemplateModeEnum;\r\nimport com.get.salecenter.enums.MiniProgramPageEnum;\r\nimport com.get.salecenter.service.IAgentCompanyService;\r\nimport com.get.salecenter.service.IAgentContractAccountService;\r\nimport com.get.salecenter.service.IAgentContractAgentAccountService;\r\nimport com.get.salecenter.service.IAgentContractService;\r\nimport com.get.salecenter.service.IAgentService;\r\nimport com.get.salecenter.service.IAgentStaffService;\r\nimport com.get.salecenter.service.IAppAgentApproveCommentService;\r\nimport com.get.salecenter.service.IAppAgentContactPersonService;\r\nimport com.get.salecenter.service.IAppAgentContractAccountService;\r\nimport com.get.salecenter.service.IAppAgentService;\r\nimport com.get.salecenter.service.IContactPersonService;\r\nimport com.get.salecenter.service.IContactPersonTypeService;\r\nimport com.get.salecenter.service.IMediaAndAttachedService;\r\nimport com.get.salecenter.service.IStaffBdCodeService;\r\nimport com.get.salecenter.service.IStudentService;\r\nimport com.get.salecenter.utils.EmailSenderUtils;\r\nimport com.get.salecenter.utils.MyStringUtils;\r\nimport com.get.salecenter.vo.AddAppAgentContext;\r\nimport com.get.salecenter.vo.AppAgentChangeDataVo;\r\nimport com.get.salecenter.vo.AppAgentContactPersonListVo;\r\nimport com.get.salecenter.vo.AppAgentContractAccountListVo;\r\nimport com.get.salecenter.vo.AppAgentFormDetailVo;\r\nimport com.get.salecenter.vo.AppAgentListVo;\r\nimport com.get.salecenter.vo.AppAgentSetAgreeContext;\r\nimport com.get.salecenter.vo.AppAgentVo;\r\nimport com.get.salecenter.vo.MediaAndAttachedVo;\r\nimport com.google.common.collect.Lists;\r\nimport com.google.common.collect.Maps;\r\nimport com.google.common.collect.Sets;\r\nimport io.seata.spring.annotation.GlobalTransactional;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.lang.StringUtils;\r\nimport org.springframework.context.annotation.Lazy;\r\nimport org.springframework.stereotype.Service;\r\nimport org.springframework.transaction.annotation.Transactional;\r\n\r\nimport javax.annotation.Resource;\r\nimport javax.servlet.http.HttpServletResponse;\r\nimport java.io.BufferedOutputStream;\r\nimport java.io.IOException;\r\nimport java.net.URLEncoder;\r\nimport java.nio.charset.StandardCharsets;\r\nimport java.util.ArrayList;\r\nimport java.util.Arrays;\r\nimport java.util.Base64;\r\nimport java.util.Collections;\r\nimport java.util.Date;\r\nimport java.util.HashMap;\r\nimport java.util.HashSet;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.Objects;\r\nimport java.util.Properties;\r\nimport java.util.Set;\r\nimport java.util.StringJoiner;\r\nimport java.util.UUID;\r\nimport java.util.function.Function;\r\nimport java.util.stream.Collectors;\r\n\r\nimport static com.get.salecenter.service.impl.AppAgentChangeStatementPdfGenerator.generatePdf;\r\n\r\n\r\n/**\r\n * @author: Hardy\r\n * @create: 2022/11/17 17:16\r\n * @verison: 1.0\r\n * @description:\r\n */\r\n@Slf4j\r\n@Service\r\npublic class AppAgentServiceImpl extends GetServiceImpl<AppAgentMapper, AppAgent> implements IAppAgentService {\r\n\r\n    //授权用户IAE访问桶网址\r\n    public final static String OSS_FILES_IAE_PRD_URL = \"https://hti-ais-files-prd-**********.cos.ap-shanghai.myqcloud.com\";\r\n    //授权用户访问桶网址\r\n    public final static String OSS_FILES_DEV_URL = \"https://hti-ais-files-dev-**********.cos.ap-shanghai.myqcloud.com\";\r\n    public final static String OSS_FILES_PRD_URL = \"https://get-bms-files-prd-**********.cos.ap-shanghai.myqcloud.com\";\r\n    public final static String OSS_FILES_TEST_URL = \"https://get-bms-files-test-**********.cos.ap-shanghai.myqcloud.com\";\r\n\r\n    @Resource\r\n    private AgentContractAccountMapper agentContractAccountMapper;\r\n\r\n    @Resource\r\n    private IReminderCenterClient reminderCenterClient;\r\n\r\n    @Resource\r\n    private IPartnerCenterClient partnerCenterClient;\r\n\r\n    @Resource\r\n    private EmailSenderUtils emailSenderUtils;\r\n\r\n\r\n    @Resource\r\n    private ContactPersonCompanyMapper contactPersonCompanyMapper;\r\n\r\n    @Resource\r\n    private IAgentContractAgentAccountService agentContractAgentAccountService;\r\n\r\n    @Resource\r\n    private AgentContractMapper agentContractMapper;\r\n\r\n    @Resource\r\n    private AgentContractCompanyMapper agentContractCompanyMapper;\r\n\r\n    @Resource\r\n    private UtilService utilService;\r\n    @Resource\r\n    private AppAgentMapper appAgentMapper;\r\n    @Lazy\r\n    @Resource\r\n    private IAppAgentContactPersonService appAgentContactPersonService;\r\n    @Resource\r\n    private AppAgentContactPersonMapper appAgentContactPersonMapper;\r\n    @Lazy\r\n    @Resource\r\n    private IAppAgentContractAccountService appAgentContractAccountService;\r\n    @Lazy\r\n    @Resource\r\n    private IAppAgentApproveCommentService appAgentApproveCommentService;\r\n    @Lazy\r\n    @Resource\r\n    private IMediaAndAttachedService mediaAndAttachedService;\r\n    @Resource\r\n    private IPermissionCenterClient permissionCenterClient;\r\n    @Resource\r\n    private IInstitutionCenterClient institutionCenterClient;\r\n    @Resource\r\n    private IRegistrationCenterClient registrationCenterClient;\r\n    @Lazy\r\n    @Resource\r\n    private IAgentService agentService;\r\n    @Lazy\r\n    @Resource\r\n    private IContactPersonTypeService contactPersonTypeService;\r\n    @Resource\r\n    private IFinanceCenterClient financeCenterClient;\r\n    @Lazy\r\n    @Resource\r\n    private IAgentContractService agentContractService;\r\n    @Lazy\r\n    @Resource\r\n    private IAgentCompanyService agentCompanyService;\r\n    @Lazy\r\n    @Resource\r\n    private IAgentContractAccountService agentContractAccountService;\r\n    @Lazy\r\n    @Resource\r\n    private IStaffBdCodeService staffBdCodeService;\r\n    @Lazy\r\n    @Resource\r\n    private IAgentStaffService agentStaffService;\r\n    @Lazy\r\n    @Resource\r\n    private IContactPersonService contactPersonService;\r\n    @Resource\r\n    private IFileCenterClient fileCenterClient;\r\n    @Resource\r\n    private NewIssueUserAgentMapper newIssueUserAgentMapper;\r\n    @Resource\r\n    private newIssueUserSuperiorMapper newIssueUserSuperiorMapper;\r\n    @Resource\r\n    private NewIssueStudentMapper newIssueStudentMapper;\r\n    @Resource\r\n    private RStudentIssueStudentMapper rStudentIssueStudentMapper;\r\n    @Lazy\r\n    @Resource\r\n    private IStudentService studentService;\r\n    @Resource\r\n    private GetRedis getRedis;\r\n\r\n    /**\r\n     * 代理申请数据校验\r\n     *\r\n     * @param appAgentAddDto\r\n     */\r\n    private void validateAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        if (GeneralTool.isEmpty(appAgentAddDto)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_vo_null\"));\r\n        }\r\n        List<AppAgentContactPersonAddDto> appAgentContactPersonAddVoList = appAgentAddDto\r\n                .getAppAgentContactPersonAddVos();\r\n        if (CollectionUtil.isEmpty(appAgentContactPersonAddVoList)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_contact_person_not_null\"));\r\n        }\r\n        if (!AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {\r\n            return;\r\n        }\r\n        // 校验新版联系人数据\r\n        validateNewAppAgentContractPersonList(appAgentContactPersonAddVoList);\r\n    }\r\n\r\n    /**\r\n     * 校验新版联系人数据\r\n     *\r\n     * @param appAgentContactPersonAddVoList\r\n     */\r\n    private void validateNewAppAgentContractPersonList(\r\n            List<AppAgentContactPersonAddDto> appAgentContactPersonAddVoList) {\r\n        if (CollectionUtil.isEmpty(appAgentContactPersonAddVoList)) {\r\n            log.error(\"代理联系人不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_contact_person_cannot_be_empty\"));\r\n        }\r\n        if (appAgentContactPersonAddVoList.size() != 3) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_contact_person_count_invalid\"));\r\n        }\r\n        // 使用ContactPersonTypeEnum中的校验方法验证联系人类型\r\n        List<String> contactPersonTypeCodes = appAgentContactPersonAddVoList.stream()\r\n                .map(AppAgentContactPersonAddDto::getFkContactPersonTypeKey)\r\n                .collect(Collectors.toList());\r\n        // 校验联系人类型是否合法\r\n        if (!ContactPersonTypeEnum.validateRequiredContactPersonTypes(contactPersonTypeCodes)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_new_contact_person_type_invalid\"));\r\n        }\r\n\r\n        // 校验紧急联系人不能与企业负责人/小程序管理员和佣金结算负责人是同一个人\r\n        // 紧急联系人\r\n        AppAgentContactPersonAddDto emergencyContact = appAgentContactPersonAddVoList.stream()\r\n                .filter(contact -> ContactPersonTypeEnum.EMERGENCY.getCode()\r\n                        .equals(contact.getFkContactPersonTypeKey()))\r\n                .findFirst()\r\n                .orElse(null);\r\n\r\n        // 检查紧急联系人与其他两种角色是否有相同的姓名或邮箱（避免角色重复）\r\n        boolean hasDuplicate = appAgentContactPersonAddVoList.stream()\r\n                .filter(contact -> !ContactPersonTypeEnum.EMERGENCY.getCode()\r\n                        .equals(contact.getFkContactPersonTypeKey()))\r\n                .anyMatch(contact -> contact.getName().equals(emergencyContact.getName())\r\n                        || contact.getEmail().equals(emergencyContact.getEmail()));\r\n\r\n        if (hasDuplicate) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_emergency_contact_duplicate\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 代理申请续签修改\r\n     *\r\n     * @param appAgentRenewalUpdateDto\r\n     */\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public void renewalUpdate(AppAgentRenewalUpdateDto appAgentRenewalUpdateDto) {\r\n        Long id = appAgentRenewalUpdateDto.getId();\r\n        if (ObjectUtils.isNull(id)) {\r\n            log.error(\"代理申请id不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_application_id_cannot_be_empty\"));\r\n        }\r\n        List<AppAgentContactPersonAddDto> appAgentContactPersonAddVos = appAgentRenewalUpdateDto\r\n                .getAppAgentContactPersonAddVos();\r\n        // 校验新版联系人数据\r\n        this.validateNewAppAgentContractPersonList(appAgentContactPersonAddVos);\r\n\r\n        // 更新代理申请数据\r\n        AppAgent appAgent = this.getById(id);\r\n        if (ObjectUtils.isNull(appAgent)) {\r\n            log.error(\"代理申请数据有误\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_app_data_error\"));\r\n        }\r\n        appAgent.setNatureNote(appAgentRenewalUpdateDto.getNatureNote());\r\n        appAgent.setContractStartTime(appAgentRenewalUpdateDto.getContractStartTime());\r\n        appAgent.setContractEndTime(appAgentRenewalUpdateDto.getContractEndTime());\r\n        appAgent.setChangeStatement(appAgentRenewalUpdateDto.getChangeStatement());\r\n        appAgent.setGmtModified(new Date());\r\n        appAgent.setGmtModifiedUser(SecureUtil.getLoginId());\r\n\r\n        boolean updateResult = this.updateById(appAgent);\r\n        if (!updateResult) {\r\n            log.error(\"更新失败\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n        // 2.保存联系人\r\n        AddAppAgentContext addAppAgentContext = createAddAppAgentContext(appAgent, appAgentRenewalUpdateDto);\r\n        doSaveAppAgentContactPerson(addAppAgentContext);\r\n\r\n        // 4.发送邮件 添加提醒任务\r\n        addReminderTask(addAppAgentContext);\r\n    }\r\n\r\n    /**\r\n     * 续约审核通过修改\r\n     *\r\n     * @param appAgentAddDto\r\n     */\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public void renewalAgreeUpdate(AppAgentAddDto appAgentAddDto) {\r\n        log.info(\"开始处理续约审批同意，参数: {}\", appAgentAddDto);\r\n\r\n        // 参数校验\r\n        if (ObjectUtils.isNull(appAgentAddDto)) {\r\n            log.error(\"续约审批数据不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_vo_null\"));\r\n        }\r\n        Long appAgentId = appAgentAddDto.getId();\r\n        if (ObjectUtils.isNull(appAgentId)) {\r\n            log.error(\"代理申请ID不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        Long fkAgentId = appAgentAddDto.getFkAgentId();\r\n        if (ObjectUtils.isNull(fkAgentId)) {\r\n            log.error(\"关联代理ID不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_id_null\"));\r\n        }\r\n\r\n        // 获取现有的AppAgent记录\r\n        AppAgent existingAppAgent = this.getById(appAgentId);\r\n        if (ObjectUtils.isNull(existingAppAgent)) {\r\n            log.error(\"代理申请数据不存在，ID: {}\", appAgentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_app_data_error\"));\r\n        }\r\n\r\n        // 校验是否为续约申请\r\n        if (!AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(existingAppAgent.getAppType())) {\r\n            log.error(\"不是续约申请类型，无法进行续约审批，AppType: {}\", existingAppAgent.getAppType());\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"not_renewal_application\"));\r\n        }\r\n\r\n        // ====== 第一步：更新AppAgent及其关联数据 ======\r\n        updateAppAgentAndRelatedData(appAgentAddDto, existingAppAgent);\r\n\r\n        // ====== 第二步：调用原来的doSetAgree方法处理审批同意 ======\r\n        AppAgentVo appAgentVo = BeanCopyUtils.objClone(existingAppAgent, AppAgentVo::new);\r\n        doSetAgree(appAgentVo);\r\n\r\n        log.info(\"续约审批同意处理完成，AppAgentId: {}, AgentId: {}\", appAgentId, fkAgentId);\r\n    }\r\n\r\n    /**\r\n     * 第一步：更新AppAgent及其关联数据\r\n     */\r\n    private void updateAppAgentAndRelatedData(AppAgentAddDto appAgentAddDto, AppAgent existingAppAgent) {\r\n        log.info(\"开始更新AppAgent及其关联数据，AppAgentId: {}\", existingAppAgent.getId());\r\n\r\n        // 1. 更新AppAgent主记录\r\n        updateAppAgentMainRecord(appAgentAddDto, existingAppAgent);\r\n\r\n        // 2. 更新联系人数据（采用有则更新，无则新增策略）\r\n        updateAppAgentContactPersons(appAgentAddDto);\r\n\r\n        // 3. 更新合同账户数据（采用有则更新，无则新增策略）\r\n        updateAppAgentContractAccounts(appAgentAddDto);\r\n\r\n        log.info(\"AppAgent及其关联数据更新完成\");\r\n    }\r\n\r\n    /**\r\n     * 更新AppAgent主记录\r\n     */\r\n    private void updateAppAgentMainRecord(AppAgentAddDto appAgentAddDto, AppAgent existingAppAgent) {\r\n        // 使用现有数据更新，保留原有的关键字段\r\n        BeanCopyUtils.copyProperties(appAgentAddDto, existingAppAgent,\r\n                \"id\", \"appType\", \"fkAgentId\", \"appStatus\", \"gmtCreate\", \"gmtCreateUser\");\r\n\r\n        utilService.setUpdateInfo(existingAppAgent);\r\n        boolean result = this.updateById(existingAppAgent);\r\n        if (!result) {\r\n            log.error(\"更新AppAgent主记录失败，ID: {}\", existingAppAgent.getId());\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n        log.info(\"AppAgent主记录更新成功\");\r\n    }\r\n\r\n    /**\r\n     * 更新AppAgent联系人数据（有则更新，无则新增）- 优化批量操作\r\n     */\r\n    private void updateAppAgentContactPersons(AppAgentAddDto appAgentAddDto) {\r\n        List<AppAgentContactPersonAddDto> contactPersonDtos = appAgentAddDto.getAppAgentContactPersonAddVos();\r\n        if (CollectionUtil.isEmpty(contactPersonDtos)) {\r\n            log.warn(\"没有联系人数据需要更新\");\r\n            return;\r\n        }\r\n\r\n        // 分组处理：有ID的更新，无ID的新增\r\n        List<AppAgentContactPersonAddDto> toUpdateDtos = new ArrayList<>();\r\n        List<AppAgentContactPersonAddDto> toInsertDtos = new ArrayList<>();\r\n\r\n        for (AppAgentContactPersonAddDto dto : contactPersonDtos) {\r\n            if (GeneralTool.isNotEmpty(dto.getId())) {\r\n                toUpdateDtos.add(dto);\r\n            } else {\r\n                toInsertDtos.add(dto);\r\n            }\r\n        }\r\n\r\n        // 批量更新现有记录\r\n        if (CollectionUtil.isNotEmpty(toUpdateDtos)) {\r\n            List<Long> updateIds = toUpdateDtos.stream().map(AppAgentContactPersonAddDto::getId)\r\n                    .collect(Collectors.toList());\r\n            List<AppAgentContactPerson> existingContactPersons = appAgentContactPersonService.listByIds(updateIds);\r\n            Map<Long, AppAgentContactPerson> existingMap = existingContactPersons.stream()\r\n                    .collect(Collectors.toMap(AppAgentContactPerson::getId, Function.identity()));\r\n\r\n            List<AppAgentContactPerson> updateList = new ArrayList<>();\r\n            for (AppAgentContactPersonAddDto dto : toUpdateDtos) {\r\n                AppAgentContactPerson existing = existingMap.get(dto.getId());\r\n                if (existing != null) {\r\n                    BeanCopyUtils.copyProperties(dto, existing, \"id\", \"fkAppAgentId\");\r\n                    utilService.setUpdateInfo(existing);\r\n                    updateList.add(existing);\r\n                }\r\n            }\r\n\r\n            if (CollectionUtil.isNotEmpty(updateList)) {\r\n                boolean result = appAgentContactPersonService.updateBatchById(updateList);\r\n                if (!result) {\r\n                    log.error(\"批量更新联系人失败\");\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n                }\r\n                log.debug(\"批量更新联系人成功，数量: {}\", updateList.size());\r\n            }\r\n        }\r\n\r\n        // 批量新增记录\r\n        if (CollectionUtil.isNotEmpty(toInsertDtos)) {\r\n            List<AppAgentContactPerson> insertList = new ArrayList<>();\r\n            for (AppAgentContactPersonAddDto dto : toInsertDtos) {\r\n                AppAgentContactPerson newContactPerson = BeanCopyUtils.objClone(dto, AppAgentContactPerson::new);\r\n                newContactPerson.setFkAppAgentId(appAgentAddDto.getId());\r\n                utilService.setCreateInfo(newContactPerson);\r\n                insertList.add(newContactPerson);\r\n            }\r\n\r\n            boolean result = appAgentContactPersonService.saveBatch(insertList);\r\n            if (!result) {\r\n                log.error(\"批量新增联系人失败\");\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n            log.debug(\"批量新增联系人成功，数量: {}\", insertList.size());\r\n        }\r\n\r\n        log.info(\"AppAgent联系人数据更新完成，更新: {}, 新增: {}\", toUpdateDtos.size(), toInsertDtos.size());\r\n    }\r\n\r\n    /**\r\n     * 更新AppAgent合同账户数据（有则更新，无则新增）- 优化批量操作\r\n     */\r\n    private void updateAppAgentContractAccounts(AppAgentAddDto appAgentAddDto) {\r\n        List<AppAgentContractAccountAddDto> contractAccountDtos = appAgentAddDto.getAppAgentContractAccountAddVos();\r\n        if (CollectionUtil.isEmpty(contractAccountDtos)) {\r\n            log.warn(\"没有合同账户数据需要更新\");\r\n            return;\r\n        }\r\n\r\n        // 分组处理：有ID的更新，无ID的新增\r\n        List<AppAgentContractAccountAddDto> toUpdateDtos = new ArrayList<>();\r\n        List<AppAgentContractAccountAddDto> toInsertDtos = new ArrayList<>();\r\n\r\n        for (AppAgentContractAccountAddDto dto : contractAccountDtos) {\r\n            if (GeneralTool.isNotEmpty(dto.getId())) {\r\n                toUpdateDtos.add(dto);\r\n            } else {\r\n                toInsertDtos.add(dto);\r\n            }\r\n        }\r\n\r\n        // 批量更新现有记录\r\n        if (CollectionUtil.isNotEmpty(toUpdateDtos)) {\r\n            List<Long> updateIds = toUpdateDtos.stream().map(AppAgentContractAccountAddDto::getId)\r\n                    .collect(Collectors.toList());\r\n            List<AppAgentContractAccount> existingContractAccounts = appAgentContractAccountService\r\n                    .listByIds(updateIds);\r\n            Map<Long, AppAgentContractAccount> existingMap = existingContractAccounts.stream()\r\n                    .collect(Collectors.toMap(AppAgentContractAccount::getId, Function.identity()));\r\n\r\n            List<AppAgentContractAccount> updateList = new ArrayList<>();\r\n            for (AppAgentContractAccountAddDto dto : toUpdateDtos) {\r\n                AppAgentContractAccount existing = existingMap.get(dto.getId());\r\n                if (existing != null) {\r\n                    BeanCopyUtils.copyProperties(dto, existing, \"id\", \"fkAppAgentId\");\r\n                    utilService.setUpdateInfo(existing);\r\n                    updateList.add(existing);\r\n                }\r\n            }\r\n\r\n            if (CollectionUtil.isNotEmpty(updateList)) {\r\n                boolean result = appAgentContractAccountService.updateBatchById(updateList);\r\n                if (!result) {\r\n                    log.error(\"批量更新合同账户失败\");\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n                }\r\n                log.debug(\"批量更新合同账户成功，数量: {}\", updateList.size());\r\n            }\r\n        }\r\n\r\n        // 批量新增记录\r\n        if (CollectionUtil.isNotEmpty(toInsertDtos)) {\r\n            List<AppAgentContractAccount> insertList = new ArrayList<>();\r\n            for (AppAgentContractAccountAddDto dto : toInsertDtos) {\r\n                AppAgentContractAccount newContractAccount = BeanCopyUtils.objClone(dto, AppAgentContractAccount::new);\r\n                newContractAccount.setFkAppAgentId(appAgentAddDto.getId());\r\n                utilService.setCreateInfo(newContractAccount);\r\n                insertList.add(newContractAccount);\r\n            }\r\n\r\n            boolean result = appAgentContractAccountService.saveBatch(insertList);\r\n            if (!result) {\r\n                log.error(\"批量新增合同账户失败\");\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n            log.debug(\"批量新增合同账户成功，数量: {}\", insertList.size());\r\n        }\r\n\r\n        log.info(\"AppAgent合同账户数据更新完成，更新: {}, 新增: {}\", toUpdateDtos.size(), toInsertDtos.size());\r\n    }\r\n\r\n    /**\r\n     * 变更申请数据获取\r\n     *\r\n     * @param fkAppAgentId\r\n     * @return\r\n     */\r\n    @Override\r\n    public AppAgentChangeDataVo getChangeData(Long fkAppAgentId) {\r\n        if (ObjectUtils.isNull(fkAppAgentId)) {\r\n            log.error(\"代理申请id数据不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        AppAgent appAgent = this.getById(fkAppAgentId);\r\n        if (ObjectUtils.isNull(appAgent)) {\r\n            log.error(\"代理数据有误\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_not_exist\"));\r\n        }\r\n        Long fkAgentId = appAgent.getFkAgentId();\r\n        if (ObjectUtils.isNull(fkAgentId)) {\r\n            log.error(\"代理管理id为空, 数据有误\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_id_null\"));\r\n        }\r\n        AppAgentChangeDataVo appAgentChangeDataVo = new AppAgentChangeDataVo();\r\n        appAgentChangeDataVo.setNatureNote(appAgent.getNatureNote());\r\n        appAgentChangeDataVo.setAppStatus(appAgent.getAppStatus());\r\n        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()\r\n                .eq(AgentContract::getFkAgentId, fkAgentId)\r\n                .eq(AgentContract::getIsActive, true)\r\n                .orderByDesc(AgentContract::getGmtCreate)\r\n                .last(\"LIMIT 1\");\r\n        AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);\r\n        if (agentContract == null) {\r\n            return appAgentChangeDataVo;\r\n        }\r\n        // 合同编号\r\n        appAgentChangeDataVo.setContractNum(agentContract.getContractNum());\r\n        return appAgentChangeDataVo;\r\n    }\r\n\r\n    /**\r\n     * 变更申请数据修改\r\n     *\r\n     * @param appAgentChangeDataDto\r\n     */\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public void changeData(AppAgentChangeDataDto appAgentChangeDataDto) {\r\n        if (ObjectUtils.isNull(appAgentChangeDataDto)) {\r\n            log.error(\"数据为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        Long appAgentId = appAgentChangeDataDto.getId();\r\n        if (ObjectUtils.isNull(appAgentId)) {\r\n            log.error(\"数据为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        AppAgent appAgent = this.getById(appAgentId);\r\n        if (ObjectUtils.isNull(appAgent)) {\r\n            log.error(\"数据有误\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_not_exist\"));\r\n        }\r\n        appAgent.setChangeStatement(appAgentChangeDataDto.getChangeStatement());\r\n        boolean result = this.updateById(appAgent);\r\n        if (!result) {\r\n            log.error(\"更新失败\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取代理变更申请信息导出为Pdf\r\n     *\r\n     * @param appAgentDto\r\n     * @param response\r\n     */\r\n    @Override\r\n    public void getLetterForAgentInformationChangePdf(AppAgentDto appAgentDto, HttpServletResponse response) {\r\n        if (GeneralTool.isEmpty(appAgentDto)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"missing_required_configuration\"));\r\n        }\r\n        if (GeneralTool.isEmpty(appAgentDto.getChangeStatement())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"missing_required_configuration\"));\r\n        }\r\n        String changeStatement = appAgentDto.getChangeStatement();\r\n        changeStatement = changeStatement.replaceAll(\"\\\\\\\\n\", \"\\n\");\r\n        // 创建pdf模版\r\n        // 根据json内容填充pdf\r\n        // 导出pdf\r\n        try {\r\n            generatePdf(appAgentDto, response);\r\n        } catch (Exception e) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"file_export_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 过滤续约同意合同账户数据\r\n     */\r\n    private void filterRenewalAgreeContractAccount(AppAgentAddDto appAgentAddDto) {\r\n        Long appAgentAddDtoId = appAgentAddDto.getId();\r\n        if (appAgentAddDtoId == null) {\r\n            return;\r\n        }\r\n        List<AppAgentContractAccountAddDto> appAgentContractAccountAddVos = appAgentAddDto\r\n                .getAppAgentContractAccountAddVos();\r\n        if (CollectionUtil.isEmpty(appAgentContractAccountAddVos)) {\r\n            return;\r\n        }\r\n        List<Long> idList = appAgentContractAccountAddVos.stream()\r\n                .map(AppAgentContractAccountAddDto::getId)\r\n                .collect(Collectors.toList());\r\n        if (CollectionUtil.isEmpty(idList)) {\r\n            return;\r\n        }\r\n        List<AppAgent> appAgents = this.appAgentMapper.selectBatchIds(idList);\r\n        if (CollectionUtil.isEmpty(appAgents)) {\r\n            return;\r\n        }\r\n\r\n        List<AppAgentContractAccountAddDto> appAgentFilter = appAgentContractAccountAddVos.stream()\r\n                .filter(appAgentContractAccountAddDto -> appAgentContractAccountAddDto.getId() == null\r\n                        && !appAgentAddDtoId.equals(appAgentContractAccountAddDto.getId()))\r\n                .collect(Collectors.toList());\r\n        if (CollectionUtil.isNotEmpty(appAgentFilter)) {\r\n            appAgentAddDto.setAppAgentContractAccountAddVos(appAgentFilter);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 代理申请表单新增\r\n     *\r\n     * @param appAgentAddDto\r\n     * @return\r\n     */\r\n    @Transactional(rollbackFor = Exception.class)\r\n    @Override\r\n    public String addAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        AddAppAgentContext addAppAgentContext = saveOrUpdateAppAgent(appAgentAddDto);\r\n\r\n        if (!AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {\r\n            // 发送旧版邮件\r\n            addReminderTask(addAppAgentContext);\r\n        } else {\r\n            // 发送新版代理申请提交邮件\r\n            sendApplicationSubmittedEmails(addAppAgentContext);\r\n        }\r\n\r\n        String encrypt = null;\r\n        try {\r\n            encrypt = AESUtils.Encrypt(String.valueOf(addAppAgentContext.getFkAppAgentId()), AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n        }\r\n        return encrypt;\r\n    }\r\n\r\n    /**\r\n     * 保存或更新代理管理数据\r\n     *\r\n     * @param appAgentAddDto\r\n     * @return\r\n     */\r\n    private AddAppAgentContext saveOrUpdateAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        // 代理申请数据校验\r\n        this.validateAppAgent(appAgentAddDto);\r\n\r\n        // 1.保存代理申请\r\n        AddAppAgentContext addAppAgentContext = doSaveAppAgent(appAgentAddDto);\r\n        // 2.保存联系人\r\n        doSaveAppAgentContactPerson(addAppAgentContext);\r\n        // 3.保存合同账户\r\n        doSaveAppAgentContractAccount(addAppAgentContext);\r\n        return addAppAgentContext;\r\n    }\r\n\r\n    /**\r\n     * 创建AddAppAgentContext对象用于提醒任务\r\n     *\r\n     * @param appAgent                 代理申请实体\r\n     * @param appAgentRenewalUpdateDto 续签更新DTO\r\n     * @return AddAppAgentContext对象\r\n     */\r\n    private AddAppAgentContext createAddAppAgentContext(AppAgent appAgent,\r\n                                                        AppAgentRenewalUpdateDto appAgentRenewalUpdateDto) {\r\n        // 创建AppAgentAddDto对象\r\n        AppAgentAddDto appAgentAddDto = new AppAgentAddDto();\r\n        appAgentAddDto.setId(appAgent.getId());\r\n        appAgentAddDto.setFkCompanyId(appAgent.getFkCompanyId());\r\n        appAgentAddDto.setFkStaffId(appAgent.getFkStaffId());\r\n        appAgentAddDto.setName(appAgent.getName());\r\n        appAgentAddDto.setFkAreaCountryId(appAgent.getFkAreaCountryId());\r\n        appAgentAddDto.setFkAreaStateId(appAgent.getFkAreaStateId());\r\n        appAgentAddDto.setFkAreaCityId(appAgent.getFkAreaCityId());\r\n        appAgentAddDto.setAppAgentContactPersonAddVos(appAgentRenewalUpdateDto.getAppAgentContactPersonAddVos());\r\n\r\n        // 创建AddAppAgentContext对象\r\n        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();\r\n        addAppAgentContext.setFkAppAgentId(appAgent.getId());\r\n        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);\r\n\r\n        return addAppAgentContext;\r\n    }\r\n\r\n    /**\r\n     * 添加提醒任务\r\n     *\r\n     * @param addAppAgentContext\r\n     */\r\n    private void addReminderTask(AddAppAgentContext addAppAgentContext) {\r\n        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();\r\n        Long fkAppAgentId = addAppAgentContext.getFkAppAgentId();\r\n        // 获取中英文配置\r\n        Map<Long, String> versionConfigMap = permissionCenterClient\r\n                .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();\r\n        String versionValue2 = versionConfigMap.get(appAgentAddDto.getFkCompanyId());\r\n        Set<Long> staffIds = new HashSet<>();\r\n        staffIds.add(appAgentAddDto.getFkStaffId());\r\n        Map<Long, String> map1 = permissionCenterClient.getStaffEnNameByIds(staffIds);\r\n        String nameEn = map1.get(appAgentAddDto.getFkStaffId());\r\n        if (GeneralTool.isEmpty(appAgentAddDto.getId())) {\r\n            StringBuilder taskTitle = null;\r\n            String taskRemark = null;\r\n            // 加提醒任务\r\n            Map<String, String> map = new HashMap<>();\r\n            if (versionValue2.equals(\"en\")) {\r\n                taskTitle = new StringBuilder(\"Proxy online application\");\r\n                map.put(\"staffName\", nameEn);\r\n                map.put(\"other\", \"，The online application form has been submitted, please refer to it.\");\r\n                map.put(\"agentName\", appAgentAddDto.getName());\r\n                taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER_ENGLISH);\r\n            } else {\r\n                taskTitle = new StringBuilder(\"代理在线申请\");\r\n                String staffName = permissionCenterClient.getStaffName(appAgentAddDto.getFkStaffId()).getData();\r\n                map.put(\"staffName\", staffName);\r\n                map.put(\"other\", \"，已经提交在线申请表单，请查阅。\");\r\n                map.put(\"agentName\", appAgentAddDto.getName());\r\n                taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER);\r\n            }\r\n            taskTitle.append(\"（\").append(appAgentAddDto.getName()).append(\"）\");\r\n\r\n            RemindTaskDto remindTaskVo = new RemindTaskDto();\r\n            remindTaskVo.setTaskTitle(taskTitle.toString());\r\n            remindTaskVo.setTaskRemark(taskRemark);\r\n            // 邮件方式发送\r\n            remindTaskVo.setRemindMethod(\"1\");\r\n            // 默认设置执行中\r\n            remindTaskVo.setStatus(1);\r\n            // 默认背景颜色\r\n            remindTaskVo.setTaskBgColor(\"#3788d8\");\r\n            remindTaskVo.setFkStaffId(appAgentAddDto.getFkStaffId());\r\n            remindTaskVo.setStartTime(new Date());\r\n            remindTaskVo.setFkTableName(TableEnum.APP_AGENT.key);\r\n            remindTaskVo.setFkTableId(fkAppAgentId);\r\n            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);\r\n            if (versionValue2.equals(\"en\")) {\r\n                remindTaskVo.setLanguageCode(\"en\");\r\n            }\r\n            Result<Boolean> result = reminderCenterClient.batchAdd(Lists.newArrayList(remindTaskVo));\r\n            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"news_emil_send_fail\"));\r\n            }\r\n        }\r\n\r\n        List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = addAppAgentContext.getAppAgentAddVo()\r\n                .getAppAgentContactPersonAddVos();\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentContactPersonAddDtos)) {\r\n            if (GeneralTool.isNotEmpty(appAgentContactPersonAddDtos.get(0))\r\n                    && GeneralTool.isNotEmpty(appAgentContactPersonAddDtos.get(0).getEmail())) {\r\n                Map<String, String> params = Maps.newHashMap();\r\n                params.put(\"email\", appAgentContactPersonAddDtos.get(0).getEmail());\r\n                params.put(\"taskRemark\",\r\n                        getTaskRemarkToContactPerson(fkAppAgentId, appAgentAddDto.getFkCompanyId(), versionValue2));\r\n                if (versionValue2.equals(\"en\")) {\r\n                    params.put(\"title\", \"Online form successfully submitted\");\r\n                    params.put(\"taskTitle\", \"Dear Partner, Hello！\");\r\n                    reminderCenterClient.batchSendEnEmail(Lists.newArrayList(params),\r\n                            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key, versionValue2);\r\n                } else {\r\n                    params.put(\"title\", \"在线表单成功提交\");\r\n                    params.put(\"taskTitle\", \"尊敬的合作方，您好！\");\r\n                    reminderCenterClient.batchSendEmail(Lists.newArrayList(params),\r\n                            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key);\r\n                }\r\n\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 提醒内容构建\r\n     *\r\n     * @param fkAppAgentId\r\n     * @return\r\n     */\r\n    private String getTaskRemarkToContactPerson(Long fkAppAgentId, Long fkCompanyId, String versionValue2) {\r\n        // String link =\r\n        // ApplyAgentOnlineFormUtils.getAppAgentFormDetailLink(fkAppAgentId,\r\n        // fkCompanyId);\r\n        String encrypt = null;\r\n        try {\r\n            encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n        }\r\n        String link = \"\";\r\n        // 根据公司获取域名\r\n        // Map<Long, String> companyConfigMap =\r\n        // permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key,\r\n        // 3).getData();\r\n        // String configValue2 = companyConfigMap.get(fkCompanyId);\r\n        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();\r\n        String configValue3 = configVo.getValue3();\r\n\r\n        String taskRemark = null;\r\n        link = configValue3 + \"/apply-agent-online-form/?sign=\" + encrypt;\r\n        System.out.println(\"=============\" + link);\r\n        if (versionValue2.equals(\"zh\")) {\r\n            taskRemark = \"<div class=\\\"desc\\\">\\n\" +\r\n                    \"    <div>您已经使用在线表单成功提交了合作申请。在人工审核前，若内容有所变动或需要补充，您可以点击或复制以下链接在浏览器重新打开申请资料进行修改：</div>\\n\" +\r\n                    \"    <a style='display:inline-block;margin-top:0' href = \\\"\" + link + \"\\\">\" + link + \"</a>\\n\" +\r\n                    \"    <div>谢谢您的申请，我们会尽快审核，期待与您的合作。</div>\\n\" +\r\n                    \"</div>\";\r\n        } else {\r\n\r\n            taskRemark = Base64.getEncoder().encodeToString((\"<div class=\\\"desc\\\">\\n\" +\r\n                    \"    <div>You have successfully submitted a partnership application using the online form. Before manual review, if the content has changed or needs to be supplemented, you can click or copy the following link to reopen the application in your browser for modification: </div>\\n\"\r\n                    +\r\n                    \"    <a style='display:inline-block;margin-top:0' href = \\\"\" + link + \"\\\">\" + link + \"</a>\\n\" +\r\n                    \"    <div>Thank you for your application, we will review it as soon as possible, and look forward to working with you.</div>\\n\"\r\n                    +\r\n                    \"</div>\").getBytes(StandardCharsets.UTF_8));\r\n\r\n        }\r\n\r\n        return taskRemark;\r\n\r\n    }\r\n\r\n    /**\r\n     * 列表\r\n     *\r\n     * @param appAgentListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<AppAgentListVo> getAppAgents(AppAgentListDto appAgentListDto, Page page) {\r\n        // 查询列表数据\r\n        List<AppAgentListVo> appAgentListVos = doGetAppAgentListDtos(appAgentListDto, page);\r\n\r\n        // 设置属性\r\n        doSetAppAgentListDtoName(appAgentListVos);\r\n\r\n        return appAgentListVos;\r\n    }\r\n\r\n    /**\r\n     * 详情\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    @Override\r\n    public AppAgentVo findAppAgentById(Long id) {\r\n        // 查询详情\r\n        AppAgentVo appAgentVo = doGetAppAgentDto(id);\r\n\r\n        // 设置属性\r\n        doSetAppAgentDto(appAgentVo);\r\n\r\n        return appAgentVo;\r\n    }\r\n\r\n    /**\r\n     * 编辑\r\n     *\r\n     * @param appAgentUpdateDto\r\n     * @return\r\n     */\r\n    @Transactional(rollbackFor = Exception.class)\r\n    @Override\r\n    public AppAgentVo updateAppAgent(AppAgentUpdateDto appAgentUpdateDto) {\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentUpdateDto, AppAgent::new);\r\n        utilService.setUpdateInfo(appAgent);\r\n        int i = appAgentMapper.updateById(appAgent);\r\n        if (i != 1) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n        // 保存附件\r\n        doSaveMediaAndAttacheds(appAgentUpdateDto, appAgent);\r\n\r\n        return findAppAgentById(appAgent.getId());\r\n    }\r\n\r\n    /**\r\n     * 保存附件\r\n     *\r\n     * @param appAgentUpdateDto\r\n     * @param appAgent\r\n     */\r\n    private void doSaveMediaAndAttacheds(AppAgentUpdateDto appAgentUpdateDto, AppAgent appAgent) {\r\n        String nature = appAgentUpdateDto.getNature();\r\n        List<MediaAndAttachedDto> mediaAttachedVos = Lists.newArrayList();\r\n        if (\"1\".equals(nature)) {\r\n            MediaAndAttachedDto mediaAndAttachedDto = appAgentUpdateDto.getMediaAndAttachedVo();\r\n            if (mediaAndAttachedDto != null) {\r\n                mediaAndAttachedDto.setFkTableId(appAgent.getId());\r\n                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n                mediaAndAttachedDto.setTypeKey(TableEnum.BUSINESS_LICENSE.key);\r\n                mediaAttachedVos.add(mediaAndAttachedDto);\r\n            }\r\n        }\r\n        List<AgentIdCardDto> cardVo = appAgentUpdateDto.getAgentIdCardVos();\r\n        if (GeneralTool.isNotEmpty(cardVo)) {\r\n            for (AgentIdCardDto idCardVo : cardVo) {\r\n                Integer type = idCardVo.getType();\r\n                String value = ProjectExtraEnum.getInitialValueByKey(type, ProjectExtraEnum.idCartFB);\r\n                if (StringUtils.isBlank(value)) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"ILLEGAL_FILE_TYPE\"));\r\n                }\r\n                MediaAndAttachedDto attachedVo = idCardVo.getMediaAndAttachedVo();\r\n                attachedVo.setFkTableId(appAgentUpdateDto.getId());\r\n                attachedVo.setFkTableName(TableEnum.APP_AGENT.key);\r\n                if (type == 0) {\r\n                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key);\r\n                } else {\r\n                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key);\r\n                }\r\n                mediaAttachedVos.add(attachedVo);\r\n            }\r\n        }\r\n        if (GeneralTool.isNotEmpty(mediaAttachedVos)) {\r\n            Boolean aBoolean = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAttachedVos);\r\n            if (!aBoolean) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 联系人列表\r\n     *\r\n     * @param appAgentContactPersonListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<AppAgentContactPersonListVo> getAppAgentContactPersons(\r\n            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page) {\r\n        // 根据条件查询列表数据\r\n        List<AppAgentContactPersonListVo> appAgentContactPersonListVos = doGetAppAgentContactPersonListDtos(\r\n                appAgentContactPersonListDto, page);\r\n\r\n        // 设置属性\r\n        doSetAppAgentContactPersonListDto(appAgentContactPersonListVos);\r\n\r\n        return appAgentContactPersonListVos;\r\n    }\r\n\r\n    /**\r\n     * 合同账户列表\r\n     *\r\n     * @param appAgentContractAccountListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<AppAgentContractAccountListVo> getAppAgentContractAccounts(\r\n            AppAgentContractAccountListDto appAgentContractAccountListDto, Page page) {\r\n        // 列表数据\r\n        List<AppAgentContractAccountListVo> appAgentContractAccountListVos = doGetAppAgentContractAccountListDtos(\r\n                appAgentContractAccountListDto, page);\r\n\r\n        // 设置属性\r\n        doSetAppAgentContractAccountListDtos(appAgentContractAccountListVos);\r\n\r\n        return appAgentContractAccountListVos;\r\n    }\r\n\r\n    /**\r\n     * 附件列表\r\n     *\r\n     * @param mediaAndAttachedDto\r\n     * @param voSearchBean\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<MediaAndAttachedVo> getAppAgentMedia(MediaAndAttachedDto mediaAndAttachedDto, Page voSearchBean) {\r\n        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n        return mediaAndAttachedService.getMediaAndAttachedDto(mediaAndAttachedDto);\r\n    }\r\n\r\n    /**\r\n     * 添加附件\r\n     *\r\n     * @param mediaAttachedVos\r\n     * @return\r\n     */\r\n    @Transactional(rollbackFor = Exception.class)\r\n    @Override\r\n    public List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVos) {\r\n        if (GeneralTool.isEmpty(mediaAttachedVos)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"upload_vo_null\"));\r\n        }\r\n        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();\r\n        Long agentId = mediaAttachedVos.get(0).getFkTableId();\r\n        AppAgent appAgent = this.baseMapper.selectById(agentId);\r\n        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {\r\n            if (TableEnum.BUSINESS_LICENSE.key.equals(mediaAndAttachedDto.getTypeKey()) && appAgent != null\r\n                    && !\"1\".equals(appAgent.getNature())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"NON_COMPANY_AGENT_CANNOT_UPLOAD_LICENSE\"));\r\n            }\r\n            // 设置插入的表\r\n            mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n            mediaAndAttachedVos.add(mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto));\r\n        }\r\n        return mediaAndAttachedVos;\r\n    }\r\n\r\n    /**\r\n     * 更新申请状态\r\n     *\r\n     * @param id\r\n     * @param appStatus\r\n     */\r\n    @Override\r\n    public void updateAppStatus(Long id, Integer appStatus) {\r\n        // 查询更新的实体\r\n        AppAgentVo appAgentVo = doGetAppAgentDto(id);\r\n\r\n        // 更新状态\r\n        doUpdateAppStatus(appAgentVo, appStatus);\r\n    }\r\n\r\n    /**\r\n     * 申请状态下拉\r\n     *\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<Map<String, Object>> getAppStatusSelect() {\r\n        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.APP_STATUS_TYPE);\r\n    }\r\n\r\n    /**\r\n     * 验证联系人和代理账户\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    @Override\r\n    public String validateAgentContactPersonAndAccount(Long id) {\r\n        Set<String> mobiles = Sets.newHashSet();\r\n        Set<String> mails = Sets.newHashSet();\r\n        Set<String> accountNames = Sets.newHashSet();\r\n        Set<String> accountNums = Sets.newHashSet();\r\n        List<AppAgentContactPerson> appAgentContactPeople = appAgentContactPersonService\r\n                .list(Wrappers.lambdaQuery(AppAgentContactPerson.class).eq(AppAgentContactPerson::getFkAppAgentId, id));\r\n        if (GeneralTool.isNotEmpty(appAgentContactPeople)) {\r\n            mobiles = appAgentContactPeople.stream().map(AppAgentContactPerson::getMobile).collect(Collectors.toSet());\r\n            mails = appAgentContactPeople.stream().map(AppAgentContactPerson::getEmail).collect(Collectors.toSet());\r\n        }\r\n\r\n        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService.list(\r\n                Wrappers.lambdaQuery(AppAgentContractAccount.class).eq(AppAgentContractAccount::getFkAppAgentId, id));\r\n        if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {\r\n            // 名称\r\n            accountNames = appAgentContractAccounts.stream().map(AppAgentContractAccount::getBankAccount)\r\n                    .collect(Collectors.toSet());\r\n            // 账户\r\n            accountNums = appAgentContractAccounts.stream().map(AppAgentContractAccount::getBankAccountNum)\r\n                    .collect(Collectors.toSet());\r\n        }\r\n\r\n        // contact_person_mobile=联系人移动电话\r\n        // contact_person_tel=联系人固话\r\n        // contact_person_email=联系人电邮\r\n        // bank_account_name=账户名称\r\n        // bank_account_num=账号\r\n        // STUDENT_EXIST\r\n        StringBuilder sb = new StringBuilder();\r\n        if (GeneralTool.isNotEmpty(mobiles)) {\r\n            List<SaleContactPerson> mobileList = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)\r\n                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)\r\n                    .in(SaleContactPerson::getMobile, mobiles));\r\n            if (GeneralTool.isNotEmpty(mobileList)) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                for (SaleContactPerson saleContactPerson : mobileList) {\r\n                    sj.add(saleContactPerson.getMobile());\r\n                }\r\n                sb.append(LocaleMessageUtils.getMessage(\"contact_person_mobile\")).append(\":\").append(sj.toString())\r\n                        .append(LocaleMessageUtils.getMessage(\"STUDENT_EXIST\")).append(\"；<br/>\");\r\n            }\r\n        }\r\n        if (GeneralTool.isNotEmpty(mobiles)) {\r\n            List<SaleContactPerson> tels = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)\r\n                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)\r\n                    .in(SaleContactPerson::getTel, mobiles));\r\n            if (GeneralTool.isNotEmpty(tels)) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                for (SaleContactPerson saleContactPerson : tels) {\r\n                    sj.add(saleContactPerson.getTel());\r\n                }\r\n                sb.append(LocaleMessageUtils.getMessage(\"contact_person_tel\")).append(\":\").append(sj.toString())\r\n                        .append(LocaleMessageUtils.getMessage(\"STUDENT_EXIST\")).append(\"；<br/>\");\r\n            }\r\n        }\r\n        if (GeneralTool.isNotEmpty(mails)) {\r\n            List<SaleContactPerson> mailList = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)\r\n                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)\r\n                    .in(SaleContactPerson::getEmail, mails));\r\n            if (GeneralTool.isNotEmpty(mailList)) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                for (SaleContactPerson saleContactPerson : mailList) {\r\n                    sj.add(saleContactPerson.getEmail());\r\n                }\r\n                sb.append(LocaleMessageUtils.getMessage(\"contact_person_email\")).append(\":\").append(sj.toString())\r\n                        .append(LocaleMessageUtils.getMessage(\"STUDENT_EXIST\")).append(\"；<br/>\");\r\n            }\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(accountNames)) {\r\n            List<AgentContractAccount> accountNameList = agentContractAccountService.list(Wrappers\r\n                    .lambdaQuery(AgentContractAccount.class).in(AgentContractAccount::getBankAccount, accountNames));\r\n            if (GeneralTool.isNotEmpty(accountNameList)) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                for (AgentContractAccount agentContractAccount : accountNameList) {\r\n                    sj.add(agentContractAccount.getBankAccount());\r\n                }\r\n                sb.append(LocaleMessageUtils.getMessage(\"bank_account_name\")).append(\":\").append(sj.toString())\r\n                        .append(LocaleMessageUtils.getMessage(\"STUDENT_EXIST\")).append(\"；<br/>\");\r\n            }\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(accountNums)) {\r\n            List<AgentContractAccount> accountNumList = agentContractAccountService.list(Wrappers\r\n                    .lambdaQuery(AgentContractAccount.class).in(AgentContractAccount::getBankAccountNum, accountNums));\r\n            if (GeneralTool.isNotEmpty(accountNumList)) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                for (AgentContractAccount agentContractAccount : accountNumList) {\r\n                    sj.add(agentContractAccount.getBankAccountNum());\r\n                }\r\n                sb.append(LocaleMessageUtils.getMessage(\"bank_account_num\")).append(\":\").append(sj.toString())\r\n                        .append(LocaleMessageUtils.getMessage(\"STUDENT_EXIST\")).append(\"；<br/>\");\r\n            }\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(sb)) {\r\n            return sb.toString();\r\n        }\r\n        return null;\r\n    }\r\n\r\n    @Override\r\n    public List<BaseSelectEntity> getBdStaffSelect(Long companyId) {\r\n        return appAgentMapper.getBdStaffSelect(companyId);\r\n    }\r\n\r\n    /**\r\n     * 表单配置\r\n     *\r\n     * @return\r\n     */\r\n    @Override\r\n    public JSONObject getAppAgentFormConfig() {\r\n        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.APPLY_AGENT_ONLINE_FORM_LIMIT.key)\r\n                .getData();\r\n        JSONObject jsonObject = JSONObject.parseObject(configVo.getValue1());\r\n        return jsonObject;\r\n    }\r\n\r\n    /**\r\n     * 代理申请表单回显\r\n     *\r\n     * @param sign\r\n     * @return\r\n     */\r\n    @Override\r\n    public AppAgentFormDetailVo getAppAgentFormDetail(String sign) {\r\n        // 转义+变成空格的情况\r\n        Long id = decodeId(sign);\r\n        AppAgentFormDetailVo appAgentFormDetailVo = getAppAgentFormDetailById(id);\r\n\r\n        return appAgentFormDetailVo;\r\n    }\r\n\r\n    /**\r\n     * 代理ID解密\r\n     *\r\n     * @param sign\r\n     * @return\r\n     */\r\n    private static Long decodeId(String sign) {\r\n        sign = sign.replaceAll(\" \", \"+\");\r\n        String decrypt = null;\r\n        try {\r\n            decrypt = AESUtils.Decrypt(sign, AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"SIGN_PARSE_FAILED\"));\r\n        }\r\n        Long id = Long.valueOf(decrypt);\r\n        return id;\r\n    }\r\n\r\n    /**\r\n     * 根据代理申请id获取表单数据\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    @Override\r\n    public AppAgentFormDetailVo getAppAgentFormDetailById(Long id) {\r\n        if (ObjectUtils.isNull(id)) {\r\n            return null;\r\n        }\r\n        AppAgent appAgent = appAgentMapper.selectById(id);\r\n        if (GeneralTool.isEmpty(appAgent)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_not_exist\"));\r\n        }\r\n\r\n        AppAgentFormDetailVo appAgentFormDetailVo = BeanCopyUtils.objClone(appAgent, AppAgentFormDetailVo::new);\r\n        assert appAgentFormDetailVo != null;\r\n\r\n        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key))) {\r\n            appAgentFormDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key);\r\n            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);\r\n        }\r\n        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key))) {\r\n            appAgentFormDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_COMPANY.key);\r\n            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);\r\n        }\r\n        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key))) {\r\n            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_PERSONAL.key);\r\n        }\r\n        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();\r\n        mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n        mediaAndAttachedDto.setFkTableId(appAgent.getId());\r\n        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedService\r\n                .getMediaAndAttachedDto(mediaAndAttachedDto);\r\n        if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {\r\n            List<MediaAndAttachedDto> mediaAndAttachedDtos = BeanCopyUtils.copyListProperties(mediaAndAttachedVos,\r\n                    MediaAndAttachedDto::new);\r\n            appAgentFormDetailVo.setMediaAndAttachedVos(mediaAndAttachedDtos);\r\n        }\r\n\r\n        List<AppAgentContactPerson> appAgentContactPersonList = appAgentContactPersonService\r\n                .list(Wrappers.lambdaQuery(AppAgentContactPerson.class)\r\n                        .eq(AppAgentContactPerson::getFkAppAgentId, appAgent.getId()));\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentContactPersonList)) {\r\n            List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = BeanCopyUtils\r\n                    .copyListProperties(appAgentContactPersonList, AppAgentContactPersonAddDto::new);\r\n            appAgentFormDetailVo.setAppAgentContactPersonAddVos(appAgentContactPersonAddDtos);\r\n        } else {\r\n            appAgentFormDetailVo.setAppAgentContactPersonAddVos(Collections.emptyList());\r\n        }\r\n\r\n        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService\r\n                .list(Wrappers.lambdaQuery(AppAgentContractAccount.class)\r\n                        .eq(AppAgentContractAccount::getFkAppAgentId, appAgent.getId()));\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {\r\n            List<AppAgentContractAccountAddDto> appAgentContractAccountAddDtos = BeanCopyUtils\r\n                    .copyListProperties(appAgentContractAccounts, AppAgentContractAccountAddDto::new);\r\n            if (GeneralTool.isNotEmpty(appAgentContractAccountAddDtos)) {\r\n                Set<Long> fkTableIds = appAgentContractAccountAddDtos.stream().map(AppAgentContractAccountAddDto::getId)\r\n                        .collect(Collectors.toSet());\r\n                Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService\r\n                        .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key, fkTableIds);\r\n                for (AppAgentContractAccountAddDto appAgentContractAccountAddDto : appAgentContractAccountAddDtos) {\r\n                    List<MediaAndAttachedVo> mediaAndAttachedVoList = mediaAndAttachedDtoByFkTableIds\r\n                            .get(appAgentContractAccountAddDto.getId());\r\n                    if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {\r\n                        appAgentContractAccountAddDto.setMediaAndAttachedVos(\r\n                                BeanCopyUtils.copyListProperties(mediaAndAttachedVoList, MediaAndAttachedDto::new));\r\n                    }\r\n                }\r\n            }\r\n            appAgentFormDetailVo.setAppAgentContractAccountAddVos(appAgentContractAccountAddDtos);\r\n        } else {\r\n            appAgentFormDetailVo.setAppAgentContractAccountAddVos(Collections.emptyList());\r\n        }\r\n\r\n        Properties props = System.getProperties();\r\n        String profile = props.getProperty(\"spring.profiles.active\");\r\n        if (GeneralTool.isNotEmpty(profile)) {\r\n            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)\r\n                    || profile.equals(AppConstant.TW_CODE)) {\r\n                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);\r\n            } else if (profile.equals(AppConstant.IAE_CODE)) {\r\n                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);// IAE环境下使用私密桶地址为IAE桶url\r\n            } else if (profile.equals(AppConstant.TEST_CODE)) {\r\n                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);\r\n            } else {\r\n                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);\r\n            }\r\n        }\r\n        Long fkAgentId = appAgent.getFkAgentId();\r\n        if (ObjectUtils.isNotNull(fkAgentId)) {\r\n            LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()\r\n                    .eq(AgentContract::getFkAgentId, fkAgentId)\r\n                    .eq(AgentContract::getIsActive, true)\r\n                    .orderByDesc(AgentContract::getGmtCreate)\r\n                    .last(\"LIMIT 1\");\r\n            AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);\r\n            if (ObjectUtils.isNotNull(agentContract)) {\r\n                appAgentFormDetailVo.setContractNum(agentContract.getContractNum());\r\n            }\r\n        }\r\n        if (StringUtils.isBlank(appAgentFormDetailVo.getPersonalName())) {\r\n            appAgentFormDetailVo.setPersonalName(appAgentFormDetailVo.getName());\r\n        }\r\n        return appAgentFormDetailVo;\r\n    }\r\n\r\n    /**\r\n     * 常用国家下拉框\r\n     *\r\n     * @return\r\n     */\r\n    @Override\r\n    public List<BaseSelectEntity> getCommonCountrySelect() {\r\n        List<AreaCountryVo> areaCountryVos = institutionCenterClient\r\n                .getCountryByPublicLevel(ProjectExtraEnum.PUBLIC_COUNTRY_COMMON.key);\r\n        if (GeneralTool.isEmpty(areaCountryVos)) {\r\n            return Collections.emptyList();\r\n        }\r\n        List<BaseSelectEntity> baseSelectEntities = BeanCopyUtils.copyListProperties(areaCountryVos,\r\n                BaseSelectEntity::new);\r\n        return baseSelectEntities;\r\n    }\r\n\r\n    @Override\r\n    public void getDownloadFilePrivate(HttpServletResponse response, FileVo fileVo) {\r\n        SaleFileDto saleFileDto = fileCenterClient.getDownloadFile(fileVo).getData();\r\n        BufferedOutputStream outputStream = null;\r\n        try {\r\n            outputStream = new BufferedOutputStream(response.getOutputStream());\r\n            response.setHeader(\"Content-Disposition\", \"attachment;filename=\"\r\n                    + URLEncoder.encode(new String(fileVo.getFileNameOrc().getBytes(\"utf-8\"), \"UTF-8\")));\r\n            byte[] bytes = null;\r\n            bytes = saleFileDto.getBytes();\r\n            outputStream.write(bytes);\r\n        } catch (IOException e) {\r\n            e.printStackTrace();\r\n        } finally {\r\n            try {\r\n                if (outputStream != null) {\r\n                    outputStream.flush();\r\n                    outputStream.close();\r\n                }\r\n            } catch (Exception e) {\r\n                e.printStackTrace();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 奖学金用户是否已申请\r\n     *\r\n     * @param userId\r\n     * @return\r\n     */\r\n    @Override\r\n    public Boolean validatedUser(Long userId) {\r\n        List<AppAgent> appAgents = appAgentMapper.selectList(Wrappers.lambdaQuery(AppAgent.class)\r\n                .eq(AppAgent::getGmtCreateUser, \"get_issue_\" + userId));\r\n        return GeneralTool.isNotEmpty(appAgents);\r\n    }\r\n\r\n    /**\r\n     * 更新申请状态\r\n     *\r\n     * @param appAgentVo\r\n     * @param appStatus\r\n     */\r\n    private void doUpdateAppStatus(AppAgentVo appAgentVo, Integer appStatus) {\r\n        if (GeneralTool.isEmpty(appStatus) || GeneralTool.isEmpty(appAgentVo)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        if (ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appStatus)) {\r\n            // 我要审核\r\n            doSetReview(appAgentVo);\r\n        }\r\n        if (ProjectExtraEnum.APP_STATUS_AGREE.key.equals(appStatus)) {\r\n            // 设置同意\r\n            doSetAgree(appAgentVo);\r\n        }\r\n        if (ProjectExtraEnum.APP_STATUS_REJECT.key.equals(appStatus)) {\r\n            // 设置拒绝\r\n            doSetReject(appAgentVo);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 审批同意\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    // @Transactional(rollbackFor = Exception.class)\r\n    // @DSTransactional\r\n    @Override\r\n    @GlobalTransactional\r\n    public void doSetAgree(AppAgentVo appAgentVo) {\r\n\r\n        // 代理查重\r\n        validateAgent(appAgentVo);\r\n\r\n        // 同步代理数据\r\n        AppAgentSetAgreeContext appAgentSetAgreeContext = doCopyAppAgentToAgent(appAgentVo);\r\n\r\n        // 同步代理绑定bd数据\r\n        doCopyAppAgentBdToAgentBd(appAgentSetAgreeContext);\r\n\r\n        // 生成合同\r\n        doCreateAgentContract(appAgentSetAgreeContext);\r\n\r\n        // 同步联系人数据\r\n        doCopyAppAgentContactPersonToAgentContactPerson(appAgentSetAgreeContext);\r\n\r\n        // 同步合同账户资料\r\n        doCopyAppAgentContractAccountToAgentContractAccount(appAgentSetAgreeContext);\r\n\r\n        // 保存合同联系人类型\r\n        // doCreateContractPerson(appAgentSetAgreeContext);\r\n\r\n        // 同步附件\r\n        doCopyMediaAndAttached(appAgentSetAgreeContext);\r\n\r\n        // 回填代理id\r\n        doUpdateAppAgentInfo(appAgentSetAgreeContext);\r\n\r\n        // 若是奖学金进来的，要在issue库新增关系表和更新issue学生代理id\r\n        // doUpdateIssueStudentAndAgent(appAgentSetAgreeContext);\r\n\r\n        // 注册伙伴中心用户\r\n        doRegisterPartnerUsers(appAgentSetAgreeContext);\r\n\r\n    }\r\n\r\n    /**\r\n     * 保存合同账户类型\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCreateContractAccount(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        List<AppAgentContractAccount> appAgentContractAccounts = appAgentSetAgreeContext.getAppAgentContractAccounts();\r\n        if (CollectionUtil.isEmpty(appAgentContractAccounts)) {\r\n            return;\r\n        }\r\n        // this.agentContractAgentAccountService.list(new\r\n        // LambdaQueryWrapper<AgentContractAgentAccount>().eq(AgentContractAgentAccount\r\n        // :: getFkAgentContractAccountId));\r\n\r\n        // appAgentContractAccounts.stream().map(appAgentContractAccount -> {\r\n        // AgentContractAgentAccount agentAccount = new AgentContractAgentAccount();\r\n        // })\r\n\r\n    }\r\n\r\n    /**\r\n     * 保存合同联系人类型\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCreateContractPerson(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        if (ObjectUtils.isEmpty(agentId)) {\r\n            return;\r\n        }\r\n        List<AppAgentContactPerson> appAgentContactPersons = appAgentSetAgreeContext.getAppAgentContactPersons();\r\n        if (CollectionUtil.isEmpty(appAgentContactPersons)) {\r\n            return;\r\n        }\r\n        List<SaleContactPerson> saleContactPersonList = appAgentContactPersons.stream()\r\n                .map(appAgentContactPerson -> BeanCopyUtils.objClone(appAgentContactPerson, SaleContactPerson::new))\r\n                .collect(Collectors.toList());\r\n        if (CollectionUtil.isEmpty(saleContactPersonList)) {\r\n            return;\r\n        }\r\n        for (SaleContactPerson saleContactPerson : saleContactPersonList) {\r\n            saleContactPerson.setFkTableName(TableEnum.SALE_AGENT.key);\r\n            // 代理管理id\r\n            saleContactPerson.setFkTableId(agentId);\r\n        }\r\n        // 保存合同联系人数据\r\n        boolean result = this.contactPersonService.saveBatch(saleContactPersonList);\r\n    }\r\n\r\n    /**\r\n     * 若是奖学金进来的，要在issue库新增关系表和更新issue学生代理id\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doUpdateIssueStudentAndAgent(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n\r\n        String gmtCreateUser = appAgentSetAgreeContext.getAppAgentDto().getGmtCreateUser();\r\n        if (!gmtCreateUser.startsWith(\"get_issue_\")) {\r\n            return;\r\n        }\r\n\r\n        String userIdStr = gmtCreateUser.substring(10);\r\n        Long userId;\r\n        try {\r\n            userId = Long.parseLong(userIdStr);\r\n        } catch (Exception e) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"illegal_type\"));\r\n        }\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        if (appAgentSetAgreeContext.getIsRenewal()) {\r\n            LambdaQueryWrapper<NewIssueUserAgent> newIssueUserAgentLambdaQueryWrapper = new LambdaQueryWrapper<NewIssueUserAgent>()\r\n                    .eq(NewIssueUserAgent::getFkAgentId, agentId);\r\n            List<NewIssueUserAgent> newIssueUserAgents = newIssueUserAgentMapper\r\n                    .selectList(newIssueUserAgentLambdaQueryWrapper);\r\n            if (CollectionUtil.isNotEmpty(newIssueUserAgents)) {\r\n                this.newIssueUserAgentMapper.delete(newIssueUserAgentLambdaQueryWrapper);\r\n            }\r\n        }\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n\r\n        NewIssueUserAgent newIssueUserAgent = new NewIssueUserAgent();\r\n        newIssueUserAgent.setFkCompanyId(appAgentVo.getFkCompanyId());\r\n        newIssueUserAgent.setFkAgentId(agentId);\r\n        newIssueUserAgent.setFkUserId(userId);\r\n        newIssueUserAgent.setRoleType(0);\r\n        newIssueUserAgent.setIsSubagentPermission(false);\r\n        newIssueUserAgent.setIsBmsPermission(true);\r\n        newIssueUserAgent.setIsShowAppStatusPermission(true);\r\n        utilService.setCreateInfo(newIssueUserAgent);\r\n        int i = newIssueUserAgentMapper.insert(newIssueUserAgent);\r\n        if (i <= 0) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n        }\r\n\r\n        User user = registrationCenterClient.getUserByAgentId(agentId).getData();\r\n        NewIssueUserSuperior newIssueUserSuperior = new NewIssueUserSuperior();\r\n        newIssueUserSuperior.setFkUserId(userId);\r\n        newIssueUserSuperior.setFkUserSuperiorId(user.getId());\r\n        newIssueUserSuperior.setFkCompanyId(appAgentSetAgreeContext.getFkCompanyId());\r\n        utilService.setCreateInfo(newIssueUserSuperior);\r\n        newIssueUserSuperiorMapper.insert(newIssueUserSuperior);\r\n\r\n        List<NewIssueStudent> newIssueStudents = newIssueStudentMapper\r\n                .selectList(Wrappers.lambdaQuery(NewIssueStudent.class)\r\n                        .eq(NewIssueStudent::getGmtCreateUserId, userId));\r\n\r\n        if (GeneralTool.isNotEmpty(newIssueStudents)) {\r\n            for (NewIssueStudent newIssueStudent : newIssueStudents) {\r\n                newIssueStudent.setFkAgentId(agentId);\r\n                utilService.setUpdateInfo(newIssueStudent);\r\n                newIssueStudentMapper.updateById(newIssueStudent);\r\n            }\r\n\r\n            List<Long> issueStudentIds = newIssueStudents.stream().map(NewIssueStudent::getId)\r\n                    .collect(Collectors.toList());\r\n            List<RStudentIssueStudent> rStudentIssueStudents = rStudentIssueStudentMapper\r\n                    .selectList(Wrappers.lambdaQuery(RStudentIssueStudent.class)\r\n                            .in(RStudentIssueStudent::getFkStudentIdIssue2, issueStudentIds));\r\n\r\n            if (GeneralTool.isNotEmpty(rStudentIssueStudents)) {\r\n                Set<Long> studentIds = rStudentIssueStudents.stream().map(RStudentIssueStudent::getFkStudentId)\r\n                        .collect(Collectors.toSet());\r\n                if (GeneralTool.isNotEmpty(studentIds)) {\r\n                    StudentAgentBindingDto studentAgentBindingDto = new StudentAgentBindingDto();\r\n                    studentAgentBindingDto.setFkAgentId(agentId);\r\n                    studentAgentBindingDto.setStudentOfferAgentId(agentId);\r\n                    studentAgentBindingDto.setAccommodationAgentId(agentId);\r\n                    studentAgentBindingDto.setInsuranceAgentId(agentId);\r\n                    studentAgentBindingDto.setFkStudentIds(studentIds);\r\n                    studentAgentBindingDto.setIsCancelOriginalBinding(true);\r\n                    if (GeneralTool.isNotEmpty(appAgentSetAgreeContext.getAppAgentContactPersons())) {\r\n                        AppAgentContactPerson appAgentContactPerson = appAgentSetAgreeContext\r\n                                .getAppAgentContactPersons().get(0);\r\n                        studentAgentBindingDto.setStudentOfferEmail(appAgentContactPerson.getEmail());\r\n                    }\r\n                    studentService.updateStudentAgentBinding(studentAgentBindingDto);\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 同步bd绑定数据\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCopyAppAgentBdToAgentBd(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long fkAgentId = appAgentSetAgreeContext.getAgentId();\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        if (GeneralTool.isEmpty(fkAgentId) && GeneralTool.isEmpty(appAgentVo)) {\r\n            return;\r\n        }\r\n        if (appAgentSetAgreeContext.getIsRenewal()) {\r\n            LambdaQueryWrapper<AgentStaff> staffLambdaQueryWrapper = new LambdaQueryWrapper<AgentStaff>()\r\n                    .eq(AgentStaff::getFkAgentId, fkAgentId);\r\n            List<AgentStaff> agentStaffs = this.agentStaffService.list(staffLambdaQueryWrapper);\r\n            if (CollectionUtil.isNotEmpty(agentStaffs)) {\r\n                List<Long> staffIds = agentStaffs.stream().map(AgentStaff::getId).collect(Collectors.toList());\r\n                this.agentStaffService.removeByIds(staffIds);\r\n            }\r\n        }\r\n\r\n        AgentStaff agentStaff = new AgentStaff();\r\n        agentStaff.setFkAgentId(fkAgentId);\r\n        agentStaff.setFkStaffId(appAgentVo.getFkStaffId());\r\n        agentStaff.setIsActive(true);\r\n        agentStaff.setActiveDate(new Date());\r\n        utilService.setCreateInfo(agentStaff);\r\n        int i = agentStaffService.getBaseMapper().insert(agentStaff);\r\n        if (i != 1) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 代理查重\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    private void validateAgent(AppAgentVo appAgentVo) {\r\n        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        // if (GeneralTool.isEmpty(appAgentVo.getEmail2())){\r\n        // throw new GetServiceException((\"en\".equals(SecureUtil.getLocale())?\"Business\r\n        // news receiving\r\n        // mail,\":\"业务新闻接收邮箱,\")+LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        // }\r\n        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appAgentVo.getAppStatus())) {\r\n            // 如果不为申请中状态了 则不能设置成同意\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n        if (!Objects.equals(appAgentVo.getAppStatusModifyUser(), SecureUtil.getLoginId())) {\r\n            // 登录人和申请审批的人不是同一个 则不能设置同意\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"NON_REVIEWER\"));\r\n        }\r\n        // 是否续约\r\n        Boolean isRenewal = AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(appAgentVo.getAppType())\r\n                && appAgentVo.getFkAgentId() != null;\r\n        // 代理申请id\r\n        Long fkAgentId = appAgentVo.getFkAgentId();\r\n\r\n        LambdaQueryWrapper<Agent> wrapper = Wrappers.<Agent>lambdaQuery();\r\n        if (GeneralTool.isEmpty(appAgentVo.getNature())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"nature_is_empty\"));\r\n        }\r\n        wrapper.eq(Agent::getNature, appAgentVo.getNature());\r\n        // 性质=个人 + 身份证（禁止提交）\r\n        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key).equals(appAgentVo.getNature())) {\r\n            if (GeneralTool.isEmpty(appAgentVo.getIdCardNum())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_card_num_is_empty\"));\r\n            }\r\n            wrapper.eq(Agent::getIdCardNum, appAgentVo.getIdCardNum());\r\n            List<Agent> agents = agentService.list(wrapper);\r\n            // 续签过滤自己\r\n            if (isRenewal) {\r\n                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());\r\n            }\r\n            if (GeneralTool.isNotEmpty(agents)) {\r\n                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());\r\n                List<AgentCompany> agentCompanies = agentCompanyService\r\n                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)\r\n                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));\r\n                if (GeneralTool.isNotEmpty(agentCompanies)) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_person_id_card_duplicate\"));\r\n                }\r\n            }\r\n        }\r\n        // 性质=公司 + 公司税号（禁止提交）\r\n        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key).equals(appAgentVo.getNature())) {\r\n            if (GeneralTool.isEmpty(appAgentVo.getTaxCode())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"tax_code_is_empty\"));\r\n            }\r\n            wrapper.eq(Agent::getTaxCode, appAgentVo.getTaxCode());\r\n            List<Agent> agents = agentService.list(wrapper);\r\n            if (isRenewal) {\r\n                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());\r\n            }\r\n            if (GeneralTool.isNotEmpty(agents)) {\r\n                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());\r\n                List<AgentCompany> agentCompanies = agentCompanyService\r\n                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)\r\n                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));\r\n                if (GeneralTool.isNotEmpty(agentCompanies)) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_company_tax_code_duplicate\"));\r\n                }\r\n            }\r\n        }\r\n        // 性质=工作室 + 身份证号码（禁止提交）|| 性质=国际学校 + 身份证号码（禁止提交）\r\n        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key).equals(appAgentVo.getNature()) || String\r\n                .valueOf(ProjectExtraEnum.AGENT_NATURE_INTERNATIONAL_SCHOOL.key).equals(appAgentVo.getNature())) {\r\n            if (GeneralTool.isEmpty(appAgentVo.getIdCardNum())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_card_num_is_empty\"));\r\n            }\r\n            wrapper.eq(Agent::getIdCardNum, appAgentVo.getIdCardNum());\r\n            List<Agent> agents = agentService.list(wrapper);\r\n            if (isRenewal) {\r\n                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());\r\n            }\r\n            if (GeneralTool.isNotEmpty(agents)) {\r\n                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());\r\n                List<AgentCompany> agentCompanies = agentCompanyService\r\n                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)\r\n                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));\r\n                if (GeneralTool.isNotEmpty(agentCompanies)) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_person_id_card_duplicate\"));\r\n                }\r\n            }\r\n        }\r\n        // 改为不禁止\r\n        // //性质=其他 + 代理名称（禁止提交）\r\n        // if\r\n        // (String.valueOf(ProjectExtraEnum.AGENT_NATURE_OTHER.key).equals(appAgentVo.getNature())){\r\n        // if (GeneralTool.isEmpty(appAgentVo.getName())){\r\n        // throw new GetServiceException(\"name missing！\");\r\n        // }\r\n        // wrapper.eq(Agent::getName,appAgentVo.getName());\r\n        // List<Agent> agents = agentService.list(wrapper);\r\n        // if (GeneralTool.isNotEmpty(agents)){\r\n        // Set<Long> ids =\r\n        // agents.stream().map(Agent::getId).collect(Collectors.toSet());\r\n        // List<AgentCompany> agentCompanies =\r\n        // agentCompanyService.list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId,\r\n        // ids).eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));\r\n        // if (GeneralTool.isNotEmpty(agentCompanies)){\r\n        // throw new GetServiceException(LocaleMessageUtils.getMessage(\"record_exist\"));\r\n        // }\r\n        // }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 回填信息\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doUpdateAppAgentInfo(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);\r\n        appAgent.setFkAgentId(agentId);\r\n        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_AGREE.key);\r\n        appAgent.setAppStatusModifyTime(new Date());\r\n        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());\r\n        utilService.setUpdateInfo(appAgent);\r\n        boolean b = updateById(appAgent);\r\n        if (!b) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 同步合同账户资料\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCopyAppAgentContractAccountToAgentContractAccount(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService\r\n                .list(Wrappers.<AppAgentContractAccount>lambdaQuery().eq(AppAgentContractAccount::getFkAppAgentId,\r\n                        appAgentVo.getId()));\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();\r\n\r\n        if (isRenewal) {\r\n            // 续约使用智能同步策略：有则更新，无则新增，多余的删除（合同账户例外：按原有逻辑只处理新增）\r\n            doRenewalSyncContractAccountsWithUpdateStrategy(appAgentContractAccounts, agentId);\r\n        } else {\r\n            // 非续约保持原有逻辑\r\n            Set<Long> appAgentContractAccountIds = appAgentContractAccounts.stream().map(AppAgentContractAccount::getId)\r\n                    .collect(Collectors.toSet());\r\n            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService\r\n                    .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,\r\n                            appAgentContractAccountIds);\r\n            List<SaleMediaAndAttached> mediaAndAttachedSaveList = Lists.newArrayList();\r\n            if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {\r\n                for (AppAgentContractAccount appAgentContractAccount : appAgentContractAccounts) {\r\n                    AgentContractAccountDto agentContractAccountDto = BeanCopyUtils.objClone(appAgentContractAccount,\r\n                            AgentContractAccountDto::new);\r\n                    agentContractAccountDto.setIsActive(true);\r\n                    agentContractAccountDto.setFkAgentId(agentId);\r\n                    agentContractAccountDto.setGmtModified(null);\r\n                    agentContractAccountDto.setGmtModifiedUser(null);\r\n                    Long agentContractAccountId = agentContractAccountService\r\n                            .addContractAccount(agentContractAccountDto);\r\n                    List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds\r\n                            .get(appAgentContractAccount.getId());\r\n                    if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {\r\n                        List<SaleMediaAndAttached> saleMediaAndAttacheds = BeanCopyUtils\r\n                                .copyListProperties(mediaAndAttachedVos, SaleMediaAndAttached::new);\r\n                        for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {\r\n                            saleMediaAndAttached.setId(null);\r\n                            saleMediaAndAttached.setFkTableName(TableEnum.AGENT_CONTRACT_ACCOUNT.key);\r\n                            saleMediaAndAttached.setFkTableId(agentContractAccountId);\r\n                            saleMediaAndAttached.setGmtModified(null);\r\n                            saleMediaAndAttached.setGmtModifiedUser(null);\r\n                            utilService.setCreateInfo(saleMediaAndAttached);\r\n                        }\r\n                        mediaAndAttachedSaveList.addAll(saleMediaAndAttacheds);\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (GeneralTool.isNotEmpty(mediaAndAttachedSaveList)) {\r\n                boolean b = mediaAndAttachedService.saveBatch(mediaAndAttachedSaveList);\r\n                if (!b) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n                }\r\n            }\r\n        }\r\n\r\n        appAgentSetAgreeContext.setAppAgentContractAccounts(appAgentContractAccounts);\r\n    }\r\n\r\n    /**\r\n     * 续约合同账户智能同步策略：有则更新，无则新增，多余的删除\r\n     *\r\n     * @param appAgentContractAccounts AppAgent的合同账户列表\r\n     * @param agentId                  代理ID\r\n     */\r\n    private void doRenewalSyncContractAccountsWithUpdateStrategy(List<AppAgentContractAccount> appAgentContractAccounts,\r\n                                                                 Long agentId) {\r\n        if (CollectionUtil.isEmpty(appAgentContractAccounts)) {\r\n            return;\r\n        }\r\n\r\n        // 获取当前Agent下所有现有的合同账户，建立映射关系\r\n        List<AgentContractAccount> existingContractAccounts = agentContractAccountService.list(\r\n                Wrappers.<AgentContractAccount>lambdaQuery()\r\n                        .eq(AgentContractAccount::getFkAgentId, agentId)\r\n                        .eq(AgentContractAccount::getIsActive, true));\r\n        Map<Long, AgentContractAccount> existingContractAccountMap = existingContractAccounts.stream()\r\n                .collect(Collectors.toMap(AgentContractAccount::getId, Function.identity()));\r\n\r\n        Set<Long> processedContractAccountIds = new HashSet<>();\r\n        List<AgentContractAccount> toUpdateList = new ArrayList<>();\r\n        List<AppAgentContractAccount> toInsertList = new ArrayList<>();\r\n\r\n        for (AppAgentContractAccount appContractAccount : appAgentContractAccounts) {\r\n            Long fkAgentContractAccountId = appContractAccount.getFkAgentContractAccountId();\r\n\r\n            if (GeneralTool.isNotEmpty(fkAgentContractAccountId)\r\n                    && existingContractAccountMap.containsKey(fkAgentContractAccountId)) {\r\n                // 有关联ID且确实存在，准备更新现有的AgentContractAccount记录\r\n                AgentContractAccount existingContractAccount = existingContractAccountMap.get(fkAgentContractAccountId);\r\n                prepareUpdateAgentContractAccountForRenewal(appContractAccount, existingContractAccount);\r\n                toUpdateList.add(existingContractAccount);\r\n                processedContractAccountIds.add(fkAgentContractAccountId);\r\n            } else {\r\n                // 无关联ID或关联的记录不存在，准备新增AgentContractAccount记录\r\n                toInsertList.add(appContractAccount);\r\n            }\r\n        }\r\n\r\n        // 批量更新\r\n        if (CollectionUtil.isNotEmpty(toUpdateList)) {\r\n            boolean updateResult = agentContractAccountService.updateBatchById(toUpdateList);\r\n            if (!updateResult) {\r\n                log.error(\"续约批量更新合同账户失败\");\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n            log.debug(\"续约批量更新合同账户成功，数量: {}\", toUpdateList.size());\r\n        }\r\n\r\n        // 批量新增（使用原有的addContractAccount逻辑保持一致）\r\n        List<SaleMediaAndAttached> mediaAndAttachedSaveList = Lists.newArrayList();\r\n        if (CollectionUtil.isNotEmpty(toInsertList)) {\r\n            // 获取附件信息\r\n            Set<Long> appAgentContractAccountIds = toInsertList.stream().map(AppAgentContractAccount::getId)\r\n                    .collect(Collectors.toSet());\r\n            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService\r\n                    .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,\r\n                            appAgentContractAccountIds);\r\n\r\n            for (AppAgentContractAccount appAgentContractAccount : toInsertList) {\r\n                AgentContractAccountDto agentContractAccountDto = BeanCopyUtils.objClone(appAgentContractAccount,\r\n                        AgentContractAccountDto::new);\r\n                agentContractAccountDto.setIsActive(true);\r\n                agentContractAccountDto.setFkAgentId(agentId);\r\n                agentContractAccountDto.setGmtModified(null);\r\n                agentContractAccountDto.setGmtModifiedUser(null);\r\n                Long agentContractAccountId = agentContractAccountService.addContractAccount(agentContractAccountDto);\r\n\r\n                // 处理附件\r\n                List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds\r\n                        .get(appAgentContractAccount.getId());\r\n                if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {\r\n                    List<SaleMediaAndAttached> saleMediaAndAttacheds = BeanCopyUtils\r\n                            .copyListProperties(mediaAndAttachedVos, SaleMediaAndAttached::new);\r\n                    for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {\r\n                        saleMediaAndAttached.setId(null);\r\n                        saleMediaAndAttached.setFkTableName(TableEnum.AGENT_CONTRACT_ACCOUNT.key);\r\n                        saleMediaAndAttached.setFkTableId(agentContractAccountId);\r\n                        saleMediaAndAttached.setGmtModified(null);\r\n                        saleMediaAndAttached.setGmtModifiedUser(null);\r\n                        utilService.setCreateInfo(saleMediaAndAttached);\r\n                    }\r\n                    mediaAndAttachedSaveList.addAll(saleMediaAndAttacheds);\r\n                }\r\n            }\r\n            log.debug(\"续约批量新增合同账户成功，数量: {}\", toInsertList.size());\r\n        }\r\n\r\n        // 保存附件\r\n        if (GeneralTool.isNotEmpty(mediaAndAttachedSaveList)) {\r\n            boolean b = mediaAndAttachedService.saveBatch(mediaAndAttachedSaveList);\r\n            if (!b) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n        }\r\n\r\n        log.info(\"续约合同账户数据同步完成，更新: {}, 新增: {}\", toUpdateList.size(), toInsertList.size());\r\n    }\r\n\r\n    /**\r\n     * 准备更新的AgentContractAccount记录（续约专用）\r\n     */\r\n    private void prepareUpdateAgentContractAccountForRenewal(AppAgentContractAccount appContractAccount,\r\n                                                             AgentContractAccount existing) {\r\n        BeanCopyUtils.copyProperties(appContractAccount, existing, \"id\", \"fkAgentId\", \"fkAgentContractAccountId\");\r\n        utilService.setUpdateInfo(existing);\r\n    }\r\n\r\n    /**\r\n     * 同步附件\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCopyMediaAndAttached(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        Long appAgentDtoId = appAgentVo.getId();\r\n        List<AppAgentContractAccount> appAgentContractAccounts = appAgentSetAgreeContext.getAppAgentContractAccounts();\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        if (appAgentSetAgreeContext.getIsRenewal()) {\r\n            LambdaQueryWrapper<SaleMediaAndAttached> saleMediaAndAttachedLambdaQueryWrapper = new LambdaQueryWrapper<SaleMediaAndAttached>()\r\n                    .eq(SaleMediaAndAttached::getFkTableId, agentId)\r\n                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key);\r\n            List<SaleMediaAndAttached> saleMediaAndAttacheds = this.mediaAndAttachedService\r\n                    .list(saleMediaAndAttachedLambdaQueryWrapper);\r\n            if (CollectionUtil.isNotEmpty(saleMediaAndAttacheds)) {\r\n                this.mediaAndAttachedService.remove(saleMediaAndAttachedLambdaQueryWrapper);\r\n            }\r\n        }\r\n\r\n        List<SaleMediaAndAttached> saleMediaAndAttacheds = Lists.newArrayList();\r\n        // 代理附件、合同账户附件\r\n        saleMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery()\r\n                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT.key)\r\n                .eq(SaleMediaAndAttached::getFkTableId, appAgentDtoId));\r\n\r\n        // if (GeneralTool.isNotEmpty(appAgentContractAccounts)){\r\n        // Set<Long> ids =\r\n        // appAgentContractAccounts.stream().map(AppAgentContractAccount::getId).collect(Collectors.toSet());\r\n        // List<SaleMediaAndAttached> saleMediaAndAttachedList =\r\n        // mediaAndAttachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery()\r\n        // .eq(SaleMediaAndAttached::getFkTableName,\r\n        // TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)\r\n        // .in(SaleMediaAndAttached::getFkTableId, ids));\r\n        //\r\n        // if (GeneralTool.isNotEmpty(saleMediaAndAttachedList)){\r\n        // saleMediaAndAttacheds.addAll(saleMediaAndAttachedList);\r\n        // }\r\n        // }\r\n\r\n        if (GeneralTool.isEmpty(saleMediaAndAttacheds)) {\r\n            return;\r\n        }\r\n        for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {\r\n            saleMediaAndAttached.setId(null);\r\n            saleMediaAndAttached.setFkTableName(TableEnum.SALE_AGENT.key);\r\n            saleMediaAndAttached.setFkTableId(agentId);\r\n            saleMediaAndAttached.setGmtModified(null);\r\n            saleMediaAndAttached.setGmtModifiedUser(null);\r\n            utilService.setCreateInfo(saleMediaAndAttached);\r\n        }\r\n\r\n        boolean b = mediaAndAttachedService.saveBatch(saleMediaAndAttacheds);\r\n        if (!b) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 同步代理联系人数据\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCopyAppAgentContactPersonToAgentContactPerson(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long fkCompanyId = appAgentSetAgreeContext.getFkCompanyId();\r\n        Long fkAgentId = appAgentSetAgreeContext.getAgentId();\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n\r\n        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();\r\n        List<AppAgentContactPerson> appAgentContactPersons = appAgentContactPersonService.list(Wrappers\r\n                .<AppAgentContactPerson>lambdaQuery().eq(AppAgentContactPerson::getFkAppAgentId, appAgentVo.getId()));\r\n\r\n        if (isRenewal) {\r\n            // 续约使用智能同步策略：有则更新，无则新增，多余的删除\r\n            doRenewalSyncContactPersonsWithUpdateStrategy(appAgentContactPersons, fkAgentId, fkCompanyId);\r\n        } else {\r\n            // 非续约保持原有逻辑\r\n            if (GeneralTool.isNotEmpty(appAgentContactPersons)) {\r\n                List<ContactPersonDto> contactPersonVos = BeanCopyUtils.copyListProperties(appAgentContactPersons,\r\n                        ContactPersonDto::new);\r\n                contactPersonVos.forEach(c -> {\r\n                    c.setFkCompanyId(fkCompanyId);\r\n                    c.setFkTableName(TableEnum.SALE_AGENT.key);\r\n                    c.setFkTableId(fkAgentId);\r\n                    c.setGmtModified(null);\r\n                    c.setGmtModifiedUser(null);\r\n                });\r\n                for (ContactPersonDto contactPersonVo : contactPersonVos) {\r\n                    if (StringUtils.isBlank(contactPersonVo.getFkContactPersonTypeKey())) {\r\n                        contactPersonVo.setFkContactPersonTypeKey(\"CONTACT_AGENT_SALES\");\r\n                    }\r\n                    agentService.addAgentContactPerson(contactPersonVo);\r\n                }\r\n                // 创建合同联系人的时候排除紧急联系人\r\n                List<ContactPersonDto> excludeEmergency = contactPersonVos.stream()\r\n                        .filter(contactPersonVo -> !(contactPersonVo.getFkContactPersonTypeKey() != null\r\n                                && contactPersonVo.getFkContactPersonTypeKey().contains(ContactPersonTypeEnum.EMERGENCY.getCode())))\r\n                        .collect(Collectors.toList());\r\n                for (ContactPersonDto contactPersonVo : excludeEmergency) {\r\n                    contactPersonVo.setFkContactPersonTypeKey(\"CONTACT_AGENT_CONTRACT\");\r\n                    agentService.addAgentContactPerson(contactPersonVo);\r\n                }\r\n            }\r\n        }\r\n        appAgentSetAgreeContext.setAppAgentContactPersons(appAgentContactPersons);\r\n    }\r\n\r\n    /**\r\n     * 续约联系人智能同步策略：有则更新，无则新增，多余的删除\r\n     *\r\n     * @param appAgentContactPersons AppAgent的联系人列表\r\n     * @param agentId                代理ID\r\n     * @param fkCompanyId            公司ID\r\n     */\r\n    private void doRenewalSyncContactPersonsWithUpdateStrategy(List<AppAgentContactPerson> appAgentContactPersons,\r\n                                                               Long agentId, Long fkCompanyId) {\r\n        if (CollectionUtil.isEmpty(appAgentContactPersons)) {\r\n            return;\r\n        }\r\n\r\n        // 获取当前Agent下所有现有的联系人，建立映射关系\r\n        List<SaleContactPerson> existingContactPersons = contactPersonService.list(\r\n                Wrappers.<SaleContactPerson>lambdaQuery()\r\n                        .eq(SaleContactPerson::getFkTableId, agentId));\r\n        Map<Long, SaleContactPerson> existingContactPersonMap = existingContactPersons.stream()\r\n                .collect(Collectors.toMap(SaleContactPerson::getId, Function.identity()));\r\n\r\n        Set<Long> processedContactPersonIds = new HashSet<>();\r\n        List<SaleContactPerson> toUpdateList = new ArrayList<>();\r\n        List<ContactPersonDto> toInsertList = new ArrayList<>();\r\n\r\n        for (AppAgentContactPerson appContactPerson : appAgentContactPersons) {\r\n            Long fkContactPersonId = appContactPerson.getFkContactPersonId();\r\n\r\n            if (GeneralTool.isNotEmpty(fkContactPersonId) && existingContactPersonMap.containsKey(fkContactPersonId)) {\r\n                // 有关联ID且确实存在，准备更新现有的SaleContactPerson记录\r\n                SaleContactPerson existingContactPerson = existingContactPersonMap.get(fkContactPersonId);\r\n                prepareUpdateSaleContactPersonForRenewal(appContactPerson, existingContactPerson);\r\n                toUpdateList.add(existingContactPerson);\r\n                processedContactPersonIds.add(fkContactPersonId);\r\n            } else {\r\n                // 无关联ID或关联的记录不存在，准备新增SaleContactPerson记录\r\n                ContactPersonDto newContactPersonDto = prepareNewContactPersonDtoForRenewal(appContactPerson, agentId,\r\n                        fkCompanyId);\r\n                toInsertList.add(newContactPersonDto);\r\n            }\r\n        }\r\n\r\n        // 批量更新 + 双重创建（排除紧急联系人）\r\n        if (CollectionUtil.isNotEmpty(toUpdateList)) {\r\n            boolean updateResult = contactPersonService.updateBatchById(toUpdateList);\r\n            if (!updateResult) {\r\n                log.error(\"续约批量更新联系人失败\");\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n            log.debug(\"续约批量更新联系人成功，数量: {}\", toUpdateList.size());\r\n            \r\n            // 为非紧急联系人创建合同副本\r\n            List<SaleContactPerson> eligibleForContractCopy = toUpdateList.stream()\r\n                    .filter(contact -> isEligibleForContractCopy(contact.getFkContactPersonTypeKey()))\r\n                    .collect(Collectors.toList());\r\n            \r\n            if (CollectionUtil.isNotEmpty(eligibleForContractCopy)) {\r\n                createContractContactPersonCopies(eligibleForContractCopy, agentId, fkCompanyId);\r\n                log.info(\"续约为更新的联系人创建合同副本，数量: {}\", eligibleForContractCopy.size());\r\n            }\r\n        }\r\n\r\n        // 批量新增 + 双重创建（排除紧急联系人）\r\n        if (CollectionUtil.isNotEmpty(toInsertList)) {\r\n            for (ContactPersonDto contactPersonDto : toInsertList) {\r\n                if (StringUtils.isBlank(contactPersonDto.getFkContactPersonTypeKey())) {\r\n                    contactPersonDto.setFkContactPersonTypeKey(\"CONTACT_AGENT_SALES\");\r\n                }\r\n                agentService.addAgentContactPerson(contactPersonDto);\r\n\r\n                // 为非紧急联系人创建合同副本\r\n                if (isEligibleForContractCopy(contactPersonDto.getFkContactPersonTypeKey())) {\r\n                    ContactPersonDto contractContactPersonDto = BeanCopyUtils.objClone(contactPersonDto,\r\n                            ContactPersonDto::new);\r\n                    contractContactPersonDto.setFkContactPersonTypeKey(\"CONTACT_AGENT_CONTRACT\");\r\n                    agentService.addAgentContactPerson(contractContactPersonDto);\r\n                }\r\n            }\r\n            \r\n            // 统计创建合同副本的数量用于日志记录\r\n            long contractCopyCount = toInsertList.stream()\r\n                    .filter(contact -> isEligibleForContractCopy(contact.getFkContactPersonTypeKey()))\r\n                    .count();\r\n            \r\n            log.debug(\"续约批量新增联系人成功，数量: {}，其中创建合同副本: {}\", toInsertList.size(), contractCopyCount);\r\n        }\r\n\r\n        // 删除不再需要的联系人记录（在现有记录中但不在本次处理范围内的）\r\n        Set<Long> toDeleteIds = existingContactPersonMap.keySet().stream()\r\n                .filter(id -> !processedContactPersonIds.contains(id))\r\n                .collect(Collectors.toSet());\r\n\r\n        if (CollectionUtil.isNotEmpty(toDeleteIds)) {\r\n            contactPersonService.removeByIds(toDeleteIds);\r\n\r\n            // 删除关联的公司数据\r\n            LambdaQueryWrapper<SaleContactPersonCompany> companyWrapper = new LambdaQueryWrapper<SaleContactPersonCompany>()\r\n                    .in(SaleContactPersonCompany::getFkContactPersonId, toDeleteIds);\r\n            List<SaleContactPersonCompany> saleContactPersonCompanies = this.contactPersonCompanyMapper\r\n                    .selectList(companyWrapper);\r\n            if (CollectionUtil.isNotEmpty(saleContactPersonCompanies)) {\r\n                this.contactPersonCompanyMapper.delete(companyWrapper);\r\n            }\r\n\r\n            log.info(\"续约删除不再需要的联系人记录，数量: {}\", toDeleteIds.size());\r\n        }\r\n\r\n        log.info(\"续约联系人数据同步完成，更新: {}, 新增: {}, 删除: {}\", toUpdateList.size(), toInsertList.size(), toDeleteIds.size());\r\n    }\r\n\r\n    /**\r\n     * 准备更新的SaleContactPerson记录（续约专用）\r\n     */\r\n    private void prepareUpdateSaleContactPersonForRenewal(AppAgentContactPerson appContactPerson,\r\n                                                          SaleContactPerson existing) {\r\n        BeanCopyUtils.copyProperties(appContactPerson, existing, \"id\", \"fkTableName\", \"fkTableId\", \"fkContactPersonId\");\r\n        utilService.setUpdateInfo(existing);\r\n    }\r\n\r\n    /**\r\n     * 准备新增的ContactPersonDto记录（续约专用）\r\n     */\r\n    private ContactPersonDto prepareNewContactPersonDtoForRenewal(AppAgentContactPerson appContactPerson, Long agentId,\r\n                                                                  Long fkCompanyId) {\r\n        ContactPersonDto newContactPersonDto = BeanCopyUtils.objClone(appContactPerson, ContactPersonDto::new);\r\n        newContactPersonDto.setFkCompanyId(fkCompanyId);\r\n        newContactPersonDto.setFkTableName(TableEnum.SALE_AGENT.key);\r\n        newContactPersonDto.setFkTableId(agentId);\r\n        newContactPersonDto.setGmtModified(null);\r\n        newContactPersonDto.setGmtModifiedUser(null);\r\n        return newContactPersonDto;\r\n    }\r\n\r\n    /**\r\n     * 同步生成合同\r\n     *\r\n     * @param appAgentSetAgreeContext\r\n     */\r\n    private void doCreateAgentContract(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long fkAgentId = appAgentSetAgreeContext.getAgentId();\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        // 生成合同数据\r\n\r\n        Long agentId = appAgentVo.getId();\r\n        AppAgent appAgent = this.appAgentMapper.selectById(agentId);\r\n        if (ObjectUtils.isNull(appAgent)) {\r\n            log.error(\"代理申请数据有误\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_app_data_error\"));\r\n        }\r\n        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();\r\n\r\n        String nature = appAgentVo.getNature();\r\n        AgentContractDto agentContractDto = new AgentContractDto();\r\n        agentContractDto.setContractApprovalMode(0);\r\n        if (isRenewal) {\r\n            agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());\r\n        } else {\r\n            // 申请来源\r\n            Integer appFrom = appAgent.getAppFrom();\r\n            if (appFrom == null || AgentAppFromEnum.getAgentAppFromEnum(appFrom) == null) {\r\n                agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());\r\n            }\r\n            AgentAppFromEnum agentAppFromEnum = AgentAppFromEnum.getAgentAppFromEnum(appFrom);\r\n            switch (agentAppFromEnum) {\r\n                case WEB_APPLY_1:\r\n                    agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());\r\n                    break;\r\n                case WEB_APPLY_2:\r\n                case PARTNER_APPLY_2:\r\n                    agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n        if (GeneralTool.isNotEmpty(nature)) {\r\n            agentContractDto.setFkAgentContractTypeId(Long.valueOf((nature)));\r\n        }\r\n        agentContractDto.setFkAgentId(fkAgentId);\r\n        agentContractDto.setIsActive(true);\r\n        agentContractDto.setStartTime(appAgentVo.getContractStartTime());\r\n        agentContractDto.setEndTime(appAgentVo.getContractEndTime());\r\n        List<AgentCompany> agentCompanies = agentCompanyService\r\n                .list(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, fkAgentId));\r\n        if (GeneralTool.isEmpty(agentCompanies)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n        Long fkCompanyId = agentCompanies.get(0).getFkCompanyId();\r\n        agentContractDto.setFkCompanyId(fkCompanyId);\r\n        // 合同保存\r\n        agentContractService.addAgentContract(agentContractDto);\r\n        appAgentSetAgreeContext.setFkCompanyId(fkCompanyId);\r\n    }\r\n\r\n    /**\r\n     * 同步代理\r\n     *\r\n     * @param appAgentVo\r\n     * @return\r\n     */\r\n    private AppAgentSetAgreeContext doCopyAppAgentToAgent(AppAgentVo appAgentVo) {\r\n        Boolean isRenewal = AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(appAgentVo.getAppType())\r\n                && appAgentVo.getFkAgentId() != null;\r\n        AppAgentSetAgreeContext appAgentSetAgreeContext = new AppAgentSetAgreeContext();\r\n        appAgentSetAgreeContext.setAppAgentDto(appAgentVo);\r\n        appAgentSetAgreeContext.setIsRenewal(isRenewal);\r\n        if (isRenewal) {\r\n            // 续约处理\r\n            appAgentSetAgreeContext.setAgentId(appAgentVo.getFkAgentId());\r\n            Long id = appAgentVo.getId();\r\n            AppAgent appAgent = this.getById(id);\r\n            if (ObjectUtils.isNull(appAgent)) {\r\n                log.error(\"代理申请数据有误\");\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_app_data_error\"));\r\n            }\r\n\r\n            // 获取现有的Agent数据\r\n            Agent existingAgent = agentService.getById(appAgentVo.getFkAgentId());\r\n            if (ObjectUtils.isEmpty(existingAgent)) {\r\n                log.error(\"找不到对应的代理数据，AgentId: {}\", appAgentVo.getFkAgentId());\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_not_found\"));\r\n            }\r\n\r\n            existingAgent.setFkAreaCountryId(appAgent.getFkAreaCountryId());\r\n            existingAgent.setFkAreaStateId(appAgent.getFkAreaStateId());\r\n            existingAgent.setFkAreaCityId(appAgent.getFkAreaCityId());\r\n            existingAgent.setName(appAgent.getName());\r\n            existingAgent.setNameNote(appAgent.getNameNote());\r\n            existingAgent.setPersonalName(appAgent.getPersonalName());\r\n            existingAgent.setNickName(appAgent.getNickName());\r\n            existingAgent.setNature(appAgent.getNature());\r\n            existingAgent.setNatureNote(appAgent.getNatureNote());\r\n            existingAgent.setLegalPerson(appAgent.getLegalPerson());\r\n            existingAgent.setTaxCode(appAgent.getTaxCode());\r\n            existingAgent.setIdCardNum(appAgent.getIdCardNum());\r\n            existingAgent.setAddress(appAgent.getAddress());\r\n            existingAgent.setRemark(appAgent.getRemark());\r\n\r\n            // 设置修改信息\r\n            utilService.setUpdateInfo(existingAgent);\r\n\r\n            // 保存更新\r\n            boolean updateResult = agentService.updateById(existingAgent);\r\n            if (!updateResult) {\r\n                log.error(\"更新Agent数据失败，AgentId: {}\", existingAgent.getId());\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n\r\n            log.info(\"代理续约更新成功，AgentId: {}, AppAgentId: {}\", existingAgent.getId(), appAgent.getId());\r\n\r\n        } else {\r\n            // 新代理申请处理\r\n            AgentDto agentDto = BeanCopyUtils.objClone(appAgentVo, AgentDto::new);\r\n            assert agentDto != null;\r\n            agentDto.setId(null);\r\n            agentDto.setIsSettlementPort(true);\r\n            agentDto.setIsActive(true);\r\n            agentDto.setIsKeyAgent(false);\r\n            agentDto.setGmtModified(null);\r\n            agentDto.setGmtModifiedUser(null);\r\n            // AgentAddDto agentAddVo = new AgentAddDto();\r\n            // agentAddVo.setAgentVo(agentDto);\r\n            Long fkAgentId = agentService.addAgent(agentDto);\r\n            appAgentSetAgreeContext.setAgentId(fkAgentId);\r\n        }\r\n\r\n        return appAgentSetAgreeContext;\r\n    }\r\n\r\n    /**\r\n     * 审批拒绝\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    private void doSetReject(AppAgentVo appAgentVo) {\r\n        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appAgentVo.getAppStatus())) {\r\n            // 如果不为申请中状态了 则不能设置成拒绝\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n        if (!Objects.equals(appAgentVo.getAppStatusModifyUser(), SecureUtil.getLoginId())) {\r\n            // 登录人和申请审批的人不是同一个 则不能设置拒绝\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"NON_REVIEWER\"));\r\n        }\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);\r\n        assert appAgent != null;\r\n        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REJECT.key);\r\n        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());\r\n        appAgent.setAppStatusModifyTime(new Date());\r\n        utilService.setUpdateInfo(appAgent);\r\n        boolean b = updateById(appAgent);\r\n        if (!b) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n        // 根据申请来源类型发送不同的拒绝邮件\r\n        sendRejectionEmailsByType(appAgentVo);\r\n    }\r\n\r\n    /**\r\n     * 发送拒绝邮件\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    private void addRejectReminderTask(AppAgentVo appAgentVo) {\r\n        Long fkAppAgentId = appAgentVo.getId();\r\n        if (GeneralTool.isEmpty(appAgentVo.getFkStaffId())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"staff_id_null\"));\r\n        }\r\n\r\n        Map<Long, String> companyConfigMap = permissionCenterClient\r\n                .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_AGENT_APP_REJECTED.key, 1).getData();\r\n        String value1 = companyConfigMap.get(appAgentVo.getFkCompanyId());\r\n        // ConfigVo configDto =\r\n        // permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_AGENT_APP_REJECTED.key).getData();\r\n        // String value1 = configDto.getValue1();\r\n        // JSONObject jsonObject = JSONObject.parseObject(value1);\r\n        // Integer geaStatus = jsonObject.getInteger(\"OTHER\");\r\n        // Integer iaeStatus = jsonObject.getInteger(\"IAE\");\r\n\r\n        if (value1.equals(\"1\")) {\r\n            StringBuilder taskTitle = new StringBuilder(\"代理在线申请\");\r\n            taskTitle.append(\"（\").append(appAgentVo.getName()).append(\"）\").append(\"，审批拒绝\");\r\n            String staffName = permissionCenterClient.getStaffName(appAgentVo.getFkStaffId()).getData();\r\n            // 加提醒任务\r\n            Map<String, String> map = new HashMap<>();\r\n            map.put(\"staffName\", staffName);\r\n            map.put(\"agentName\", appAgentVo.getName());\r\n            map.put(\"other\", \"，已经提交在线申请表单，审批拒绝。\");\r\n            String taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER);\r\n\r\n            RemindTaskDto remindTaskVo = new RemindTaskDto();\r\n            remindTaskVo.setTaskTitle(taskTitle.toString());\r\n            remindTaskVo.setTaskRemark(taskRemark);\r\n            // 邮件方式发送\r\n            remindTaskVo.setRemindMethod(\"1\");\r\n            // 默认设置执行中\r\n            remindTaskVo.setStatus(1);\r\n            // 默认背景颜色\r\n            remindTaskVo.setTaskBgColor(\"#3788d8\");\r\n            remindTaskVo.setFkStaffId(appAgentVo.getFkStaffId());\r\n            remindTaskVo.setStartTime(new Date());\r\n            remindTaskVo.setFkTableName(TableEnum.APP_AGENT.key);\r\n            remindTaskVo.setFkTableId(fkAppAgentId);\r\n            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);\r\n\r\n            Result<Boolean> result = reminderCenterClient.batchAdd(Lists.newArrayList(remindTaskVo));\r\n            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"news_emil_send_fail\"));\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 我要审核\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    private void doSetReview(AppAgentVo appAgentVo) {\r\n        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        if (!ProjectExtraEnum.APP_STATUS_NEW.key.equals(appAgentVo.getAppStatus())) {\r\n            // 如果不为新申请状态了 则不能设置成我要审核\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);\r\n        assert appAgent != null;\r\n        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REVIEW.key);\r\n        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());\r\n        appAgent.setAppStatusModifyTime(new Date());\r\n        utilService.setUpdateInfo(appAgent);\r\n        boolean b = updateById(appAgent);\r\n        if (!b) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 设置合同账户列表属性\r\n     *\r\n     * @param appAgentContractAccountListVos\r\n     */\r\n    private void doSetAppAgentContractAccountListDtos(\r\n            List<AppAgentContractAccountListVo> appAgentContractAccountListVos) {\r\n        if (GeneralTool.isEmpty(appAgentContractAccountListVos)) {\r\n            return;\r\n        }\r\n        Set<String> currencyTypeNums = appAgentContractAccountListVos.stream()\r\n                .map(AppAgentContractAccountListVo::getFkCurrencyTypeNum).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        Map<String, String> currencyTypeNumNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums)\r\n                .getData();\r\n        // 附件\r\n        Set<Long> appAgentContractAccountIds = appAgentContractAccountListVos.stream()\r\n                .map(AppAgentContractAccountListVo::getId).collect(Collectors.toSet());\r\n        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtosMap = mediaAndAttachedService\r\n                .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,\r\n                        appAgentContractAccountIds);\r\n        for (AppAgentContractAccountListVo appAgentContractAccountListVo : appAgentContractAccountListVos) {\r\n            /* 设置币种名称 */\r\n            if (GeneralTool.isNotEmpty(currencyTypeNumNameMap)\r\n                    && GeneralTool.isNotEmpty(appAgentContractAccountListVo.getFkCurrencyTypeNum())) {\r\n                appAgentContractAccountListVo.setFkCurrencyTypeNumName(\r\n                        currencyTypeNumNameMap.get(appAgentContractAccountListVo.getFkCurrencyTypeNum()));\r\n            }\r\n            /* 设置币种名称 */\r\n\r\n            // 设置附件\r\n            List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtosMap\r\n                    .get(appAgentContractAccountListVo.getId());\r\n            appAgentContractAccountListVo.setMediaAndAttachedDtos(mediaAndAttachedVos);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 查询合同账户列表\r\n     *\r\n     * @param appAgentContractAccountListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    private List<AppAgentContractAccountListVo> doGetAppAgentContractAccountListDtos(\r\n            AppAgentContractAccountListDto appAgentContractAccountListDto, Page page) {\r\n        if (GeneralTool.isEmpty(appAgentContractAccountListDto.getFkAppAgentId())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        List<AppAgentContractAccount> appAgentContractAccountList = appAgentContractAccountService\r\n                .getAppAgentContractAccounts(appAgentContractAccountListDto, page);\r\n        if (GeneralTool.isEmpty(appAgentContractAccountList)) {\r\n            return Collections.emptyList();\r\n        }\r\n        return BeanCopyUtils.copyListProperties(appAgentContractAccountList, AppAgentContractAccountListVo::new);\r\n    }\r\n\r\n    /**\r\n     * 设置联系人列表属性\r\n     *\r\n     * @param appAgentContactPersonListVos\r\n     */\r\n    private void doSetAppAgentContactPersonListDto(List<AppAgentContactPersonListVo> appAgentContactPersonListVos) {\r\n        if (GeneralTool.isEmpty(appAgentContactPersonListVos)) {\r\n            return;\r\n        }\r\n        /* 联系人类型名称 */\r\n        List<ContactPersonType> contactPersonTypes = contactPersonTypeService.list();\r\n        Map<String, String> typeKeyNameMap = contactPersonTypes.stream().collect(HashMap::new,\r\n                (m, v) -> m.put(v.getTypeKey(), v.getTypeName()), HashMap::putAll);\r\n        /* 联系人类型名称 */\r\n\r\n        for (AppAgentContactPersonListVo appAgentContactPersonListVo : appAgentContactPersonListVos) {\r\n            /* 联系人类型名称 */\r\n            if (GeneralTool.isNotEmpty(appAgentContactPersonListVo.getFkContactPersonTypeKey())) {\r\n                StringJoiner sj = new StringJoiner(\",\");\r\n                String[] contactPersonTypeKeys = appAgentContactPersonListVo.getFkContactPersonTypeKey().split(\",\");\r\n                for (String contactPersonTypeKey : contactPersonTypeKeys) {\r\n                    if (GeneralTool.isNotEmpty(typeKeyNameMap)\r\n                            && GeneralTool.isNotEmpty(typeKeyNameMap.get(contactPersonTypeKey))) {\r\n                        sj.add(typeKeyNameMap.get(contactPersonTypeKey));\r\n                    }\r\n                }\r\n                appAgentContactPersonListVo.setContactPersonTypeName(sj.toString());\r\n            }\r\n            /* 联系人类型名称 */\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 查询联系人列表\r\n     *\r\n     * @param appAgentContactPersonListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    private List<AppAgentContactPersonListVo> doGetAppAgentContactPersonListDtos(\r\n            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page) {\r\n        if (GeneralTool.isEmpty(appAgentContactPersonListDto.getFkAppAgentId())) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        List<AppAgentContactPerson> appAgentContactPersonList = appAgentContactPersonService\r\n                .getAppAgentContactPersons(appAgentContactPersonListDto, page);\r\n        if (GeneralTool.isEmpty(appAgentContactPersonList)) {\r\n            return Collections.emptyList();\r\n        }\r\n        return BeanCopyUtils.copyListProperties(appAgentContactPersonList, AppAgentContactPersonListVo::new);\r\n    }\r\n\r\n    /**\r\n     * 设置属性\r\n     *\r\n     * @param appAgentVo\r\n     */\r\n    private void doSetAppAgentDto(AppAgentVo appAgentVo) {\r\n        if (GeneralTool.isEmpty(appAgentVo)) {\r\n            return;\r\n        }\r\n\r\n        String companyName = permissionCenterClient.getCompanyNameById(appAgentVo.getFkCompanyId()).getData();\r\n        appAgentVo.setFkCompanyName(companyName);\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaCountryId())) {\r\n            String countryName = institutionCenterClient.getCountryNameById(appAgentVo.getFkAreaCountryId()).getData();\r\n            appAgentVo.setFkCountryName(countryName);\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaStateId())) {\r\n            String stateName = institutionCenterClient.getStateFullNameById(appAgentVo.getFkAreaStateId()).getData();\r\n            appAgentVo.setFkStateName(stateName);\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaCityId())) {\r\n            String cityName = institutionCenterClient.getCityFullNameById(appAgentVo.getFkAreaCityId()).getData();\r\n            appAgentVo.setFkCityName(cityName);\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getFkStaffId())) {\r\n            String bdName = permissionCenterClient.getStaffName(appAgentVo.getFkStaffId()).getData();\r\n            appAgentVo.setBdName(bdName);\r\n\r\n            StaffBdCode staffBdCode = staffBdCodeService.getOne(\r\n                    Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getFkStaffId, appAgentVo.getFkStaffId()));\r\n            appAgentVo.setBdCode(staffBdCode.getBdCode());\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getAppStatus())) {\r\n            appAgentVo.setAppStatusName(\r\n                    ProjectExtraEnum.getValueByKey(appAgentVo.getAppStatus(), ProjectExtraEnum.APP_STATUS_TYPE));\r\n        }\r\n        if (GeneralTool.isNotEmpty(appAgentVo.getNature())) {\r\n            try {\r\n                appAgentVo.setNatureName(ProjectExtraEnum.getValueByKey(Integer.valueOf(appAgentVo.getNature()),\r\n                        ProjectExtraEnum.AGENT_NATURE_TYPE));\r\n            } catch (Exception e) {\r\n                throw new GetServiceException(e.getMessage());\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 获取AppAgentDto\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    private AppAgentVo doGetAppAgentDto(Long id) {\r\n        if (GeneralTool.isEmpty(id)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        AppAgent appAgent = appAgentMapper.selectById(id);\r\n        AppAgentVo appAgentVo = BeanCopyUtils.objClone(appAgent, AppAgentVo::new);\r\n        return appAgentVo;\r\n    }\r\n\r\n    /**\r\n     * 设置属性\r\n     *\r\n     * @param appAgentListVos\r\n     */\r\n    private void doSetAppAgentListDtoName(List<AppAgentListVo> appAgentListVos) {\r\n        if (GeneralTool.isEmpty(appAgentListVos)) {\r\n            return;\r\n        }\r\n\r\n        // 公司ids\r\n        Set<Long> companyIds = appAgentListVos.stream().map(AppAgentListVo::getFkCompanyId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        // 国家ids\r\n        Set<Long> countryIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaCountryId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        // 州省ids\r\n        Set<Long> stateIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaStateId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        // 城市ids\r\n        Set<Long> cityIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaCityId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        // 代理ids\r\n        Set<Long> agentIds = appAgentListVos.stream().map(AppAgentListVo::getFkAgentId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n        // BDids\r\n        Set<Long> staffIds = appAgentListVos.stream().map(AppAgentListVo::getFkStaffId).filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n\r\n        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();\r\n        Map<Long, String> countryNamesMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();\r\n        Map<Long, String> stateFullNamesMap = institutionCenterClient.getStateFullNamesByIds(stateIds).getData();\r\n        Map<Long, String> cityFullNamesMap = institutionCenterClient.getCityFullNamesByIds(cityIds).getData();\r\n        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);\r\n        Map<Long, String> numMap = Maps.newHashMap();\r\n        Map<Long, String> bdCodeMap = Maps.newHashMap();\r\n\r\n        if (GeneralTool.isNotEmpty(agentIds)) {\r\n            List<Agent> agents = agentService.list(Wrappers.<Agent>lambdaQuery().in(Agent::getId, agentIds));\r\n            numMap = agents.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getNum()), HashMap::putAll);\r\n        }\r\n        if (GeneralTool.isNotEmpty(staffIds)) {\r\n            List<StaffBdCode> staffBdCodes = staffBdCodeService\r\n                    .list(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, staffIds));\r\n            if (GeneralTool.isNotEmpty(staffBdCodes)) {\r\n                bdCodeMap = staffBdCodes.stream().collect(HashMap::new,\r\n                        (m, v) -> m.put(v.getFkStaffId(), v.getBdCode()), HashMap::putAll);\r\n            }\r\n        }\r\n\r\n        for (AppAgentListVo appAgentListVo : appAgentListVos) {\r\n            /* 区域名称 */\r\n            StringBuilder area = new StringBuilder();\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaCountryId())\r\n                    && GeneralTool.isNotEmpty(countryNamesMap)\r\n                    && GeneralTool.isNotEmpty(countryNamesMap.get(appAgentListVo.getFkAreaCountryId()))) {\r\n                area.append(countryNamesMap.get(appAgentListVo.getFkAreaCountryId()));\r\n            }\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaStateId())\r\n                    && GeneralTool.isNotEmpty(stateFullNamesMap)\r\n                    && GeneralTool.isNotEmpty(stateFullNamesMap.get(appAgentListVo.getFkAreaStateId()))) {\r\n                area.append(\"/\").append(stateFullNamesMap.get(appAgentListVo.getFkAreaStateId()));\r\n            }\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaCityId())\r\n                    && GeneralTool.isNotEmpty(cityFullNamesMap)\r\n                    && GeneralTool.isNotEmpty(cityFullNamesMap.get(appAgentListVo.getFkAreaCityId()))) {\r\n                area.append(\"/\").append(cityFullNamesMap.get(appAgentListVo.getFkAreaCityId()));\r\n            }\r\n            appAgentListVo.setAreaName(area.toString());\r\n            /* 区域名称 */\r\n\r\n            /* 公司名称 */\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkCompanyId()) && GeneralTool.isNotEmpty(companyNameMap)) {\r\n                appAgentListVo.setFkCompanyName(companyNameMap.get(appAgentListVo.getFkCompanyId()));\r\n            }\r\n            /* 公司名称 */\r\n\r\n            /* 代理编号 */\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAgentId()) && GeneralTool.isNotEmpty(numMap)) {\r\n                appAgentListVo.setAgentNum(numMap.get(appAgentListVo.getFkAgentId()));\r\n            }\r\n            /* 代理编号 */\r\n\r\n            /* bd名称&bd编号 */\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getFkStaffId())) {\r\n                if (GeneralTool.isNotEmpty(staffNamesMap)) {\r\n                    appAgentListVo.setBdName(staffNamesMap.get(appAgentListVo.getFkStaffId()));\r\n                }\r\n                if (GeneralTool.isNotEmpty(bdCodeMap)) {\r\n                    appAgentListVo.setBdCode(bdCodeMap.get(appAgentListVo.getFkStaffId()));\r\n                }\r\n            }\r\n            // 申请状态名称\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getAppStatus())) {\r\n                appAgentListVo.setAppStatusName(ProjectExtraEnum.getValueByKey(appAgentListVo.getAppStatus(),\r\n                        ProjectExtraEnum.APP_STATUS_TYPE));\r\n            }\r\n\r\n            // 代理性质名称\r\n            if (GeneralTool.isNotEmpty(appAgentListVo.getNature())) {\r\n                try {\r\n                    appAgentListVo.setNatureName(ProjectExtraEnum.getValueByKey(\r\n                            Integer.valueOf(appAgentListVo.getNature()), ProjectExtraEnum.AGENT_NATURE_TYPE));\r\n                } catch (Exception e) {\r\n                    throw new GetServiceException(e.getMessage());\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    /**\r\n     * 查询列表数据\r\n     *\r\n     * @param appAgentListDto\r\n     * @param page\r\n     * @return\r\n     */\r\n    private List<AppAgentListVo> doGetAppAgentListDtos(AppAgentListDto appAgentListDto, Page page) {\r\n        IPage<Agent> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));\r\n        // 获取业务下属\r\n        Long staffId = SecureUtil.getStaffId();\r\n        // 员工id + 业务下属员工ids\r\n        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();\r\n        staffFollowerIds.add(staffId);\r\n        // 获取分页数据\r\n        List<AppAgentListVo> appAgentListVos = appAgentMapper.getAppAgents(iPage, staffFollowerIds, appAgentListDto,\r\n                appAgentListDto.getAgentAnnualSummaryVo());\r\n        page.setAll((int) iPage.getTotal());\r\n        if (GeneralTool.isEmpty(appAgentListVos)) {\r\n            return Collections.emptyList();\r\n        }\r\n        return appAgentListVos;\r\n    }\r\n\r\n    /**\r\n     * 保存合同账户\r\n     *\r\n     * @param addAppAgentContext\r\n     */\r\n    private void doSaveAppAgentContractAccount(AddAppAgentContext addAppAgentContext) {\r\n        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();\r\n        List<AppAgentContractAccountAddDto> appAgentContractAccountAddDtos = appAgentAddDto\r\n                .getAppAgentContractAccountAddVos();\r\n\r\n        if (GeneralTool.isNotEmpty(appAgentAddDto.getId())) {\r\n            // 如果是更新\r\n            if (GeneralTool.isNotEmpty(appAgentContractAccountAddDtos)) {\r\n\r\n                List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService\r\n                        .list(Wrappers.lambdaQuery(AppAgentContractAccount.class)\r\n                                .eq(AppAgentContractAccount::getFkAppAgentId, appAgentAddDto.getId()));\r\n\r\n                Set<Long> accountIds = appAgentContractAccounts.stream().map(AppAgentContractAccount::getId)\r\n                        .collect(Collectors.toSet());\r\n\r\n                // 先删后增\r\n                appAgentContractAccountService.remove(Wrappers.lambdaQuery(AppAgentContractAccount.class)\r\n                        .eq(AppAgentContractAccount::getFkAppAgentId, appAgentAddDto.getId()));\r\n\r\n                if (GeneralTool.isNotEmpty(accountIds)) {\r\n                    // 先删后增\r\n                    mediaAndAttachedService.remove(Wrappers.lambdaQuery(SaleMediaAndAttached.class)\r\n                            .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)\r\n                            .eq(SaleMediaAndAttached::getTypeKey,\r\n                                    FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key)\r\n                            .in(SaleMediaAndAttached::getFkTableId, accountIds));\r\n                }\r\n            }\r\n        }\r\n\r\n        // 一个币种只能创建一个账户\r\n        Map<String, List<AppAgentContractAccountAddDto>> currencyNumMap = appAgentContractAccountAddDtos.stream()\r\n                .collect(Collectors.groupingBy(AppAgentContractAccountAddDto::getFkCurrencyTypeNum));\r\n        for (List<AppAgentContractAccountAddDto> agentContractAccountAddVoList : currencyNumMap.values()) {\r\n            if (agentContractAccountAddVoList.size() > 1) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"only_has_one_account\"));\r\n            }\r\n        }\r\n\r\n        List<MediaAndAttachedDto> saveMediaAndAttachedDtos = Lists.newArrayList();\r\n        for (AppAgentContractAccountAddDto appAgentContractAccountAddDto : appAgentContractAccountAddDtos) {\r\n            if (GeneralTool.isEmpty(appAgentContractAccountAddDto.getIsDefault())) {\r\n                appAgentContractAccountAddDto.setIsDefault(false);\r\n            }\r\n            List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentContractAccountAddDto.getMediaAndAttachedVos();\r\n            AppAgentContractAccount appAgentContractAccount = BeanCopyUtils.objClone(appAgentContractAccountAddDto,\r\n                    AppAgentContractAccount::new);\r\n            assert appAgentContractAccount != null;\r\n            appAgentContractAccount.setFkAppAgentId(addAppAgentContext.getFkAppAgentId());\r\n            utilService.setCreateInfo(appAgentContractAccount);\r\n            appAgentContractAccount.setGmtCreateUser(\"[form]\");\r\n            boolean b = appAgentContractAccountService.save(appAgentContractAccount);\r\n            if (!b) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n            if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {\r\n                for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {\r\n                    String tableName = TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key;\r\n                    mediaAndAttachedDto.setFkTableName(tableName);\r\n                    mediaAndAttachedDto.setFkTableId(appAgentContractAccount.getId());\r\n                    mediaAndAttachedDto.setTypeKey(FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key);\r\n                    saveMediaAndAttachedDtos.add(mediaAndAttachedDto);\r\n                }\r\n            }\r\n        }\r\n        if (GeneralTool.isNotEmpty(saveMediaAndAttachedDtos)) {\r\n            Boolean batchSaveResult = mediaAndAttachedService.saveBatchMediaAndAttached(saveMediaAndAttachedDtos);\r\n            if (!batchSaveResult) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 保存代理联系人\r\n     *\r\n     * @param addAppAgentContext\r\n     */\r\n    private void doSaveAppAgentContactPerson(AddAppAgentContext addAppAgentContext) {\r\n        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();\r\n        List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = appAgentAddDto\r\n                .getAppAgentContactPersonAddVos();\r\n        if (GeneralTool.isNotEmpty(appAgentAddDto.getId())) {\r\n            // 如果是编辑 先删后增\r\n            appAgentContactPersonService.remove(Wrappers.lambdaQuery(AppAgentContactPerson.class)\r\n                    .eq(AppAgentContactPerson::getFkAppAgentId, appAgentAddDto.getId()));\r\n        }\r\n\r\n        // 如果是新版，设置默认的isCommissionEmail值\r\n        if (AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {\r\n            for (AppAgentContactPersonAddDto contactPerson : appAgentContactPersonAddDtos) {\r\n                if (contactPerson.getIsCommissionEmail() != null) {\r\n                    continue;\r\n                }\r\n                // 使用枚举获取默认值\r\n                contactPerson.setIsCommissionEmail(\r\n                        ContactPersonTypeEnum.getDefaultIsCommissionEmail(contactPerson.getFkContactPersonTypeKey()));\r\n            }\r\n        }\r\n\r\n        List<AppAgentContactPerson> appAgentContactPeoples = BeanCopyUtils\r\n                .copyListProperties(appAgentContactPersonAddDtos, AppAgentContactPerson::new);\r\n        appAgentContactPeoples.forEach(appAgentContactPerson -> {\r\n            appAgentContactPerson.setFkAppAgentId(addAppAgentContext.getFkAppAgentId());\r\n            utilService.setCreateInfo(appAgentContactPerson);\r\n            appAgentContactPerson.setGmtCreateUser(\"[form]\");\r\n        });\r\n        boolean b = appAgentContactPersonService.saveBatch(appAgentContactPeoples);\r\n        if (!b) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 新增代理申请\r\n     *\r\n     * @param appAgentAddDto\r\n     * @return\r\n     */\r\n    private AddAppAgentContext doSaveAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentAddDto, AppAgent::new);\r\n        assert appAgent != null;\r\n        // 设置名称\r\n        if (ProjectExtraEnum.APP_AGENT_COMPANY.key.equals(appAgentAddDto.getNatureType())) {\r\n            if (ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key.equals(appAgentAddDto.getCooperationType())) {\r\n                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key));\r\n            } else {\r\n                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key));\r\n            }\r\n        } else if (ProjectExtraEnum.APP_AGENT_PERSONAL.key.equals(appAgentAddDto.getNatureType())) {\r\n            appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key));\r\n        }\r\n\r\n        boolean isNewAdd = GeneralTool.isEmpty(appAgentAddDto.getId());\r\n\r\n        // 若是奖学金过来的申请 创建人要改变\r\n        String createUser = GeneralTool.isNotEmpty(appAgentAddDto.getUserId())\r\n                ? \"get_issue_\" + appAgentAddDto.getUserId()\r\n                : \"[form]\";\r\n        // 新申请\r\n        appAgent.setAppType(AgentAppTypeEnum.NEW_APPLICATION.getCode());\r\n        if (isNewAdd) {\r\n            // 默认新申请\r\n            appAgent.setAppStatus(0);\r\n            utilService.setCreateInfo(appAgent);\r\n            appAgent.setGmtCreateUser(createUser);\r\n\r\n            int i = appAgentMapper.insert(appAgent);\r\n            if (i != 1) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n        } else {\r\n            if (appAgent.getAppStatus() == 3) { // 拒绝状态重新提交表单，则修改状态为新申请\r\n                appAgent.setAppStatus(0);\r\n            }\r\n            utilService.setUpdateInfo(appAgent);\r\n            appAgent.setGmtModifiedUser(createUser);\r\n            int i = appAgentMapper.updateById(appAgent);\r\n            if (i != 1) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n            }\r\n        }\r\n\r\n        // 保存营业执照\r\n        List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentAddDto.getMediaAndAttachedVos();\r\n        if (!isNewAdd) {\r\n            // 如果是编辑 先删后增\r\n            mediaAndAttachedService.deleteMediaAndAttachedByTableId(appAgent.getId(), TableEnum.APP_AGENT.key);\r\n        }\r\n        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {\r\n            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {\r\n                mediaAndAttachedDto.setFkTableId(appAgent.getId());\r\n                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n                if (GeneralTool.isEmpty(mediaAndAttachedDto.getTypeKey())) {\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n                }\r\n            }\r\n            Boolean flag = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAndAttachedDtos);\r\n            if (!flag) {\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n        }\r\n        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();\r\n        addAppAgentContext.setFkAppAgentId(appAgent.getId());\r\n        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);\r\n        return addAppAgentContext;\r\n    }\r\n\r\n    /**\r\n     * 注册合作伙伴用户\r\n     * 新申请和续约申请统一注册管理员类型，根据业务类型发送不同的邮件模板\r\n     *\r\n     * @param appAgentSetAgreeContext 代理审核通过上下文\r\n     */\r\nprivate void doRegisterPartnerUsers(AppAgentSetAgreeContext appAgentSetAgreeContext) {\r\n        Long agentId = appAgentSetAgreeContext.getAgentId();\r\n        log.info(\"开始注册合作伙伴用户，代理ID: {}\", agentId);\r\n\r\n        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();\r\n        List<AppAgentContactPerson> contactPersons = appAgentSetAgreeContext.getAppAgentContactPersons();\r\n\r\n        if (GeneralTool.isEmpty(contactPersons)) {\r\n            log.info(\"联系人列表为空，跳过合作伙伴用户注册，代理ID: {}\", agentId);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // 获取BD员工邮件信息\r\n            StaffVo bdStaff = getBdStaffInfoHasEmail(appAgentVo.getFkStaffId());\r\n            Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();\r\n            \r\n            // 统一注册管理员类型，邮件模板根据续约状态区分\r\n            ContactPersonTypeEnum targetPersonType = ContactPersonTypeEnum.ADMIN;\r\n            \r\n            log.info(\"业务类型: {}, 注册联系人类型: {}, 代理ID: {}\", \r\n                isRenewal ? \"续约申请\" : \"新申请\", targetPersonType.getMsg(), agentId);\r\n                \r\n            // 统一的注册和邮件发送流程\r\n            registerAndSendEmails(contactPersons, bdStaff, appAgentVo, appAgentSetAgreeContext, targetPersonType, isRenewal);\r\n            \r\n        } catch (Exception e) {\r\n            log.error(\"注册合作伙伴用户异常，代理ID: {}\", agentId, e);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"partner_user_register_process_failed\", agentId));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 统一的注册和邮件发送流程\r\n     *\r\n     * @param contactPersons 联系人列表\r\n     * @param bdStaff BD员工信息\r\n     * @param appAgentVo 代理信息\r\n     * @param context 上下文\r\n     * @param targetPersonType 目标联系人类型\r\n     * @param isRenewal 是否为续约\r\n     */\r\n    private void registerAndSendEmails(List<AppAgentContactPerson> contactPersons, StaffVo bdStaff,\r\n                                     AppAgentVo appAgentVo, AppAgentSetAgreeContext context,\r\n                                     ContactPersonTypeEnum targetPersonType, Boolean isRenewal) {\r\n        Long agentId = context.getAgentId();\r\n        Long appAgentId = appAgentVo.getId();\r\n        \r\n        // 参数校验\r\n        validateRegisterParameters(contactPersons, bdStaff, appAgentVo, targetPersonType, agentId);\r\n        \r\n        // 过滤并构建注册用户列表\r\n        List<RegisterPartnerUserDto> registerList = contactPersons.stream()\r\n                .filter(contactPerson -> isEligibleContactPerson(contactPerson, targetPersonType))\r\n                .map(contactPerson -> buildRegisterDto(contactPerson, bdStaff, appAgentVo, context))\r\n                .collect(Collectors.toList());\r\n\r\n        if (GeneralTool.isEmpty(registerList)) {\r\n            log.info(\"没有符合条件的联系人需要注册，代理ID: {}, 目标类型: {}\", agentId, targetPersonType.getMsg());\r\n            return;\r\n        }\r\n\r\n        // 验证邮箱唯一性\r\n        validateEmailUniqueness(registerList, agentId);\r\n\r\n        log.info(\"准备注册合作伙伴用户，代理ID: {}, 目标类型: {}, 注册用户数: {}\", \r\n            agentId, targetPersonType.getMsg(), registerList.size());\r\n\r\n        try {\r\n            // 调用公共方法进行用户注册\r\n            List<RegisterPartnerUserVo> registerSuccessAccounts = callPartnerCenterRegister(registerList, agentId);\r\n\r\n            // 发送合作伙伴用户审批通过邮件\r\n            sendPartnerUserApprovalEmails(registerList, registerSuccessAccounts, agentId, appAgentId, isRenewal, bdStaff);\r\n            \r\n        } catch (Exception e) {\r\n            log.error(\"注册和发送邮件流程异常，代理ID: {}, 目标类型: {}\", agentId, targetPersonType.getMsg(), e);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"partner_user_register_and_email_failed\", agentId));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 调用合作伙伴中心注册用户的公共方法\r\n     *\r\n     * @param registerList 注册用户列表\r\n     * @param agentId 代理ID（用于日志记录）\r\n     * @return 注册结果列表\r\n     */\r\n    @Override\r\n    public List<RegisterPartnerUserVo> callPartnerCenterRegister(List<RegisterPartnerUserDto> registerList, Long agentId) {\r\n        log.info(\"开始调用合作伙伴中心注册用户，代理ID: {}, 注册用户数: {}\", agentId, registerList.size());\r\n        \r\n        if (CollectionUtil.isEmpty(registerList)) {\r\n            log.warn(\"注册用户列表为空，跳过注册，代理ID: {}\", agentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        try {\r\n            // 调用远程服务注册用户\r\n            Result<List<RegisterPartnerUserVo>> registerResult = this.partnerCenterClient.registerPartnerUser(registerList);\r\n            if (!registerResult.isSuccess()) {\r\n                log.error(\"调用合作伙伴中心注册用户接口失败，代理ID: {}, 错误信息: {}\", agentId, registerResult.getCode());\r\n                throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"partner_user_register_failed\", registerResult.getCode()));\r\n            }\r\n\r\n            List<RegisterPartnerUserVo> registerResults = registerResult.getData();\r\n            log.info(\"成功注册合作伙伴用户，代理ID: {}, 注册用户数: {}\", agentId, registerList.size());\r\n            \r\n            return registerResults != null ? registerResults : new ArrayList<>();\r\n            \r\n        } catch (Exception e) {\r\n            log.error(\"注册合作伙伴用户异常，代理ID: {}\", agentId, e);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"partner_user_register_error\", agentId));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 验证注册参数\r\n     *\r\n     * @param contactPersons 联系人列表\r\n     * @param bdStaff BD员工信息\r\n     * @param appAgentVo 代理信息\r\n     * @param targetPersonType 目标联系人类型\r\n     * @param agentId 代理ID\r\n     */\r\n    private void validateRegisterParameters(List<AppAgentContactPerson> contactPersons, StaffVo bdStaff,\r\n                                          AppAgentVo appAgentVo, ContactPersonTypeEnum targetPersonType, Long agentId) {\r\n        if (GeneralTool.isEmpty(contactPersons)) {\r\n            log.error(\"联系人列表为空，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"contact_persons_empty\", agentId));\r\n        }\r\n\r\n        if (bdStaff == null) {\r\n            log.error(\"BD员工信息为空，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"bd_staff_null\", agentId));\r\n        }\r\n\r\n        if (appAgentVo == null) {\r\n            log.error(\"代理申请信息为空，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"app_agent_null\", agentId));\r\n        }\r\n\r\n        if (targetPersonType == null) {\r\n            log.error(\"目标联系人类型为空，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"target_person_type_null\", agentId));\r\n        }\r\n\r\n        // 验证BD员工邮件信息完整性\r\n        if (StringUtils.isBlank(bdStaff.getEmail())) {\r\n            log.error(\"BD员工邮件信息不完整，代理ID: {}, 邮箱: {}\",\r\n                agentId, bdStaff.getEmail());\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"bd_staff_email_incomplete\", agentId));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 验证邮箱地址唯一性\r\n     *\r\n     * @param registerList 注册用户列表\r\n     * @param agentId 代理ID\r\n     */\r\n    private void validateEmailUniqueness(List<RegisterPartnerUserDto> registerList, Long agentId) {\r\n        Set<String> emailSet = new HashSet<>();\r\n        List<String> duplicateEmails = new ArrayList<>();\r\n\r\n        for (RegisterPartnerUserDto register : registerList) {\r\n            String email = register.getToEmail();\r\n            if (StringUtils.isBlank(email)) {\r\n                log.warn(\"发现空邮箱地址，代理ID: {}, 联系人: {}\", agentId, register.getToUser());\r\n                continue;\r\n            }\r\n\r\n            if (!emailSet.add(email.toLowerCase())) {\r\n                duplicateEmails.add(email);\r\n            }\r\n        }\r\n\r\n        if (!duplicateEmails.isEmpty()) {\r\n            log.error(\"发现重复邮箱地址，代理ID: {}, 重复邮箱: {}\", agentId, duplicateEmails);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"duplicate_emails_found\", \r\n                agentId, String.join(\", \", duplicateEmails)));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发送合作伙伴用户审批通过邮件\r\n     *\r\n     * @param registerList 注册用户列表\r\n     * @param registerResults 注册结果列表\r\n     * @param agentId 代理ID\r\n     * @param appAgentId 代理申请ID\r\n     * @param isRenewal 是否为续约申请\r\n     * @param bdStaff BD员工信息\r\n     */\r\n    private void sendPartnerUserApprovalEmails(List<RegisterPartnerUserDto> registerList,\r\n                                             List<RegisterPartnerUserVo> registerResults,\r\n                                             Long agentId, Long appAgentId, Boolean isRenewal,\r\n                                             StaffVo bdStaff) {\r\n        if (GeneralTool.isEmpty(registerList)) {\r\n            log.warn(\"注册用户列表为空，跳过邮件发送，代理ID: {}\", agentId);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            log.info(\"开始发送合作伙伴用户审批通过邮件，代理ID: {}, 邮件数量: {}\", agentId, registerList.size());\r\n\r\n            // 创建结果映射以提高查找性能\r\n            Map<String, RegisterPartnerUserVo> resultMap = new HashMap<>();\r\n            if (CollectionUtil.isNotEmpty(registerResults)) {\r\n                resultMap = registerResults.stream()\r\n                        .collect(Collectors.toMap(RegisterPartnerUserVo::getAccount, Function.identity(), (a, b) -> a));\r\n            }\r\n\r\n            List<EmailSendContext> hasAccountContexts = new ArrayList<>();\r\n            List<EmailSendContext> noAccountContexts = new ArrayList<>();\r\n\r\n            // 处理每个注册用户的邮件发送\r\n            for (RegisterPartnerUserDto register : registerList) {\r\n                String email = register.getToEmail();\r\n                RegisterPartnerUserVo result = resultMap.get(email);\r\n\r\n                boolean hasPassword = result != null && StringUtils.isNotBlank(result.getPassword());\r\n                String password = hasPassword ? result.getPassword() : null;\r\n                \r\n                // 根据是否有密码确定邮件模板\r\n                EmailTemplateEnum emailTemplate = determineEmailTemplate(isRenewal, hasPassword);\r\n                \r\n                // 构建邮件上下文\r\n                EmailSendContext context = buildEmailSendContext(register, password, bdStaff.getId(), appAgentId, emailTemplate);\r\n                \r\n                if (hasPassword) {\r\n                    hasAccountContexts.add(context);\r\n                    log.debug(\"构建有账号邮件上下文，收件人: {}, 代理ID: {}\", email, agentId);\r\n                } else {\r\n                    noAccountContexts.add(context);\r\n                    log.debug(\"构建无账号邮件上下文，收件人: {}, 代理ID: {}\", email, agentId);\r\n                }\r\n            }\r\n\r\n\r\n            // 给BD员工发送相同内容的邮件副本\r\n            sendBdEmailCopiesForRegistration(hasAccountContexts, noAccountContexts, agentId, appAgentId, bdStaff);\r\n\r\n            // 批量发送邮件\r\n            if (!hasAccountContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(hasAccountContexts, agentId);\r\n                log.info(\"成功发送有账号邮件，代理ID: {}, 邮件数量: {}\", agentId, hasAccountContexts.size());\r\n            }\r\n\r\n            if (!noAccountContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(noAccountContexts, agentId);\r\n                log.info(\"成功发送无账号邮件，代理ID: {}, 邮件数量: {}\", agentId, noAccountContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送合作伙伴用户审批通过邮件异常，代理ID: {}\", agentId, e);\r\n            // 不抛异常，避免影响主流程\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取BD员工信息\r\n     *\r\n     * @param bdStaffId BD员工ID\r\n     * @return BD员工信息\r\n     */\r\n    @Override\r\n    public StaffVo getBdStaffInfoHasEmail(Long bdStaffId) {\r\n        log.info(\"开始获取BD员工信息，员工ID: {}\", bdStaffId);\r\n\r\n        if (GeneralTool.isEmpty(bdStaffId)) {\r\n            log.error(\"BD员工ID为空，无法获取发件人信息\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"bd_staff_id_null\"));\r\n        }\r\n\r\n        Result<StaffVo> staffResult = permissionCenterClient.getStaffById(bdStaffId);\r\n        if (!staffResult.isSuccess() || GeneralTool.isEmpty(staffResult.getData())) {\r\n            log.error(\"获取BD员工信息失败，员工ID: {}, 错误信息: {}\", bdStaffId, staffResult.getCode());\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"bd_staff_info_get_failed\", bdStaffId));\r\n        }\r\n\r\n        StaffVo staffVo = staffResult.getData();\r\n        if (StringUtils.isBlank(staffVo.getEmail())) {\r\n            log.error(\"BD员工邮箱为空，员工ID: {}\", bdStaffId);\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"bd_staff_email_null\", bdStaffId));\r\n        }\r\n\r\n//        if (StringUtils.isBlank(staffVo.getEmailPassword())) {\r\n//            log.error(\"BD员工邮件密码为空，员工ID: {}\", bdStaffId);\r\n//            throw new GetServiceException(\r\n//                    LocaleMessageUtils.getFormatMessage(\"bd_staff_email_password_null\", bdStaffId));\r\n//        }\r\n\r\n        log.info(\"成功获取BD员工信息，员工ID: {}, 邮箱: {}\", bdStaffId, staffVo.getEmail());\r\n        return staffVo;\r\n    }\r\n\r\n    /**\r\n     * 批量获取BD员工信息, 跳过邮箱密码为空的staff数据\r\n     *\r\n     * @param staffIds BD员工ID集合\r\n     * @return Map<Long, StaffVo> key为员工ID，value为BD员工信息\r\n     */\r\n    private Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByStaffIds(Set<Long> staffIds) {\r\n        log.info(\"开始批量获取BD员工信息，员工ID: {}\", staffIds);\r\n\r\n        Map<Long, StaffVo> resultMap = new HashMap<>();\r\n\r\n        if (GeneralTool.isEmpty(staffIds)) {\r\n            log.error(\"BD员工ID集合为空，无法获取员工信息\");\r\n            return resultMap;\r\n        }\r\n\r\n        List<StaffVo> staffList = permissionCenterClient.getStaffByIds(staffIds);\r\n        if (CollectionUtil.isEmpty(staffList)) {\r\n            log.warn(\"未获取到BD员工信息，员工ID集合: {}\", staffIds);\r\n            return resultMap;\r\n        }\r\n\r\n        // 转换为Map并验证员工信息完整性\r\n        for (StaffVo staffVo : staffList) {\r\n            if (staffVo == null) {\r\n                continue;\r\n            }\r\n\r\n            if (StringUtils.isBlank(staffVo.getEmail())) {\r\n                log.error(\"BD员工邮箱为空，跳过该员工，员工ID: {}\", staffVo.getId());\r\n                continue;\r\n            }\r\n\r\n            resultMap.put(staffVo.getId(), staffVo);\r\n            log.debug(\"成功获取BD员工信息，员工ID: {}, 邮箱: {}\", staffVo.getId(), staffVo.getEmail());\r\n        }\r\n\r\n        log.info(\"批量获取BD员工信息完成，成功获取 {}/{} 个员工信息\", resultMap.size(), staffIds.size());\r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 根据代理ID集合批量获取BD员工信息, 跳过邮箱密码为空的staff数据\r\n     *\r\n     * @param agentIds 代理ID集合\r\n     * @return Map<Long, StaffVo> key为代理ID，value为BD员工信息\r\n     */\r\n    @Override\r\n    public Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByAgentIds(Set<Long> agentIds) {\r\n        log.info(\"开始批量获取BD员工信息，代理ID集合: {}\", agentIds);\r\n\r\n        Map<Long, StaffVo> resultMap = new HashMap<>();\r\n\r\n        // 参数校验\r\n        if (GeneralTool.isEmpty(agentIds)) {\r\n            log.error(\"代理ID集合为空，无法获取BD员工信息\");\r\n            return resultMap;\r\n        }\r\n\r\n        // 第一步：通过代理IDs获取AgentStaff映射关系\r\n        Map<Long, AgentStaff> agentStaffMap = this.agentStaffService.getAgentStaffByAgentIds(agentIds);\r\n        if (GeneralTool.isEmpty(agentStaffMap)) {\r\n            log.warn(\"未找到代理对应的BD员工关系，代理ID集合: {}\", agentIds);\r\n            return resultMap;\r\n        }\r\n\r\n        // 第二步：提取所有BD员工IDs\r\n        Set<Long> staffIds = agentStaffMap.values().stream()\r\n                .map(AgentStaff::getFkStaffId)\r\n                .filter(Objects::nonNull)\r\n                .collect(Collectors.toSet());\r\n\r\n        if (GeneralTool.isEmpty(staffIds)) {\r\n            log.warn(\"未找到有效的BD员工ID，代理ID集合: {}\", agentIds);\r\n            return resultMap;\r\n        }\r\n\r\n        // 第三步：批量获取BD员工信息\r\n        Map<Long, StaffVo> staffInfoMap = this.getStaffInfoSkipEmptyEmailByStaffIds(staffIds);\r\n        if (GeneralTool.isEmpty(staffInfoMap)) {\r\n            log.warn(\"未获取到BD员工信息，员工ID集合: {}\", staffIds);\r\n            return resultMap;\r\n        }\r\n\r\n        // 第四步：组装结果Map（代理ID -> BD员工信息）\r\n        for (Map.Entry<Long, AgentStaff> entry : agentStaffMap.entrySet()) {\r\n            Long agentId = entry.getKey();\r\n            Long staffId = entry.getValue().getFkStaffId();\r\n\r\n            if (staffId != null && staffInfoMap.containsKey(staffId)) {\r\n                StaffVo staffVo = staffInfoMap.get(staffId);\r\n                resultMap.put(agentId, staffVo);\r\n                log.debug(\"成功映射代理ID: {} -> BD员工: {}\", agentId, staffVo.getName());\r\n            } else {\r\n                log.warn(\"代理ID: {} 对应的BD员工信息获取失败，员工ID: {}\", agentId, staffId);\r\n            }\r\n        }\r\n\r\n        log.info(\"批量获取BD员工信息完成，成功获取 {}/{} 个代理的BD信息\",\r\n                resultMap.size(), agentIds.size());\r\n\r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 校验续约申请重新提交的合法性\r\n     *\r\n     * @param appAgentAddDto 续约申请数据\r\n     */\r\n    private void validateRenewalResubmission(AppAgentAddDto appAgentAddDto) {\r\n        // 判断是新增还是更新\r\n        boolean isUpdate = GeneralTool.isNotEmpty(appAgentAddDto.getId());\r\n        \r\n        if (isUpdate) {\r\n            log.info(\"续约申请更新操作，开始校验申请状态，申请ID: {}\", appAgentAddDto.getId());\r\n            \r\n            // 查询现有记录是否存在\r\n            AppAgent existingAppAgent = this.getById(appAgentAddDto.getId());\r\n            if (ObjectUtils.isNull(existingAppAgent)) {\r\n                log.error(\"续约申请记录不存在，申请ID: {}\", appAgentAddDto.getId());\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"renewal_application_not_found\"));\r\n            }\r\n            \r\n            // 检查申请状态\r\n            Integer appStatus = existingAppAgent.getAppStatus();\r\n            log.debug(\"当前续约申请状态，申请ID: {}, 状态码: {}, 状态描述: {}\", \r\n                appAgentAddDto.getId(), appStatus, AppAgentStatusEnum.getMsgByCode(appStatus));\r\n            \r\n            // 如果状态是审核中或已通过，不允许重新提交\r\n            if (AppAgentStatusEnum.isReview(appStatus) || AppAgentStatusEnum.isAgree(appStatus)) {\r\n                String errorMessage;\r\n                if (AppAgentStatusEnum.isReview(appStatus)) {\r\n                    errorMessage = LocaleMessageUtils.getMessage(\"renewal_application_review_cannot_resubmit\");\r\n                } else { // AppAgentStatusEnum.isAgree(appStatus)\r\n                    errorMessage = LocaleMessageUtils.getMessage(\"renewal_application_agree_cannot_resubmit\");\r\n                }\r\n                \r\n                log.error(\"续约申请当前状态不允许重新提交，申请ID: {}, 当前状态: {} ({})\", \r\n                    appAgentAddDto.getId(), appStatus, AppAgentStatusEnum.getMsgByCode(appStatus));\r\n                throw new GetServiceException(errorMessage);\r\n            }\r\n            \r\n            log.info(\"续约申请状态校验通过，申请ID: {}, 状态: {}\", \r\n                appAgentAddDto.getId(), AppAgentStatusEnum.getMsgByCode(appStatus));\r\n        } else {\r\n            log.info(\"续约申请新增操作，跳过状态校验\");\r\n        }\r\n    }\r\n\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public void renewalAdd(AppAgentAddDto appAgentAddDto) {\r\n        log.info(\"开始保存续约申请数据，参数: {}\", appAgentAddDto);\r\n        \r\n        // 前置校验：检查更新操作的合法性\r\n        validateRenewalResubmission(appAgentAddDto);\r\n\r\n        // 保存续约申请数据（专门为续约申请设计，不复用saveOrUpdateAppAgent以保持兼容性）\r\n        AddAppAgentContext addAppAgentContext = saveRenewalAppAgent(appAgentAddDto);\r\n\r\n        // 发送续约申请提交邮件（参考addAppAgent的新邮件模板逻辑）\r\n        sendRenewalSubmittedEmails(addAppAgentContext);\r\n\r\n        log.info(\"续约申请数据保存完成，申请ID: {}\", addAppAgentContext.getFkAppAgentId());\r\n    }\r\n\r\n    /**\r\n     * 专门为续约申请保存数据\r\n     * 不能复用saveOrUpdateAppAgent，因为那个方法会强制设置appType为新申请，需要保持原有逻辑的兼容性\r\n     *\r\n     * @param appAgentAddDto 续约申请数据\r\n     * @return AddAppAgentContext 保存上下文\r\n     */\r\n    private AddAppAgentContext saveRenewalAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        // 续约申请数据校验（复用现有校验逻辑）\r\n        this.validateAppAgent(appAgentAddDto);\r\n\r\n        // 1.保存续约申请主记录\r\n        AddAppAgentContext addAppAgentContext = doSaveRenewalAppAgent(appAgentAddDto);\r\n\r\n        // 2.保存联系人（复用现有逻辑）\r\n        doSaveAppAgentContactPerson(addAppAgentContext);\r\n\r\n        // 3.保存合同账户（复用现有逻辑）\r\n        doSaveAppAgentContractAccount(addAppAgentContext);\r\n\r\n        Long fkAgentId = appAgentAddDto.getFkAgentId();\r\n        String key = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + fkAgentId;\r\n        String token = this.getRedis.get(key);\r\n        if (StringUtils.isNotBlank(token)) {\r\n            this.getRedis.del(key);\r\n        }\r\n        return addAppAgentContext;\r\n    }\r\n\r\n    /**\r\n     * 保存续约申请主记录\r\n     * 基于doSaveAppAgent方法，但专门为续约申请设计\r\n     *\r\n     * @param appAgentAddDto 续约申请数据\r\n     * @return AddAppAgentContext 保存上下文\r\n     */\r\n    private AddAppAgentContext doSaveRenewalAppAgent(AppAgentAddDto appAgentAddDto) {\r\n        AppAgent appAgent = BeanCopyUtils.objClone(appAgentAddDto, AppAgent::new);\r\n        if (appAgent == null) {\r\n            log.error(\"续约申请数据转换失败，appAgentAddDto: {}\", appAgentAddDto);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_vo_null\"));\r\n        }\r\n\r\n        // 设置代理性质\r\n        setAgentNature(appAgent, appAgentAddDto);\r\n\r\n        // 判断是新增还是更新\r\n        boolean isUpdate = GeneralTool.isNotEmpty(appAgentAddDto.getId());\r\n\r\n        // 设置创建人\r\n        String createUser = buildCreateUser(appAgentAddDto);\r\n\r\n        // 设置续约申请特有属性\r\n        appAgent.setAppType(AgentAppTypeEnum.RENEWAL_APPLICATION.getCode());\r\n\r\n        // 保存或更新主记录\r\n        if (isUpdate) {\r\n            updateRenewalAppAgent(appAgent, createUser);\r\n        } else {\r\n            insertRenewalAppAgent(appAgent, createUser);\r\n        }\r\n\r\n        // 保存附件\r\n        saveRenewalAppAgentAttachments(appAgent.getId(), appAgentAddDto, isUpdate);\r\n\r\n        // 构建返回上下文\r\n        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();\r\n        addAppAgentContext.setFkAppAgentId(appAgent.getId());\r\n        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);\r\n        return addAppAgentContext;\r\n    }\r\n\r\n    /**\r\n     * 设置代理性质\r\n     */\r\n    private void setAgentNature(AppAgent appAgent, AppAgentAddDto appAgentAddDto) {\r\n        if (ProjectExtraEnum.APP_AGENT_COMPANY.key.equals(appAgentAddDto.getNatureType())) {\r\n            if (ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key.equals(appAgentAddDto.getCooperationType())) {\r\n                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key));\r\n            } else {\r\n                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key));\r\n            }\r\n        } else if (ProjectExtraEnum.APP_AGENT_PERSONAL.key.equals(appAgentAddDto.getNatureType())) {\r\n            appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 构建创建人信息\r\n     */\r\n    private String buildCreateUser(AppAgentAddDto appAgentAddDto) {\r\n        return GeneralTool.isNotEmpty(appAgentAddDto.getUserId())\r\n                ? \"get_issue_\" + appAgentAddDto.getUserId()\r\n                : \"[form]\";\r\n    }\r\n\r\n    /**\r\n     * 新增续约申请记录\r\n     */\r\n    private void insertRenewalAppAgent(AppAgent appAgent, String createUser) {\r\n        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key);\r\n        utilService.setCreateInfo(appAgent);\r\n        appAgent.setGmtCreateUser(createUser);\r\n\r\n        int result = appAgentMapper.insert(appAgent);\r\n        if (result != 1) {\r\n            log.error(\"续约申请新增失败，appAgent: {}\", appAgent);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新续约申请记录\r\n     */\r\n    private void updateRenewalAppAgent(AppAgent appAgent, String createUser) {\r\n        // 如果是拒绝状态重新提交，修改为新申请状态\r\n        if (appAgent.getAppStatus() != null && appAgent.getAppStatus() == 3) {\r\n            appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key);\r\n        }\r\n\r\n        utilService.setUpdateInfo(appAgent);\r\n        appAgent.setGmtModifiedUser(createUser);\r\n\r\n        int result = appAgentMapper.updateById(appAgent);\r\n        if (result != 1) {\r\n            log.error(\"续约申请更新失败，appAgent: {}\", appAgent);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 保存续约申请附件\r\n     */\r\n    private void saveRenewalAppAgentAttachments(Long appAgentId, AppAgentAddDto appAgentAddDto, boolean isUpdate) {\r\n        List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentAddDto.getMediaAndAttachedVos();\r\n\r\n        if (isUpdate) {\r\n            // 更新时先删除原有附件\r\n            mediaAndAttachedService.deleteMediaAndAttachedByTableId(appAgentId, TableEnum.APP_AGENT.key);\r\n        }\r\n\r\n        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {\r\n            // 设置附件关联信息\r\n            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {\r\n                mediaAndAttachedDto.setFkTableId(appAgentId);\r\n                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);\r\n\r\n                if (GeneralTool.isEmpty(mediaAndAttachedDto.getTypeKey())) {\r\n                    log.error(\"附件类型为空，mediaAndAttachedDto: {}\", mediaAndAttachedDto);\r\n                    throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n                }\r\n            }\r\n\r\n            Boolean result = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAndAttachedDtos);\r\n            if (!result) {\r\n                log.error(\"续约申请附件保存失败，appAgentId: {}, attachments: {}\", appAgentId, mediaAndAttachedDtos);\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"insert_fail\"));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 判断联系人是否符合注册条件\r\n     * 检查联系人的角色类型是否包含指定的联系人类型\r\n     *\r\n     * @param contactPerson 联系人信息\r\n     * @param personTypeEnum 目标联系人类型枚举\r\n     * @return true表示符合条件，false表示不符合条件\r\n     */\r\n    /**\r\n     * 判断联系人是否符合注册条件\r\n     *\r\n     * @param contactPerson 联系人\r\n     * @param personTypeEnum 联系人类型枚举\r\n     * @return 是否符合条件\r\n     */\r\n    protected boolean isEligibleContactPerson(AppAgentContactPerson contactPerson, ContactPersonTypeEnum personTypeEnum) {\r\n        if (contactPerson == null || personTypeEnum == null) {\r\n            return false;\r\n        }\r\n        String roleKey = contactPerson.getFkContactPersonTypeKey();\r\n        return isEligibleContactPersonByRoleKey(roleKey, personTypeEnum);\r\n    }\r\n\r\n    /**\r\n     * 判断联系人是否符合注册条件（SaleContactPerson版本）\r\n     *\r\n     * @param contactPerson 联系人\r\n     * @param personTypeEnum 联系人类型枚举\r\n     * @return 是否符合条件\r\n     */\r\n    @Override\r\n    public boolean isEligibleContactPerson(SaleContactPerson contactPerson, ContactPersonTypeEnum personTypeEnum) {\r\n        if (contactPerson == null || personTypeEnum == null) {\r\n            return false;\r\n        }\r\n        String roleKey = contactPerson.getFkContactPersonTypeKey();\r\n        return isEligibleContactPersonByRoleKey(roleKey, personTypeEnum);\r\n    }\r\n\r\n    /**\r\n     * 根据角色键判断联系人是否符合注册条件\r\n     *\r\n     * @param roleKey 角色键（逗号分隔）\r\n     * @param personTypeEnum 联系人类型枚举\r\n     * @return 是否符合条件\r\n     */\r\n    protected boolean isEligibleContactPersonByRoleKey(String roleKey, ContactPersonTypeEnum personTypeEnum) {\r\n        if (StringUtils.isBlank(roleKey) || personTypeEnum == null) {\r\n            return false;\r\n        }\r\n\r\n        // 解析逗号分隔的联系人类型字符串，检查是否包含目标类型\r\n        Set<String> roleSet = Arrays.stream(roleKey.split(\",\"))\r\n                .map(String::trim)\r\n                .filter(StringUtils::isNotBlank)\r\n                .collect(Collectors.toSet());\r\n\r\n        return roleSet.contains(personTypeEnum.getCode());\r\n    }\r\n\r\n    /**\r\n     * 构建注册用户DTO\r\n     *\r\n     * @param contactPerson 联系人\r\n     * @param bdStaff BD员工\r\n     * @param appAgentVo 代理申请信息\r\n     * @param context 上下文\r\n     * @return 注册用户DTO\r\n     */\r\n    private RegisterPartnerUserDto buildRegisterDto(AppAgentContactPerson contactPerson, StaffVo bdStaff,\r\n                                                    AppAgentVo appAgentVo, AppAgentSetAgreeContext context) {\r\n        if (contactPerson == null || bdStaff == null || appAgentVo == null || context == null) {\r\n            log.error(\"构建注册DTO的参数不能为空，contactPerson存在: {}, bdStaff存在: {}, appAgentVo存在: {}, context存在: {}\", \r\n                contactPerson != null, bdStaff != null, appAgentVo != null, context != null);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"build_register_dto_params_null\"));\r\n        }\r\n\r\n        String roleKey = contactPerson.getFkContactPersonTypeKey();\r\n        ContactPersonTypeEnum contactType = determineContactType(roleKey);\r\n\r\n        RegisterPartnerUserDto registerDto = new RegisterPartnerUserDto();\r\n\r\n        // 设置发件人信息\r\n        registerDto.setFromUser(bdStaff.getName());\r\n        registerDto.setFromEmail(bdStaff.getEmail());\r\n        registerDto.setEmailPassword(bdStaff.getEmailPassword());\r\n\r\n        // 设置收件人信息\r\n        registerDto.setToUser(contactPerson.getName());\r\n        String[] split = contactPerson.getEmail().split(\"; \");\r\n        if (ArrayUtil.isNotEmpty(split)) {\r\n            registerDto.setToEmail(split[0]);\r\n        }\r\n        // 设置业务信息\r\n        registerDto.setAgentId(context.getAgentId());\r\n        registerDto.setCompanyId(appAgentVo.getFkCompanyId());\r\n        registerDto.setAgentName(appAgentVo.getName());\r\n        registerDto.setApplyAgentName(appAgentVo.getName());\r\n        registerDto.setLoginId(SecureUtil.getLoginId());\r\n\r\n        // 设置角色信息\r\n        registerDto.setPartnerRoleCode(contactType.getPartnerRoleCode());\r\n        registerDto.setPartnerRoleId(contactType.getPartnerRoleId());\r\n        registerDto.setTemplateKey(getTemplateKey(contactType));\r\n\r\n        log.debug(\"构建注册DTO成功，代理ID: {}, 联系人: {}, 角色类型: {}\", \r\n            context.getAgentId(), contactPerson.getName(), contactType.getMsg());\r\n\r\n        return registerDto;\r\n    }\r\n\r\n    /**\r\n     * 确定联系人类型\r\n     *\r\n     * @param roleKey 角色键\r\n     * @return 联系人类型\r\n     */\r\n    private ContactPersonTypeEnum determineContactType(String roleKey) {\r\n        if (StringUtils.isBlank(roleKey)) {\r\n            log.error(\"联系人角色键为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"contact_person_role_key_empty\"));\r\n        }\r\n\r\n        if (roleKey.contains(ContactPersonTypeEnum.ADMIN.getCode())) {\r\n            return ContactPersonTypeEnum.ADMIN;\r\n        } else if (roleKey.contains(ContactPersonTypeEnum.COMMISSION.getCode())) {\r\n            return ContactPersonTypeEnum.COMMISSION;\r\n        }\r\n        \r\n        log.error(\"不支持的联系人类型: {}\", roleKey);\r\n        throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"unsupported_contact_person_type\", roleKey));\r\n    }\r\n\r\n    /**\r\n     * 确定邮件模板类型\r\n     *\r\n     * @param isRenewal 是否为续约\r\n     * @param hasPassword 是否有密码\r\n     * @return 邮件模板枚举\r\n     */\r\n    private EmailTemplateEnum determineEmailTemplate(Boolean isRenewal, boolean hasPassword) {\r\n        if (isRenewal) {\r\n            return hasPassword ? \r\n                EmailTemplateEnum.AGENT_RENEWAL_APPROVED_HAS_ACCOUNT :\r\n                EmailTemplateEnum.AGENT_RENEWAL_APPROVED_NO_ACCOUNT;\r\n        } else {\r\n            return hasPassword ?\r\n                EmailTemplateEnum.AGENT_APPLICATION_APPROVED_HAS_ACCOUNT :\r\n                EmailTemplateEnum.AGENT_APPLICATION_APPROVED_NO_ACCOUNT;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取邮件模板键\r\n     *\r\n     * @param contactType 联系人类型\r\n     * @return 模板键\r\n     */\r\n    private String getTemplateKey(ContactPersonTypeEnum contactType) {\r\n        switch (contactType) {\r\n            case ADMIN:\r\n            case COMMISSION:\r\n                return MailTemplateTypeEnum.INVITE_TO_REGISTER.getCode();\r\n            default:\r\n                log.error(\"不支持的联系人类型: {}\", contactType);\r\n                return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 根据AgentContractRenewalDto组装续约申请数据回显，不保存到数据库\r\n     * 参考AgentServiceImpl的renewalContract方法逻辑\r\n     *\r\n     * @param agentContractRenewalDto 代理合同续约DTO（包含代理ID和联系人ID）\r\n     * @return 组装好的代理申请数据\r\n     */\r\n    @Override\r\n    public AppAgentFormDetailVo getOrBuildRenewalApplicationData(AgentContractRenewalDto agentContractRenewalDto) {\r\n        log.info(\"开始获取或构建代理续约申请数据，参数: {}\", agentContractRenewalDto);\r\n        String sign = agentContractRenewalDto.getSign();\r\n        if (StringUtils.isBlank(sign)) {\r\n            log.error(\"sign为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        Long agentId = decodeId(sign);\r\n        if (GeneralTool.isEmpty(agentId)) {\r\n            log.error(\"代理ID为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_id_null\"));\r\n        }\r\n\r\n        // 参数校验\r\n        if (Objects.isNull(agentContractRenewalDto)) {\r\n            log.error(\"代理合同续约参数为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n\r\n        // 获取代理信息\r\n        Agent agent = agentService.getById(agentId);\r\n        if (Objects.isNull(agent)) {\r\n            log.error(\"代理信息不存在，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_not_exist\"));\r\n        }\r\n        String key = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + agentId;\r\n        String token = this.getRedis.get(key);\r\n        if (StringUtils.isNotBlank(token) && StringUtils.isNotBlank(agentContractRenewalDto.getRenewalToken()) && token.equals(agentContractRenewalDto.getRenewalToken())) {\r\n            return buildRenewalApplicationDataFromScratch(agentContractRenewalDto, agent);\r\n        }\r\n\r\n        // 优化逻辑：先检查是否已有现成的续约申请数据，有则直接复用（不需要重新校验）\r\n        AppAgent existingRenewalApplication = this.getOne(\r\n                Wrappers.<AppAgent>lambdaQuery()\r\n                        .eq(AppAgent::getFkAgentId, agentId)\r\n                        .eq(AppAgent::getAppType, AgentAppTypeEnum.RENEWAL_APPLICATION.getCode()) // 续约申请类型\r\n                        .orderByDesc(AppAgent::getGmtCreate)\r\n                        .last(\"LIMIT 1\"));\r\n\r\n        if (existingRenewalApplication != null) {\r\n            // 如果已存在续约申请数据，直接使用getAppAgentFormDetailById复用现成逻辑\r\n            log.info(\"发现已存在的续约申请数据，复用现成数据，代理ID: {}, 申请ID: {}\", agentId, existingRenewalApplication.getId());\r\n            return getAppAgentFormDetailById(existingRenewalApplication.getId());\r\n        }\r\n\r\n        // 如果不存在现成数据，才进行合同状态校验并构建新数据\r\n        // 校验代理合同审批状态 - 获取最新的有效合同状态进行校验\r\n        AgentContract latestActiveAgentContract = agentContractService.latestActiveAgentContract(agentId);\r\n        if (latestActiveAgentContract != null) {\r\n            // 只有存在合同时才进行状态校验，状态不是\"未签署、待审核、已驳回\"才允许续约\r\n            Integer contractApprovalStatus = latestActiveAgentContract.getContractApprovalStatus();\r\n            if (!AgentContractApprovalStatusEnum.isRenewalApplicationAllowed(contractApprovalStatus)) {\r\n                log.error(\"代理最新合同审批状态不允许续约，当前状态: {}({}), 代理ID: {}, 合同ID: {}\",\r\n                        contractApprovalStatus, contractApprovalStatus, agentId, latestActiveAgentContract.getId());\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_status_not_allow_renewal\"));\r\n            }\r\n        }\r\n        // 如果不存在现成数据，继续使用构建逻辑\r\n        log.info(\"未发现现成的续约申请数据，开始构建新数据，代理ID: {}\", agentId);\r\n        return buildRenewalApplicationDataFromScratch(agentContractRenewalDto, agent);\r\n    }\r\n\r\n    /**\r\n     * 从头构建代理续约申请数据\r\n     * 当数据库中不存在现成的续约申请数据时，使用此方法从代理基础数据构建完整的续约申请表单数据\r\n     *\r\n     * @param agentContractRenewalDto 续约请求参数\r\n     * @param agent                   代理基础信息\r\n     * @return 构建完成的续约申请表单数据\r\n     */\r\n    private AppAgentFormDetailVo buildRenewalApplicationDataFromScratch(AgentContractRenewalDto agentContractRenewalDto,\r\n                                                                        Agent agent) {\r\n        Long agentId = agent.getId();\r\n\r\n        // 获取代理BD信息\r\n        AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agentId);\r\n        if (agentStaff == null) {\r\n            log.error(\"代理BD信息不存在，代理ID: {}\", agentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"bd_not_found\"));\r\n        }\r\n\r\n        // 构造AppAgentFormDetailVo\r\n        AppAgentFormDetailVo formDetailVo = buildAppAgentFormDetailFromAgent(agent, agentStaff.getFkStaffId());\r\n\r\n        // 组装联系人数据\r\n        List<AppAgentContactPersonAddDto> contactPersonList = buildContactPersonListFromAgent(agentId);\r\n        formDetailVo.setAppAgentContactPersonAddVos(contactPersonList);\r\n\r\n        // 组装合同账户数据\r\n        List<AppAgentContractAccountAddDto> contractAccountList = buildContractAccountListFromAgent(agentId);\r\n        formDetailVo.setAppAgentContractAccountAddVos(contractAccountList);\r\n\r\n        // 组装附件数据\r\n        List<MediaAndAttachedDto> attachmentList = buildAttachmentListFromAgent(agentId);\r\n        formDetailVo.setMediaAndAttachedVos(attachmentList);\r\n\r\n        // 设置bmsPrivateFilesUrl - 参考getAppAgentFormDetailById方法逻辑\r\n        Properties props = System.getProperties();\r\n        String profile = props.getProperty(\"spring.profiles.active\");\r\n        if (GeneralTool.isNotEmpty(profile)) {\r\n            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)\r\n                    || profile.equals(AppConstant.TW_CODE)) {\r\n                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);\r\n            } else if (profile.equals(AppConstant.IAE_CODE)) {\r\n                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);// IAE环境下使用私密桶地址为IAE桶url\r\n            } else if (profile.equals(AppConstant.TEST_CODE)) {\r\n                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);\r\n            } else {\r\n                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);\r\n            }\r\n        }\r\n\r\n        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()\r\n                .eq(AgentContract::getFkAgentId, agentId)\r\n                .eq(AgentContract::getIsActive, true)\r\n                .orderByDesc(AgentContract::getGmtCreate)\r\n                .last(\"LIMIT 1\");\r\n        AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);\r\n        if (agentContract != null) {\r\n            formDetailVo.setContractNum(agentContract.getContractNum());\r\n        }\r\n        log.info(\"代理续约申请数据构建完成，代理ID: {}\", agentId);\r\n        return formDetailVo;\r\n    }\r\n\r\n    /**\r\n     * 从Agent构造AppAgentFormDetailVo主要信息\r\n     * 参考报告第3章\"主表数据转换：buildAppAgentFromAgent\"\r\n     * <p>\r\n     * 关键字段映射（与报告表格一致）：\r\n     * - Agent.fkCompanyId → AppAgent.fkCompanyId (直接复制)\r\n     * - Agent.name → AppAgent.name (直接复制)\r\n     * - Agent.nature → AppAgent.nature (直接复制)\r\n     * - Agent.taxCode → AppAgent.taxCode (直接复制)\r\n     * - 固定值2 → AppAgent.appType (续约申请)\r\n     * - 固定值0 → AppAgent.appStatus (待审核状态)\r\n     * - Agent.id → AppAgent.fkAgentId (关联关系)\r\n     *\r\n     * @param agent     代理信息\r\n     * @param fkStaffId BD员工ID（从AgentStaff查询）\r\n     * @return AppAgentFormDetailVo对象\r\n     */\r\n    private AppAgentFormDetailVo buildAppAgentFormDetailFromAgent(Agent agent, Long fkStaffId) {\r\n        if (agent == null || agent.getId() == null) {\r\n            return null;\r\n        }\r\n        Result<StaffVo> staffResult = permissionCenterClient.getStaffById(fkStaffId);\r\n        if (!staffResult.isSuccess() || GeneralTool.isEmpty(staffResult.getData())) {\r\n            log.error(\"获取BD员工信息失败，员工ID: {}, 错误信息: {}\", fkStaffId, staffResult.getCode());\r\n            throw new GetServiceException(LocaleMessageUtils.getFormatMessage(\"bd_staff_info_get_failed\", fkStaffId));\r\n        }\r\n        StaffVo staffInfo = staffResult.getData();\r\n        AppAgentFormDetailVo formDetailVo = new AppAgentFormDetailVo();\r\n\r\n        // 基本信息复制\r\n        formDetailVo.setFkCompanyId(staffInfo.getFkCompanyId());\r\n        formDetailVo.setFkAreaCountryId(agent.getFkAreaCountryId());\r\n        formDetailVo.setFkAreaStateId(agent.getFkAreaStateId());\r\n        formDetailVo.setFkAreaCityId(agent.getFkAreaCityId());\r\n        formDetailVo.setName(agent.getName());\r\n        formDetailVo.setNameNote(agent.getNameNote());\r\n        formDetailVo.setPersonalName(StringUtils.isNotBlank(agent.getPersonalName()) ? agent.getPersonalName() : agent.getName());\r\n        formDetailVo.setNickName(agent.getNickName());\r\n        formDetailVo.setNature(agent.getNature());\r\n        formDetailVo.setNatureNote(agent.getNatureNote());\r\n        formDetailVo.setLegalPerson(agent.getLegalPerson());\r\n        formDetailVo.setTaxCode(agent.getTaxCode());\r\n        formDetailVo.setIdCardNum(agent.getIdCardNum());\r\n        formDetailVo.setAddress(agent.getAddress());\r\n        formDetailVo.setRemark(agent.getRemark());\r\n\r\n        // BD员工信息\r\n        formDetailVo.setFkStaffId(fkStaffId);\r\n\r\n        // 根据nature设置natureType和cooperationType - 参考getAppAgentFormDetailById方法逻辑\r\n        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key))) {\r\n            formDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key);\r\n            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);\r\n        }\r\n        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key))) {\r\n            formDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_COMPANY.key);\r\n            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);\r\n        }\r\n        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key))) {\r\n            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_PERSONAL.key);\r\n        }\r\n\r\n        // 续约申请特有字段 - 根据报告中的固定值设置\r\n        formDetailVo.setAppType(AgentAppTypeEnum.RENEWAL_APPLICATION.getCode()); // 续约申请 (固定值2)\r\n        formDetailVo.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key); // 待审核状态 (固定值0)\r\n        formDetailVo.setFkAgentId(agent.getId()); // 关联原代理ID\r\n\r\n        return formDetailVo;\r\n    }\r\n\r\n    /**\r\n     * 从代理构造联系人列表\r\n     * <p>\r\n     * 转换逻辑：SaleContactPerson → AppAgentContactPerson\r\n     * 字段映射：基本信息（姓名、性别、部门、职位）+ 联系方式（手机、邮箱）\r\n     * 业务字段：联系人类型、是否佣金邮箱、是否新闻邮箱\r\n     *\r\n     * @param agentId 代理ID\r\n     * @return 联系人列表\r\n     */\r\n    private List<AppAgentContactPersonAddDto> buildContactPersonListFromAgent(Long agentId) {\r\n        // 查询代理的所有联系人\r\n        List<SaleContactPerson> allContactPersons = contactPersonService.list(\r\n                Wrappers.<SaleContactPerson>lambdaQuery()\r\n                        .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)\r\n                        .eq(SaleContactPerson::getFkTableId, agentId));\r\n\r\n        // 防御性编程：检查查询结果\r\n        if (CollectionUtil.isEmpty(allContactPersons)) {\r\n            log.warn(\"代理ID: {} 没有查询到任何联系人信息\", agentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        // 在业务层过滤排除合同联系人类型，保持与前端显示逻辑一致\r\n        // 增加空对象过滤，防止空指针异常\r\n        List<SaleContactPerson> contactPersons = allContactPersons.stream()\r\n                .filter(Objects::nonNull) // 过滤null对象\r\n                .filter(person -> !isContractContactPerson(person.getFkContactPersonTypeKey()))\r\n                .collect(Collectors.toList());\r\n\r\n        if (CollectionUtil.isEmpty(contactPersons)) {\r\n            log.warn(\"代理ID: {} 没有找到非合同联系人信息\", agentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        log.info(\"代理ID: {} 查询到 {} 个非合同联系人\", agentId, contactPersons.size());\r\n\r\n        // 转换为DTO\r\n        List<AppAgentContactPersonAddDto> contactPersonDtoList = new ArrayList<>();\r\n        for (SaleContactPerson contactPerson : contactPersons) {\r\n            // 双重检查：虽然已经过滤了，但为了保险起见再次检查\r\n            if (contactPerson == null) {\r\n                log.warn(\"发现空的联系人对象，跳过处理\");\r\n                continue;\r\n            }\r\n\r\n            AppAgentContactPersonAddDto dto = new AppAgentContactPersonAddDto();\r\n            // 复制联系人基本信息 - 参考AgentServiceImpl.createAppAgentContactPersonList\r\n            dto.setFkContactPersonTypeKey(contactPerson.getFkContactPersonTypeKey());\r\n            dto.setName(contactPerson.getName());\r\n            dto.setGender(contactPerson.getGender());\r\n            dto.setDepartment(contactPerson.getDepartment());\r\n            dto.setTitle(contactPerson.getTitle());\r\n            dto.setMobileAreaCode(contactPerson.getMobileAreaCode());\r\n            dto.setMobile(contactPerson.getMobile());\r\n            dto.setEmail(contactPerson.getEmail());\r\n            // 关联代理联系人id\r\n            dto.setFkContactPersonId(contactPerson.getId());\r\n\r\n            // 复制邮件相关字段\r\n            dto.setIsCommissionEmail(contactPerson.getIsCommissionEmail());\r\n            dto.setIsNewsEmail(contactPerson.getIsNewsEmail());\r\n            dto.setFkAreaCountryIdsNews(contactPerson.getFkAreaCountryIdsNews());\r\n            dto.setRemark(contactPerson.getRemark());\r\n\r\n            contactPersonDtoList.add(dto);\r\n        }\r\n\r\n        return contactPersonDtoList;\r\n    }\r\n\r\n    /**\r\n     * 从代理构造合同账户列表\r\n     * 参考报告第6章\"合同账户转换：copyAgentContractAccountToAppAgent\"\r\n     * <p>\r\n     * 转换逻辑：AgentContractAccount → AppAgentContractAccount\r\n     * 字段映射：银行信息（账户名、账号、银行名称、支行名称）+ 币种信息 + 地址信息\r\n     *\r\n     * @param agentId 代理ID\r\n     * @return 合同账户列表\r\n     */\r\n    private List<AppAgentContractAccountAddDto> buildContractAccountListFromAgent(Long agentId) {\r\n        List<AgentContractAccount> contractAccounts = agentContractAccountService.list(\r\n                Wrappers.<AgentContractAccount>lambdaQuery()\r\n                        .eq(AgentContractAccount::getFkAgentId, agentId)\r\n                        .eq(AgentContractAccount::getIsActive, true));\r\n\r\n        if (CollectionUtil.isEmpty(contractAccounts)) {\r\n            log.warn(\"代理ID: {} 没有找到合同账户信息\", agentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        log.info(\"代理ID: {} 查询到 {} 个合同账户\", agentId, contractAccounts.size());\r\n\r\n        // 转换为DTO\r\n        List<AppAgentContractAccountAddDto> accountDtoList = new ArrayList<>();\r\n        for (AgentContractAccount account : contractAccounts) {\r\n            AppAgentContractAccountAddDto dto = new AppAgentContractAccountAddDto();\r\n            dto.setFkCurrencyTypeNum(account.getFkCurrencyTypeNum());\r\n            dto.setBankAccount(account.getBankAccount());\r\n            dto.setBankAccountNum(account.getBankAccountNum());\r\n            dto.setBankName(account.getBankName());\r\n            dto.setBankBranchName(account.getBankBranchName());\r\n            dto.setFkAreaCountryId(account.getFkAreaCountryId());\r\n            dto.setFkAreaStateId(account.getFkAreaStateId());\r\n            dto.setFkAreaCityId(account.getFkAreaCityId());\r\n            dto.setFkAreaCityDivisionId(account.getFkAreaCityDivisionId());\r\n            dto.setBankAddress(account.getBankAddress());\r\n            dto.setBankCodeType(account.getBankCodeType());\r\n            dto.setBankCode(account.getBankCode());\r\n            dto.setAreaCountryCode(account.getAreaCountryCode());\r\n            dto.setIsDefault(account.getIsDefault());\r\n            dto.setRemark(account.getRemark());\r\n            // 关联代理合同账户id\r\n            dto.setFkAgentContractAccountId(account.getId());\r\n\r\n            accountDtoList.add(dto);\r\n        }\r\n\r\n        return accountDtoList;\r\n    }\r\n\r\n    /**\r\n     * 从代理构造附件列表\r\n     * 参考报告第7章\"附件数据转换：copyAgentAttachmentsToAppAgent\"\r\n     * <p>\r\n     * 转换逻辑：SaleMediaAndAttached 表关联关系转换\r\n     * 原关联关系: fkTableName=\"m_agent\", fkTableId=agentId\r\n     * 新关联关系: fkTableName=\"m_app_agent\", fkTableId=appAgentId (数据回显时不涉及)\r\n     *\r\n     * @param agentId 代理ID\r\n     * @return 附件列表\r\n     */\r\n    private List<MediaAndAttachedDto> buildAttachmentListFromAgent(Long agentId) {\r\n        List<SaleMediaAndAttached> attachments = mediaAndAttachedService.list(\r\n                Wrappers.<SaleMediaAndAttached>lambdaQuery()\r\n                        .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)\r\n                        .eq(SaleMediaAndAttached::getFkTableId, agentId));\r\n\r\n        if (CollectionUtil.isEmpty(attachments)) {\r\n            log.warn(\"代理ID: {} 没有找到附件信息\", agentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        log.info(\"代理ID: {} 查询到 {} 个附件\", agentId, attachments.size());\r\n\r\n        // 转换为DTO\r\n        List<MediaAndAttachedDto> attachmentDtoList = BeanCopyUtils.copyListProperties(attachments,\r\n                MediaAndAttachedDto::new);\r\n\r\n        // 通过fkFileGuid从文件中心获取完整文件信息 - 修复filePath、fileNameOrc、fileKey为null的问题\r\n        List<String> guidList = attachments.stream()\r\n                .map(SaleMediaAndAttached::getFkFileGuid)\r\n                .filter(GeneralTool::isNotEmpty)\r\n                .collect(Collectors.toList());\r\n\r\n        if (GeneralTool.isNotEmpty(guidList)) {\r\n            try {\r\n                // 调用文件中心服务获取完整文件信息\r\n                Map<String, List<String>> guidListWithTypeMap = new HashMap<>();\r\n                guidListWithTypeMap.put(LoggerModulesConsts.SALECENTER, guidList);\r\n                Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);\r\n\r\n                if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {\r\n                    List<FileDto> fileDtos = fileDtoResult.getData();\r\n\r\n                    // 根据fileGuid匹配，填充完整文件信息\r\n                    for (MediaAndAttachedDto attachmentDto : attachmentDtoList) {\r\n                        String fileGuid = attachmentDto.getFkFileGuid();\r\n                        if (GeneralTool.isNotEmpty(fileGuid)) {\r\n                            fileDtos.stream()\r\n                                    .filter(fileDto -> fileGuid.equals(fileDto.getFileGuid()))\r\n                                    .findFirst()\r\n                                    .ifPresent(fileDto -> {\r\n                                        attachmentDto.setFilePath(fileDto.getFilePath());\r\n                                        attachmentDto.setFileNameOrc(fileDto.getFileNameOrc());\r\n                                        attachmentDto.setFileKey(fileDto.getFileKey());\r\n                                    });\r\n                        }\r\n                        attachmentDto.setFkTableName(null);\r\n                        attachmentDto.setFkTableId(null);\r\n                        attachmentDto.setGmtModified(null);\r\n                        attachmentDto.setGmtModifiedUser(null);\r\n                    }\r\n                    log.info(\"成功获取 {} 个附件的完整文件信息\", fileDtos.size());\r\n                } else {\r\n                    log.warn(\"从文件中心获取文件信息失败，代理ID: {}, 错误: {}\", agentId, fileDtoResult.getCode());\r\n                }\r\n            } catch (Exception e) {\r\n                log.error(\"调用文件中心服务异常，代理ID: {}, 错误: {}\", agentId, e.getMessage(), e);\r\n            }\r\n        }\r\n\r\n        return attachmentDtoList;\r\n    }\r\n\r\n    /**\r\n     * 判断是否为合同联系人类型\r\n     * 检查联系人类型字符串中是否包含 'CONTACT_AGENT_CONTRACT'\r\n     *\r\n     * @param contactPersonTypeKey 联系人类型字符串（逗号分隔）\r\n     * @return true表示是合同联系人，false表示不是\r\n     */\r\n    private boolean isContractContactPerson(String contactPersonTypeKey) {\r\n        if (StringUtils.isBlank(contactPersonTypeKey)) {\r\n            return false;\r\n        }\r\n        // 分割逗号分隔的类型字符串，检查是否包含合同联系人类型\r\n        return Arrays.asList(contactPersonTypeKey.split(\",\"))\r\n                .stream()\r\n                .map(String::trim)\r\n                .anyMatch(\"CONTACT_AGENT_CONTRACT\"::equals);\r\n    }\r\n\r\n    /**\r\n     * 构建邮件发送上下文\r\n     *\r\n     * @param register 注册用户信息\r\n     * @param password 用户密码（可为null）\r\n     * @param staffId 员工ID\r\n     * @param appAgentId 代理申请ID（用于生成二维码）\r\n     * @param emailTemplateEnum 邮件模板类型\r\n     * @return 邮件发送上下文\r\n     */\r\n    private EmailSendContext buildEmailSendContext(RegisterPartnerUserDto register, String password, Long staffId, Long appAgentId, EmailTemplateEnum emailTemplateEnum) {\r\n        Long agentId = register.getAgentId();\r\n\r\n        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）\r\n        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();\r\n\r\n        // 构建邮件参数\r\n        Map<String, String> emailParams = new HashMap<>();\r\n        emailParams.put(\"personalName\", register.getAgentName()); // 收件人姓名\r\n        emailParams.put(\"name\", register.getAgentName()); // 代理名称\r\n        emailParams.put(\"account\", register.getToEmail()); // 账号\r\n        emailParams.put(\"password\", password); // 密码\r\n        emailParams.put(\"id\", String.valueOf(agentId)); // ID参数，对应模板中的${id}\r\n        emailParams.put(\"qrcode\", qrcodePath); // 小程序二维码路径\r\n        // 添加staffId参数用于语言配置\r\n        if (staffId != null) {\r\n            emailParams.put(\"staffId\", staffId.toString());\r\n        }\r\n\r\n        // 构建邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(ProjectKeyEnum.SALE_CENTER)\r\n                .tableName(TableEnum.SALE_AGENT)\r\n                .tableId(agentId)\r\n                .recipient(register.getToEmail())\r\n                .emailTemplate(emailTemplateEnum)\r\n                .parameters(emailParams)\r\n                .build();\r\n    }\r\n\r\n\r\n    /**\r\n     * 发送代理申请提交邮件\r\n     * 包括给ADMIN联系人和BD员工发送邮件通知\r\n     *\r\n     * @param addAppAgentContext 代理申请上下文\r\n     */\r\n    private void sendApplicationSubmittedEmails(AddAppAgentContext addAppAgentContext) {\r\n        try {\r\n            AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();\r\n            Long agentId = addAppAgentContext.getFkAppAgentId();\r\n            Long staffId = appAgentAddDto.getFkStaffId();\r\n            \r\n            List<EmailSendContext> emailContexts = new ArrayList<>();\r\n            \r\n            // 1. 发送邮件给ADMIN联系人\r\n            sendApplicationSubmittedEmailsToAdmins(agentId, staffId, emailContexts);\r\n            \r\n            \r\n            // 2. 给BD员工发送相同内容的邮件副本\r\n            sendBdEmailCopiesForSubmitted(emailContexts, agentId, staffId);\r\n\r\n            // 3. 批量发送邮件\r\n            if (!emailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(emailContexts, agentId);\r\n                log.info(\"代理申请提交邮件发送成功，代理ID: {}, 邮件数量: {}\", agentId, emailContexts.size());\r\n            }\r\n            \r\n        } catch (Exception e) {\r\n            log.error(\"发送代理申请提交邮件异常\", e);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"news_emil_send_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发送代理申请提交邮件给ADMIN联系人\r\n     *\r\n     * @param agentId 代理ID\r\n     * @param staffId 员工ID\r\n     * @param emailContexts 邮件上下文列表\r\n     */\r\n    private void sendApplicationSubmittedEmailsToAdmins(Long agentId, Long staffId, List<EmailSendContext> emailContexts) {\r\n        try {\r\n            // 查询包含ADMIN类型的联系人（联系人类型Key是多值逗号隔开）\r\n            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(\r\n                    Wrappers.<AppAgentContactPerson>lambdaQuery()\r\n                            .eq(AppAgentContactPerson::getFkAppAgentId, agentId)\r\n            );\r\n            \r\n            if (CollectionUtil.isEmpty(allContacts)) {\r\n                log.warn(\"未找到任何联系人，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n            \r\n            // 筛选包含ADMIN类型的联系人\r\n            List<AppAgentContactPerson> adminContacts = allContacts.stream()\r\n                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))\r\n                    .collect(Collectors.toList());\r\n            \r\n            if (CollectionUtil.isEmpty(adminContacts)) {\r\n                log.warn(\"未找到ADMIN类型联系人，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n            \r\n            for (AppAgentContactPerson adminContact : adminContacts) {\r\n                String[] emails = adminContact.getEmail().split(\"; \");\r\n                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {\r\n                    continue;\r\n                }\r\n                String email = emails[0];\r\n                EmailSendContext emailContext = buildApplicationSubmittedEmailContext(\r\n                        email,\r\n                        agentId,\r\n                        adminContact.getName(),\r\n                        staffId\r\n                );\r\n                emailContexts.add(emailContext);\r\n                log.info(\"添加ADMIN联系人邮件上下文，收件人: {}, 代理ID: {}\", email, agentId);\r\n            }\r\n            \r\n        } catch (Exception e) {\r\n            log.error(\"构建ADMIN联系人邮件上下文异常，代理ID: {}\", agentId, e);\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 根据申请来源类型发送不同的拒绝邮件\r\n     *\r\n     * @param appAgentVo 代理申请信息\r\n     */\r\n    private void sendRejectionEmailsByType(AppAgentVo appAgentVo) {\r\n        try {\r\n            // 先查询AppAgent实体获取appFrom字段\r\n            AppAgent appAgent = getById(appAgentVo.getId());\r\n            if (appAgent == null) {\r\n                log.error(\"未找到代理申请信息，ID: {}\", appAgentVo.getId());\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"search_result_null\"));\r\n            }\r\n\r\n            // 判断是否为新类型申请来源\r\n            if (!AgentAppFromEnum.isNewType(appAgent.getAppFrom())) {\r\n                // 发送旧版拒绝邮件\r\n                addRejectReminderTask(appAgentVo);\r\n            } else {\r\n                // 发送新版拒绝邮件（参照saveCommentAndSendRejectEmail的逻辑）\r\n                sendNewTypeRejectionEmails(appAgentVo);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"发送拒绝邮件异常，代理申请ID: {}\", appAgentVo.getId(), e);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"news_emil_send_fail\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发送新类型拒绝邮件\r\n     * 参照saveCommentAndSendRejectEmail中的邮件发送逻辑\r\n     *\r\n     * @param appAgentVo 代理申请信息\r\n     */\r\n    private void sendNewTypeRejectionEmails(AppAgentVo appAgentVo) {\r\n        Long appAgentId = appAgentVo.getId();\r\n        Long staffId = appAgentVo.getFkStaffId();\r\n        Integer appType = appAgentVo.getAppType();\r\n\r\n        // 获取ADMIN类型的联系人\r\n        List<AppAgentContactPerson> adminContactPersons = getAdminContactPersonsForRejection(appAgentId);\r\n\r\n        // 获取BD员工信息\r\n        StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);\r\n\r\n        // 根据申请类型确定邮件模板\r\n        EmailTemplateEnum emailTemplate;\r\n        Long agentId = null; // 用于续约申请\r\n\r\n        if (ObjectUtils.isNull(appType) || AgentAppTypeEnum.NEW_APPLICATION.getCode().equals(appType)) {\r\n            // 新申请拒绝邮件\r\n            emailTemplate = EmailTemplateEnum.AGENT_APPLICATION_REJECTED;\r\n        } else {\r\n            // 续约申请拒绝邮件\r\n            emailTemplate = EmailTemplateEnum.AGENT_RENEWAL_REJECTED;\r\n\r\n            // 查找关联的Agent\r\n            Long fkAgentId = appAgentVo.getFkAgentId();\r\n            if (fkAgentId == null) {\r\n                log.warn(\"续约申请没有关联的代理ID，跳过邮件发送，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            Agent agent = agentService.getAgentById(fkAgentId);\r\n            if (agent == null) {\r\n                log.warn(\"找不到关联的代理记录，跳过邮件发送，代理申请ID: {}, 代理ID: {}\", appAgentId, fkAgentId);\r\n                return;\r\n            }\r\n\r\n            agentId = agent.getId();\r\n            log.info(\"续约拒绝邮件将使用代理ID: {} (从代理申请ID: {})\", agentId, appAgentId);\r\n        }\r\n\r\n        // 发送拒绝邮件给所有收件人\r\n        sendRejectionEmailsToAllRecipients(adminContactPersons, bdStaff, appAgentId, agentId, staffId, emailTemplate);\r\n    }\r\n\r\n    /**\r\n     * 获取ADMIN类型的联系人（用于拒绝邮件发送）\r\n     *\r\n     * @param appAgentId 代理申请ID\r\n     * @return ADMIN类型的联系人列表\r\n     */\r\n    private List<AppAgentContactPerson> getAdminContactPersonsForRejection(Long appAgentId) {\r\n        // 查询所有联系人\r\n        List<AppAgentContactPerson> allContactPersons = appAgentContactPersonService.list(\r\n                Wrappers.<AppAgentContactPerson>lambdaQuery()\r\n                        .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)\r\n        );\r\n\r\n        if (CollectionUtil.isEmpty(allContactPersons)) {\r\n            log.info(\"代理申请没有关联的联系人，代理申请ID: {}\", appAgentId);\r\n            return new ArrayList<>();\r\n        }\r\n\r\n        // 筛选包含ADMIN类型的联系人\r\n        List<AppAgentContactPerson> adminContacts = allContactPersons.stream()\r\n                .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))\r\n                .collect(Collectors.toList());\r\n\r\n        if (CollectionUtil.isEmpty(adminContacts)) {\r\n            log.info(\"代理申请没有ADMIN类型的联系人，代理申请ID: {}\", appAgentId);\r\n        } else {\r\n            log.info(\"找到{}个ADMIN类型联系人，代理申请ID: {}\", adminContacts.size(), appAgentId);\r\n        }\r\n\r\n        return adminContacts;\r\n    }\r\n\r\n    /**\r\n     * 发送拒绝邮件给所有收件人（ADMIN联系人和BD员工）\r\n     *\r\n     * @param adminContactPersons ADMIN联系人列表\r\n     * @param bdStaff BD员工信息\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID（用于续约申请，可为null）\r\n     * @param staffId 员工ID\r\n     * @param emailTemplate 邮件模板\r\n     */\r\n    private void sendRejectionEmailsToAllRecipients(List<AppAgentContactPerson> adminContactPersons,\r\n                                                   StaffVo bdStaff, Long appAgentId, Long agentId, Long staffId, EmailTemplateEnum emailTemplate) {\r\n        try {\r\n            List<EmailSendContext> emailContexts = new ArrayList<>();\r\n            int adminCount = 0;\r\n\r\n            // 构建ADMIN联系人邮件\r\n            if (CollectionUtil.isNotEmpty(adminContactPersons)) {\r\n                for (AppAgentContactPerson contactPerson : adminContactPersons) {\r\n                    if (StringUtils.isBlank(contactPerson.getEmail())) {\r\n                        continue;\r\n                    }\r\n                    String[] emails = contactPerson.getEmail().split(\"; \");\r\n                    if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {\r\n                        continue;\r\n                    }\r\n                    String email = emails[0];\r\n                    EmailSendContext context = buildRejectionEmailContext(\r\n                            email,\r\n                            contactPerson.getName(),\r\n                            appAgentId,\r\n                            agentId,\r\n                            staffId,\r\n                            emailTemplate\r\n                    );\r\n                    emailContexts.add(context);\r\n                    adminCount++;\r\n                    log.debug(\"构建ADMIN联系人拒绝邮件上下文，收件人: {}, 代理申请ID: {}\", contactPerson.getEmail(), appAgentId);\r\n                }\r\n            }\r\n\r\n\r\n            // 给BD员工发送相同内容的邮件副本\r\n            sendBdEmailCopiesForRejection(emailContexts, bdStaff, appAgentId, agentId);\r\n\r\n            // 批量发送邮件\r\n            if (!emailContexts.isEmpty()) {\r\n                log.info(\"开始批量发送拒绝邮件，代理申请ID: {}, ADMIN联系人: {}个, 总计: {}个\",\r\n                        appAgentId, adminCount, emailContexts.size());\r\n                emailSenderUtils.sendBatchEmails(emailContexts, appAgentId);\r\n                log.info(\"成功批量发送拒绝邮件，代理申请ID: {}, 邮件数量: {}\", appAgentId, emailContexts.size());\r\n            } else {\r\n                log.warn(\"没有有效的收件人，跳过邮件发送，代理申请ID: {}\", appAgentId);\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"批量发送拒绝邮件异常，代理申请ID: {}\", appAgentId, e);\r\n            // 不抛异常，避免影响主流程\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 构建拒绝邮件上下文\r\n     *\r\n     * @param recipientEmail 收件人邮箱\r\n     * @param recipientName 收件人姓名\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID（用于续约申请，可为null）\r\n     * @param staffId 员工ID\r\n     * @param emailTemplate 邮件模板\r\n     * @return 邮件发送上下文\r\n     */\r\n    private EmailSendContext buildRejectionEmailContext(String recipientEmail, String recipientName,\r\n                                                       Long appAgentId, Long agentId, Long staffId, EmailTemplateEnum emailTemplate) {\r\n        // 构建邮件参数\r\n        Map<String, String> emailParams = new HashMap<>();\r\n        emailParams.put(\"personalName\", recipientName); // 收件人姓名\r\n        emailParams.put(\"name\", recipientName); // 收件人姓名（与personalName相同）\r\n\r\n        // 添加staffId用于邮件国际化\r\n        if (staffId != null) {\r\n            emailParams.put(\"staffId\", staffId.toString());\r\n        }\r\n\r\n        // 根据邮件模板类型选择使用的ID和构建二维码路径\r\n        String idForEmail;\r\n        String qrcodePath;\r\n        String encryptedAppAgentId;\r\n\r\n        if (EmailTemplateEnum.AGENT_APPLICATION_REJECTED.equals(emailTemplate)) {\r\n            // 新申请拒绝邮件 - 使用加密的appAgentId（逻辑不变）\r\n            try {\r\n                encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(appAgentId), AESConstant.AESKEY);\r\n            } catch (Exception e) {\r\n                log.error(\"代理申请ID加密失败\", e);\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n            }\r\n            idForEmail = encryptedAppAgentId;\r\n            qrcodePath = MiniProgramPageEnum.NEW_APPAGENT_ADD.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));\r\n        } else if (EmailTemplateEnum.AGENT_RENEWAL_REJECTED.equals(emailTemplate)) {\r\n            String renewalToken = this.getRenewalToken(agentId);\r\n            emailParams.put(\"renewalToken\", renewalToken);\r\n            // 续约拒绝邮件 - 使用agentId\r\n            try {\r\n                encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(agentId), AESConstant.AESKEY);\r\n            } catch (Exception e) {\r\n                log.error(\"代理ID加密失败\", e);\r\n                throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n            }\r\n            idForEmail = encryptedAppAgentId;\r\n            qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));\r\n        } else {\r\n            // 其他情况使用默认逻辑\r\n            idForEmail = \"\";\r\n            qrcodePath = MiniProgramPageEnum.LOGIN.getPath();\r\n        }\r\n\r\n        emailParams.put(\"sign\", idForEmail);\r\n        emailParams.put(\"qrcode\", qrcodePath);\r\n\r\n        // 构建邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(ProjectKeyEnum.SALE_CENTER)\r\n                .tableName(TableEnum.APP_AGENT)\r\n                .tableId(appAgentId)\r\n                .recipient(recipientEmail)\r\n                .emailTemplate(emailTemplate)\r\n                .parameters(emailParams)\r\n                .build();\r\n    }\r\n\r\n    private String getRenewalToken(Long agentId) {\r\n        String tokenKey = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + agentId;\r\n        String token = this.getRedis.get(tokenKey);\r\n        if (StringUtils.isBlank(token)) {\r\n            token = UUID.randomUUID().toString().replace(\"-\", \"\");\r\n            this.getRedis.set(tokenKey, token);\r\n        }\r\n        return token;\r\n    }\r\n\r\n    /**\r\n     * 构建代理申请提交邮件上下文\r\n     * AGENT_APPLICATION_SUBMITTED模板使用id和qrcode参数\r\n     *\r\n     * @param recipientEmail 收件人邮箱\r\n     * @param appAgentId     代理申请ID\r\n     * @param name\r\n     * @param staffId\r\n     * @return 邮件发送上下文\r\n     */\r\n    private EmailSendContext buildApplicationSubmittedEmailContext(String recipientEmail, Long appAgentId, String name, Long staffId) {\r\n        // 对appAgentId进行AES加密（仿照旧版邮件逻辑）\r\n        String encryptedAppAgentId;\r\n        try {\r\n            encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(appAgentId), AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            log.error(\"代理申请ID加密失败\", e);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n        }\r\n        \r\n        // 构建邮件参数\r\n        Map<String, String> emailParams = new HashMap<>();\r\n        emailParams.put(\"sign\", encryptedAppAgentId);\r\n        emailParams.put(\"personalName\", name);\r\n\r\n        // 使用加密后的字符串直接构建qrcode路径\r\n        String qrcodePath = MiniProgramPageEnum.NEW_APPAGENT_ADD.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));\r\n        emailParams.put(\"qrcode\", qrcodePath);\r\n\r\n        if (staffId != null) {\r\n            emailParams.put(\"staffId\", staffId.toString());\r\n        }\r\n\r\n        // 构建邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(ProjectKeyEnum.SALE_CENTER)\r\n                .tableName(TableEnum.SALE_AGENT)\r\n                .tableId(appAgentId)\r\n                .recipient(recipientEmail)\r\n                .emailTemplate(EmailTemplateEnum.AGENT_APPLICATION_SUBMITTED)\r\n                .parameters(emailParams)\r\n                .build();\r\n    }\r\n\r\n    /**\r\n     * 给BD员工发送注册邮件副本\r\n     *\r\n     * @param hasAccountContexts 有账号邮件上下文列表\r\n     * @param noAccountContexts 无账号邮件上下文列表\r\n     * @param agentId 代理ID\r\n     * @param appAgentId 代理申请ID\r\n     * @param bdStaff BD员工信息\r\n     */\r\n    private void sendBdEmailCopiesForRegistration(List<EmailSendContext> hasAccountContexts, List<EmailSendContext> noAccountContexts, \r\n                                                Long agentId, Long appAgentId, StaffVo bdStaff) {\r\n        try {\r\n            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {\r\n                log.warn(\"BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            List<EmailSendContext> bdEmailContexts = new ArrayList<>();\r\n\r\n            // 复制有账号邮件给BD\r\n            for (EmailSendContext originalContext : hasAccountContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 复制无账号邮件给BD\r\n            for (EmailSendContext originalContext : noAccountContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 发送BD邮件副本\r\n            if (!bdEmailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);\r\n                log.info(\"成功发送BD注册邮件副本，代理ID: {}, 邮件数量: {}\", agentId, bdEmailContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送BD注册邮件副本异常，代理ID: {}, 错误: {}\", agentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 给BD员工发送申请提交邮件副本\r\n     *\r\n     * @param originalContexts 原始邮件上下文列表\r\n     * @param agentId 代理ID\r\n     * @param staffId BD员工ID\r\n     */\r\n    private void sendBdEmailCopiesForSubmitted(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {\r\n        try {\r\n            if (staffId == null) {\r\n                log.warn(\"BD员工ID为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            // 获取BD员工信息\r\n            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);\r\n            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {\r\n                log.warn(\"BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            List<EmailSendContext> bdEmailContexts = new ArrayList<>();\r\n\r\n            // 复制申请提交邮件给BD\r\n            for (EmailSendContext originalContext : originalContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 发送BD邮件副本\r\n            if (!bdEmailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);\r\n                log.info(\"成功发送BD申请提交邮件副本，代理ID: {}, 邮件数量: {}\", agentId, bdEmailContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送BD申请提交邮件副本异常，代理ID: {}, 错误: {}\", agentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 给BD员工发送拒绝邮件副本\r\n     *\r\n     * @param originalContexts 原始邮件上下文列表\r\n     * @param bdStaff BD员工信息\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID\r\n     */\r\n    private void sendBdEmailCopiesForRejection(List<EmailSendContext> originalContexts, StaffVo bdStaff, Long appAgentId, Long agentId) {\r\n        try {\r\n            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {\r\n                log.warn(\"BD员工信息或邮箱为空，跳过BD邮件副本发送，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            List<EmailSendContext> bdEmailContexts = new ArrayList<>();\r\n\r\n            // 复制拒绝邮件给BD\r\n            for (EmailSendContext originalContext : originalContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 发送BD邮件副本\r\n            if (!bdEmailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(bdEmailContexts, appAgentId);\r\n                log.info(\"成功发送BD拒绝邮件副本，代理申请ID: {}, 邮件数量: {}\", appAgentId, bdEmailContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送BD拒绝邮件副本异常，代理申请ID: {}, 错误: {}\", appAgentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 创建BD员工的邮件副本（AppAgent场景）\r\n     *\r\n     * @param originalContext 原始邮件上下文\r\n     * @param bdStaff BD员工信息\r\n     * @return BD员工邮件上下文\r\n     */\r\n    private EmailSendContext createBdEmailCopyForApp(EmailSendContext originalContext, StaffVo bdStaff) {\r\n        // 完全复制原始邮件参数，不做任何修改\r\n        Map<String, String> bdEmailParams = new HashMap<>(originalContext.getParameters());\r\n\r\n        // 构建BD员工邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(originalContext.getProjectKey())\r\n                .tableName(originalContext.getTableName())\r\n                .tableId(originalContext.getTableId())\r\n                .recipient(bdStaff.getEmail()) // 只改变收件人邮箱\r\n                .emailTemplate(originalContext.getEmailTemplate()) // 使用相同的邮件模板\r\n                .parameters(bdEmailParams) // 参数完全不变\r\n                .build();\r\n    }\r\n\r\n    /**\r\n     * 判断联系人是否符合创建合同副本的条件（排除紧急联系人）\r\n     * 支持多值联系人类型（逗号分隔）的处理\r\n     *\r\n     * @param fkContactPersonTypeKey 联系人类型标识（可能包含多个逗号分隔的类型）\r\n     * @return true表示需要创建合同副本，false表示不需要\r\n     */\r\n    private boolean isEligibleForContractCopy(String fkContactPersonTypeKey) {\r\n        if (StringUtils.isBlank(fkContactPersonTypeKey)) {\r\n            return true;\r\n        }\r\n        // 检查是否包含紧急联系人代码，包含则排除\r\n        return !fkContactPersonTypeKey.contains(ContactPersonTypeEnum.EMERGENCY.getCode());\r\n    }\r\n\r\n    /**\r\n     * 为销售联系人创建对应的合同联系人副本（续约专用）\r\n     *\r\n     * @param saleContactPerson 销售联系人\r\n     * @param agentId          代理ID\r\n     * @param fkCompanyId      公司ID\r\n     */\r\n    private void createContractContactPersonCopy(SaleContactPerson saleContactPerson, Long agentId, Long fkCompanyId) {\r\n        ContactPersonDto contractContactPersonDto = BeanCopyUtils.objClone(saleContactPerson, ContactPersonDto::new);\r\n        contractContactPersonDto.setFkCompanyId(fkCompanyId);\r\n        contractContactPersonDto.setFkTableName(TableEnum.SALE_AGENT.key);\r\n        contractContactPersonDto.setFkTableId(agentId);\r\n        contractContactPersonDto.setFkContactPersonTypeKey(\"CONTACT_AGENT_CONTRACT\");\r\n        contractContactPersonDto.setGmtModified(null);\r\n        contractContactPersonDto.setGmtModifiedUser(null);\r\n        \r\n        agentService.addAgentContactPerson(contractContactPersonDto);\r\n    }\r\n\r\n    /**\r\n     * 批量为销售联系人创建对应的合同联系人副本（续约专用）\r\n     *\r\n     * @param saleContactPersons 销售联系人列表\r\n     * @param agentId           代理ID\r\n     * @param fkCompanyId       公司ID\r\n     */\r\n    private void createContractContactPersonCopies(List<SaleContactPerson> saleContactPersons, Long agentId, Long fkCompanyId) {\r\n        if (CollectionUtil.isEmpty(saleContactPersons)) {\r\n            return;\r\n        }\r\n        \r\n        for (SaleContactPerson saleContactPerson : saleContactPersons) {\r\n            createContractContactPersonCopy(saleContactPerson, agentId, fkCompanyId);\r\n        }\r\n        \r\n        log.debug(\"续约批量创建合同联系人副本成功，数量: {}\", saleContactPersons.size());\r\n    }\r\n\r\n    /**\r\n     * 发送续约申请提交邮件\r\n     * 包括给ADMIN联系人和BD员工发送邮件通知\r\n     * 参考addAppAgent中的sendApplicationSubmittedEmails方法逻辑\r\n     *\r\n     * @param addAppAgentContext 代理申请上下文\r\n     */\r\n    private void sendRenewalSubmittedEmails(AddAppAgentContext addAppAgentContext) {\r\n        try {\r\n            AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();\r\n            Long appAgentId = addAppAgentContext.getFkAppAgentId();\r\n            Long agentId = appAgentAddDto.getFkAgentId(); // 使用agentId，不需要加密\r\n            Long staffId = appAgentAddDto.getFkStaffId();\r\n\r\n            List<EmailSendContext> emailContexts = new ArrayList<>();\r\n\r\n            // 1. 发送邮件给ADMIN联系人\r\n            sendRenewalSubmittedEmailsToAdmins(appAgentId, agentId, staffId, emailContexts);\r\n\r\n            // 2. 给BD员工发送相同内容的邮件副本\r\n            sendBdEmailCopiesForRenewalSubmitted(emailContexts, agentId, staffId);\r\n\r\n            // 3. 批量发送邮件\r\n            if (!emailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(emailContexts, agentId);\r\n                log.info(\"成功发送续约申请提交邮件，代理ID: {}, 邮件数量: {}\", agentId, emailContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送续约申请提交邮件异常，申请ID: {}, 错误: {}\", addAppAgentContext.getFkAppAgentId(), e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发送续约申请提交邮件给ADMIN联系人\r\n     * 参考sendApplicationSubmittedEmailsToAdmins方法逻辑\r\n     *\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID\r\n     * @param staffId BD员工ID（用于邮件国际化）\r\n     * @param emailContexts 邮件上下文列表\r\n     */\r\n    private void sendRenewalSubmittedEmailsToAdmins(Long appAgentId, Long agentId, Long staffId, List<EmailSendContext> emailContexts) {\r\n        try {\r\n            // 查询包含ADMIN类型的联系人（联系人类型Key是多值逗号隔开）\r\n            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(\r\n                    Wrappers.<AppAgentContactPerson>lambdaQuery()\r\n                            .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)\r\n            );\r\n\r\n            if (CollectionUtil.isEmpty(allContacts)) {\r\n                log.warn(\"未找到任何联系人，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            // 筛选包含ADMIN类型的联系人\r\n            List<AppAgentContactPerson> adminContacts = allContacts.stream()\r\n                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))\r\n                    .collect(Collectors.toList());\r\n\r\n            if (CollectionUtil.isEmpty(adminContacts)) {\r\n                log.warn(\"未找到ADMIN类型联系人，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            for (AppAgentContactPerson adminContact : adminContacts) {\r\n                String[] emails = adminContact.getEmail().split(\"; \");\r\n                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {\r\n                    continue;\r\n                }\r\n                String email = emails[0];\r\n                EmailSendContext emailContext = buildRenewalSubmittedEmailContext(\r\n                        email,\r\n                        agentId, // 使用agentId，不需要加密\r\n                        staffId,  // 添加staffId用于邮件国际化\r\n                        adminContact.getName()\r\n                );\r\n                emailContexts.add(emailContext);\r\n                log.info(\"添加ADMIN联系人续约邮件上下文，收件人: {}, 代理ID: {}\", email, agentId);\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"构建ADMIN联系人续约邮件上下文异常，代理申请ID: {}\", appAgentId, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 给BD员工发送续约申请提交邮件副本\r\n     * 参考sendBdEmailCopiesForSubmitted方法逻辑\r\n     *\r\n     * @param originalContexts 原始邮件上下文列表\r\n     * @param agentId 代理ID\r\n     * @param staffId BD员工ID\r\n     */\r\n    private void sendBdEmailCopiesForRenewalSubmitted(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {\r\n        try {\r\n            if (staffId == null) {\r\n                log.warn(\"BD员工ID为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            // 获取BD员工信息\r\n            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);\r\n            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {\r\n                log.warn(\"BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            List<EmailSendContext> bdEmailContexts = new ArrayList<>();\r\n\r\n            // 复制续约申请提交邮件给BD\r\n            for (EmailSendContext originalContext : originalContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 将BD邮件上下文添加到原始列表中\r\n            originalContexts.addAll(bdEmailContexts);\r\n            log.info(\"成功添加BD续约邮件副本，代理ID: {}, BD邮件数量: {}\", agentId, bdEmailContexts.size());\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送BD续约邮件副本异常，代理ID: {}, 错误: {}\", agentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 构建续约申请提交邮件上下文\r\n     * 参考buildApplicationSubmittedEmailContext方法，但使用AGENT_RENEWAL_SUBMITTED模板和RENEWAL_APPLY页面\r\n     *\r\n     * @param recipientEmail 收件人邮箱\r\n     * @param agentId        代理ID（直接使用，不需要加密）\r\n     * @param staffId        BD员工ID（用于邮件国际化）\r\n     * @param name\r\n     * @return 邮件发送上下文\r\n     */\r\n    private EmailSendContext buildRenewalSubmittedEmailContext(String recipientEmail, Long agentId, Long staffId, String name) {\r\n        // 构建邮件参数\r\n        Map<String, String> emailParams = new HashMap<>();\r\n\r\n        // 使用RENEWAL_APPLY页面构建qrcode路径，拼接agentId\r\n\r\n        // 添加staffId用于邮件国际化\r\n        if (staffId != null) {\r\n            emailParams.put(\"staffId\", staffId.toString());\r\n        }\r\n        emailParams.put(\"personalName\", name);\r\n\r\n        String sign;\r\n        try {\r\n            sign = AESUtils.Encrypt(String.valueOf(agentId), AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            log.error(\"代理ID加密失败\", e);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n        }\r\n        String qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(URLUtil.encodeAll(sign));\r\n\r\n        emailParams.put(\"qrcode\", qrcodePath);\r\n        emailParams.put(\"sign\", sign);\r\n        // 构建邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(ProjectKeyEnum.SALE_CENTER)\r\n                .tableName(TableEnum.SALE_AGENT)\r\n                .tableId(agentId)\r\n                .recipient(recipientEmail)\r\n                .emailTemplate(EmailTemplateEnum.AGENT_RENEWAL_SUBMITTED) // 使用续约提交邮件模板\r\n                .parameters(emailParams)\r\n                .build();\r\n    }\r\n\r\n    /**\r\n     * 续约审核拒绝修改并发送邮件\r\n     *\r\n     * @param appAgentApproveCommentDto 审批意见DTO\r\n     */\r\n    @Override\r\n    @Transactional(rollbackFor = Exception.class)\r\n    public void renewalRejectAndSendEmail(AppAgentApproveCommentDto appAgentApproveCommentDto) {\r\n        log.info(\"开始处理续约审核拒绝并发送邮件，参数: {}\", appAgentApproveCommentDto);\r\n\r\n        // 参数校验\r\n        if (ObjectUtils.isNull(appAgentApproveCommentDto)) {\r\n            log.error(\"审批意见数据不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"parameter_missing\"));\r\n        }\r\n        Long appAgentId = appAgentApproveCommentDto.getFkAppAgentId();\r\n        if (ObjectUtils.isNull(appAgentId)) {\r\n            log.error(\"代理申请ID不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"id_null\"));\r\n        }\r\n        String approveComment = appAgentApproveCommentDto.getApproveComment();\r\n        if (StringUtils.isBlank(approveComment)) {\r\n            log.error(\"审批意见不能为空\");\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"approve_comment_null\"));\r\n        }\r\n\r\n        // 1. 获取代理申请信息\r\n        AppAgent appAgent = this.getById(appAgentId);\r\n        if (ObjectUtils.isNull(appAgent)) {\r\n            log.error(\"代理申请数据不存在，ID: {}\", appAgentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_app_data_error\"));\r\n        }\r\n\r\n        // 1.1. 检查申请状态，只有审核中状态才能执行拒绝操作\r\n        Integer currentStatus = appAgent.getAppStatus();\r\n        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(currentStatus)) {\r\n            log.error(\"代理申请状态不是审核中，无法执行拒绝操作，当前状态: {}, AppAgentId: {}\", currentStatus, appAgentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"app_status_not_review\"));\r\n        }\r\n        log.info(\"代理申请状态检查通过，当前状态: 审核中，AppAgentId: {}\", appAgentId);\r\n\r\n        // 获取关联的代理ID\r\n        Long agentId = appAgent.getFkAgentId();\r\n        if (ObjectUtils.isNull(agentId)) {\r\n            log.error(\"代理申请对应的代理ID为空，无法发送邮件，AppAgentId: {}\", appAgentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_id_null\"));\r\n        }\r\n\r\n        // 2. 更新代理申请状态为拒绝\r\n        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REJECT.key);\r\n        utilService.setUpdateInfo(appAgent);\r\n        boolean updateResult = this.updateById(appAgent);\r\n        if (!updateResult) {\r\n            log.error(\"更新代理申请状态失败，AppAgentId: {}\", appAgentId);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"update_fail\"));\r\n        }\r\n        log.info(\"成功更新代理申请状态为拒绝，AppAgentId: {}\", appAgentId);\r\n\r\n        // 3. 保存审批意见（参考AppAgentApproveCommentServiceImpl的saveAndSendEmail方法）\r\n        Long appAgentApproveCommentId = appAgentApproveCommentService.addAppAgentApproveCommentVo(appAgentApproveCommentDto);\r\n        log.info(\"成功保存续约拒绝审批意见，AppAgentId: {}, CommentId: {}\", appAgentId, appAgentApproveCommentId);\r\n\r\n        // 4. 发送拒绝邮件给admin和BD\r\n        sendRenewalRejectionEmails(appAgentId, agentId, appAgent.getFkStaffId(), approveComment);\r\n\r\n        // 5. 更新审批意见的邮件发送时间和人\r\n        appAgentApproveCommentService.updateEmailTime(appAgentApproveCommentId);\r\n\r\n        log.info(\"续约审核拒绝处理完成，AppAgentId: {}, AgentId: {}\", appAgentId, agentId);\r\n    }\r\n\r\n    @Override\r\n    public List<AppAgentVo> getAppAgentFormDetailByAgentId(Long agentId) {\r\n        // 查询详情\r\n        List<AppAgentVo> appAgentVos = getAppAgentFormDetail(agentId);\r\n        if (GeneralTool.isNotEmpty(appAgentVos)) {\r\n            appAgentVos.forEach(appAgentVo -> {\r\n                // 设置属性\r\n                doSetAppAgentDto(appAgentVo);\r\n            });\r\n        }\r\n\r\n        return appAgentVos;\r\n    }\r\n\r\n    @Override\r\n    public List<BaseSelectEntity> getCommonCountrySelectBySummit() {\r\n        List<AreaCountryVo> areaCountryVos = institutionCenterClient\r\n                .getCountryByPublicLevelBySummit(ProjectExtraEnum.PUBLIC_COUNTRY_COMMON.key);\r\n        if (GeneralTool.isEmpty(areaCountryVos)) {\r\n            return Collections.emptyList();\r\n        }\r\n        List<BaseSelectEntity> baseSelectEntities = BeanCopyUtils.copyListProperties(areaCountryVos,\r\n                BaseSelectEntity::new);\r\n        return baseSelectEntities;\r\n    }\r\n\r\n    private List<AppAgentVo> getAppAgentFormDetail(Long agentId) {\r\n        if (GeneralTool.isEmpty(agentId)) {\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"agent_id_null\"));\r\n        }\r\n        LambdaQueryWrapper<AppAgent> wrapper = new LambdaQueryWrapper<>();\r\n        wrapper.eq(AppAgent::getFkAgentId, agentId);\r\n        wrapper.eq(AppAgent::getAppType, AgentAppTypeEnum.NEW_APPLICATION.getCode());\r\n\r\n        List<AppAgent> appAgent = appAgentMapper.selectList(wrapper);\r\n        List<AppAgentVo> appAgentVos = BeanCopyUtils.copyListProperties(appAgent, AppAgentVo::new);\r\n        return appAgentVos;\r\n    }\r\n\r\n    /**\r\n     * 发送续约拒绝邮件给ADMIN联系人和BD员工\r\n     *\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID\r\n     * @param staffId BD员工ID\r\n     * @param rejectMessage 拒绝原因\r\n     */\r\n    private void sendRenewalRejectionEmails(Long appAgentId, Long agentId, Long staffId, String rejectMessage) {\r\n        try {\r\n            List<EmailSendContext> emailContexts = new ArrayList<>();\r\n\r\n            // 1. 发送邮件给ADMIN联系人\r\n            sendRenewalRejectionEmailsToAdmins(appAgentId, agentId, rejectMessage, staffId, emailContexts);\r\n\r\n            // 2. 给BD员工发送相同内容的邮件\r\n            sendBdEmailCopiesForRenewalRejection(emailContexts, agentId, staffId);\r\n\r\n            // 3. 批量发送邮件\r\n            if (!emailContexts.isEmpty()) {\r\n                emailSenderUtils.sendBatchEmails(emailContexts, agentId);\r\n                log.info(\"成功发送续约拒绝邮件，代理ID: {}, 邮件数量: {}\", agentId, emailContexts.size());\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送续约拒绝邮件异常，代理申请ID: {}, 代理ID: {}, 错误: {}\", appAgentId, agentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 发送续约拒绝邮件给ADMIN联系人\r\n     *\r\n     * @param appAgentId 代理申请ID\r\n     * @param agentId 代理ID\r\n     * @param rejectMessage 拒绝原因\r\n     * @param staffId BD员工ID（用于邮件国际化）\r\n     * @param emailContexts 邮件上下文列表\r\n     */\r\n    private void sendRenewalRejectionEmailsToAdmins(Long appAgentId, Long agentId, String rejectMessage, Long staffId, List<EmailSendContext> emailContexts) {\r\n        try {\r\n            // 查询包含ADMIN类型的联系人\r\n            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(\r\n                    Wrappers.<AppAgentContactPerson>lambdaQuery()\r\n                            .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)\r\n            );\r\n\r\n            if (CollectionUtil.isEmpty(allContacts)) {\r\n                log.warn(\"未找到任何联系人，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            // 筛选包含ADMIN类型的联系人\r\n            List<AppAgentContactPerson> adminContacts = allContacts.stream()\r\n                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))\r\n                    .collect(Collectors.toList());\r\n\r\n            if (CollectionUtil.isEmpty(adminContacts)) {\r\n                log.warn(\"未找到ADMIN类型联系人，代理申请ID: {}\", appAgentId);\r\n                return;\r\n            }\r\n\r\n            for (AppAgentContactPerson adminContact : adminContacts) {\r\n                String[] emails = adminContact.getEmail().split(\"; \");\r\n                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {\r\n                    continue;\r\n                }\r\n                String email = emails[0];\r\n                String contactName = adminContact.getName();\r\n\r\n                EmailSendContext emailContext = buildRenewalRejectionEmailContext(\r\n                        email,\r\n                        contactName,\r\n                        agentId,\r\n                        rejectMessage,\r\n                        staffId  // 添加staffId用于邮件国际化\r\n                );\r\n                emailContexts.add(emailContext);\r\n                log.info(\"添加ADMIN联系人续约拒绝邮件上下文，收件人: {}, 姓名: {}, 代理ID: {}\", email, contactName, agentId);\r\n            }\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"构建ADMIN联系人续约拒绝邮件上下文异常，代理申请ID: {}\", appAgentId, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 给BD员工发送续约拒绝邮件副本\r\n     *\r\n     * @param originalContexts 原始邮件上下文列表\r\n     * @param agentId 代理ID\r\n     * @param staffId BD员工ID\r\n     */\r\n    private void sendBdEmailCopiesForRenewalRejection(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {\r\n        try {\r\n            if (staffId == null) {\r\n                log.warn(\"BD员工ID为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            // 获取BD员工信息\r\n            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);\r\n            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {\r\n                log.warn(\"BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}\", agentId);\r\n                return;\r\n            }\r\n\r\n            List<EmailSendContext> bdEmailContexts = new ArrayList<>();\r\n\r\n            // 复制续约拒绝邮件给BD\r\n            for (EmailSendContext originalContext : originalContexts) {\r\n                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);\r\n                bdEmailContexts.add(bdContext);\r\n            }\r\n\r\n            // 将BD邮件上下文添加到原始列表中\r\n            originalContexts.addAll(bdEmailContexts);\r\n            log.info(\"成功添加BD续约拒绝邮件副本，代理ID: {}, BD邮件数量: {}\", agentId, bdEmailContexts.size());\r\n\r\n        } catch (Exception e) {\r\n            log.error(\"发送BD续约拒绝邮件副本异常，代理ID: {}, 错误: {}\", agentId, e.getMessage());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 构建续约拒绝邮件上下文\r\n     * 参考AgentServiceImpl中的buildRenewalEmailContext方法\r\n     *\r\n     * @param recipientEmail 收件人邮箱\r\n     * @param recipientName 收件人姓名\r\n     * @param agentId 代理ID\r\n     * @param rejectMessage 拒绝原因\r\n     * @param staffId BD员工ID（用于邮件国际化）\r\n     * @return 邮件发送上下文\r\n     */\r\n    private EmailSendContext buildRenewalRejectionEmailContext(String recipientEmail, String recipientName, Long agentId, String rejectMessage, Long staffId) {\r\n        // 新申请拒绝邮件 - 使用加密的appAgentId（逻辑不变）\r\n        String encryptedAgentId;\r\n        try {\r\n            encryptedAgentId = AESUtils.Encrypt(String.valueOf(agentId), AESConstant.AESKEY);\r\n        } catch (Exception e) {\r\n            log.error(\"代理申请ID加密失败\", e);\r\n            throw new GetServiceException(LocaleMessageUtils.getMessage(\"sign_encryption_failed\"));\r\n        }\r\n\r\n\r\n        // 构建邮件参数\r\n        Map<String, String> emailParams = new HashMap<>();\r\n        emailParams.put(\"personalName\", recipientName); // 收件人姓名\r\n        emailParams.put(\"name\", recipientName); // name 和 personalName取同一个值，就是admin的名字\r\n        emailParams.put(\"sign\", encryptedAgentId); // 代理ID\r\n        emailParams.put(\"rejectMessage\", rejectMessage); // 拒绝原因（就是approveComment）\r\n\r\n        // 构建二维码路径（参考AgentServiceImpl的buildRenewalEmailContext方法）\r\n        String qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(URLUtil.encodeAll(encryptedAgentId));\r\n        emailParams.put(\"qrcode\", qrcodePath);\r\n\r\n        // 添加staffId用于邮件国际化\r\n        if (staffId != null) {\r\n            emailParams.put(\"staffId\", staffId.toString());\r\n        }\r\n\r\n        // 构建邮件上下文\r\n        return EmailSendContext.builder()\r\n                .projectKey(ProjectKeyEnum.SALE_CENTER)\r\n                .tableName(TableEnum.SALE_AGENT)\r\n                .tableId(agentId)\r\n                .recipient(recipientEmail)\r\n                .emailTemplate(EmailTemplateEnum.AGENT_RENEWAL_REJECTED_WITH_MSG) // 使用带审批意见的续约拒绝邮件模板\r\n                .parameters(emailParams)\r\n                .build();\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java b/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java
--- a/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java	(revision 31f9e89f66b14f423362dac97fe1fdad95e24d33)
+++ b/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java	(date *************)
@@ -4158,6 +4158,22 @@
             accountDtoList.add(dto);
         }
 
+        // 查询账户附件信息
+        if (CollectionUtil.isNotEmpty(contractAccounts)) {
+            Set<Long> fkTableIds = contractAccounts.stream().map(AgentContractAccount::getId).collect(Collectors.toSet());
+            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService
+                    .getMediaAndAttachedDtoByFkTableIds(TableEnum.AGENT_CONTRACT_ACCOUNT.key, fkTableIds);
+            
+            for (AppAgentContractAccountAddDto dto : accountDtoList) {
+                List<MediaAndAttachedVo> mediaAndAttachedVoList = mediaAndAttachedDtoByFkTableIds
+                        .get(dto.getFkAgentContractAccountId());
+                if (CollectionUtil.isNotEmpty(mediaAndAttachedVoList)) {
+                    dto.setMediaAndAttachedVos(
+                            BeanCopyUtils.copyListProperties(mediaAndAttachedVoList, MediaAndAttachedDto::new));
+                }
+            }
+        }
+
         return accountDtoList;
     }
 
