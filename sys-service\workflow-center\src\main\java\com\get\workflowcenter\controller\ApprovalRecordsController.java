package com.get.workflowcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.dto.BatchApproveDto;
import com.get.workflowcenter.service.ApprovalRecordsService;
import com.get.workflowcenter.vo.ApprovalRecordListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "工作台审批")
@RestController
@RequestMapping("workflow/approvalRecords")
public class ApprovalRecordsController {

    @Resource
    private ApprovalRecordsService approvalRecordsService;

    @ApiOperation(value = "审批列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "工作流中心/我的工作台/审批列表")
    @PostMapping("datas")
    public ResponseBo<ApprovalRecordListVo> datas(@RequestBody SearchBean<ApprovalRecordDto> page) {
        List<ApprovalRecordListVo> approvalRecordListVoList = approvalRecordsService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(approvalRecordListVoList, p);
    }

    @ApiOperation(value = "批量审批")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "工作流中心/我的工作台/批量审批")
    @PostMapping("batchApprove")
    public ResponseBo<ApprovalRecordListVo> batchApprove(@RequestBody List<BatchApproveDto> batchApproveDtoList) {
        approvalRecordsService.batchApprove(batchApproveDtoList);
        return ResponseBo.ok();
    }

}
