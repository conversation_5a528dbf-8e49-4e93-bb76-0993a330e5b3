package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/12/7 15:14
 * @verison: 1.0
 * @description:
 */
@Data
public class EventVo extends BaseEntity {

    /**
     * 活动对象名称
     */
    @ApiModelProperty(value = "活动对象名称")
    private String eventTargetName;

    /**
     * 活动举办国家Name
     */
    @ApiModelProperty(value = "活动举办国家Name")
    private String areaCountryNameHold;

    /**
     * 活动举办州省Name
     */
    @ApiModelProperty(value = "活动举办州省Name")
    private String areaStateNameHold;

    /**
     * 活动举办城市Name
     */
    @ApiModelProperty(value = "活动举办城市Name")
    private String areaCityNameHold;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 活动类型名称
     */
    @ApiModelProperty(value = "活动类型名称")
    private String eventTypeName;

    /**
     * 活动目标对象国家名称
     */
    @ApiModelProperty(value = "活动目标对象国家名称")
    private String eventTargetCountryName;

    /**
     * 活动对象国家(多选)
     */
    @ApiModelProperty(value = "活动对象国家(多选)")
    private List<Long> eventTargetCountryList;

    /**
     * 负责人名字
     */
    @ApiModelProperty(value = "负责人名字")
    private String staffNameLeader1;

    /**
     * 第二负责人名字
     */
    @ApiModelProperty(value = "第二负责人名字")
    private String staffNameLeader2;

    /**
     * 媒体附件
     */
    @ApiModelProperty(value = "媒体附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtoList;

    /**
     * 所属公司名称
     */
    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    //活动数据总览

    /**
     * 活动总次数
     */
    @ApiModelProperty(value = "活动总次数")
    private Integer eventCount;


    /**
     * 总参与人数
     */
    @ApiModelProperty(value = "总参与人数")
    private Integer sumAttendedCount;

    /**
     * 平均参与人数
     */
    @ApiModelProperty(value = "平均参与人数")
    private Double averageAttendedCount;

    /**
     * 总预算费用
     */
    @ApiModelProperty(value = "总预算费用")
    private BigDecimal sumBudgetAmount;

    /**
     * 平均预算费用
     */
    @ApiModelProperty(value = "平均预算费用")
    private BigDecimal averageBudgetAmount;

    /**
     * 总实际费用
     */
    @ApiModelProperty(value = "总实际费用")
    private BigDecimal sumActualAmount;

    /**
     * 平均实际费用
     */
    @ApiModelProperty(value = "平均实际费用")
    private BigDecimal averageActualAmount;

    /**
     * 状态名字
     */
    @ApiModelProperty(value = "状态名字")
    private String statusName;

    /**
     * 查看状态
     */
    @ApiModelProperty(value = "查看状态")
    private Boolean visitStatus;

    /**
     * 显示活动时间
     */
    @ApiModelProperty(value = "显示活动时间")
    private String eventTimeName;

    /**
     * 显示活动时间
     */
    @ApiModelProperty(value = "显示活动时间历史日志，排除当前活动时间")
    private List<String> eventDeferTimeNameList;

    /**
     * 活动费用币种
     */
    @ApiModelProperty(value = "活动费用币种")
    private String eventCostCurrencyTypeNum;

    /**
     * 活动费用币种名称
     */
    @ApiModelProperty(value = "活动费用币种名称")
    private String eventCostCurrencyTypeNumName;

    /**
     * 活动费用
     */
    @ApiModelProperty(value = "活动费用")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal eventCostAmount;

    @ApiModelProperty(value = "活动费用（带币种）")
    private String eventCostAmountCurrency;

    /**
     * 收款币种名称
     */
    @ApiModelProperty(value = "收款币种名称")
    private String receiptCurrencyTypeName;

    /**
     * 收款币种名称
     */
    @ApiModelProperty(value = "收款币种名称")
    private String receiptCurrencyTypeNum;

    /**
     * 折合收款金额
     */
    @ApiModelProperty(value = "折合收款金额")
    @Column(name = "amount_receivable")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amountReceivable;

    @ApiModelProperty(value = "折合收款金额（带币种）")
    private String amountReceivableCurrency;


    @ApiModelProperty("费用收入")
    private BigDecimal amountIncome;

    @ApiModelProperty("费用收入（带币种）")
    private String amountIncomeCurrency;

    @ApiModelProperty("活动全名")
    private String fullName;

    @ApiModelProperty(value = "是否有学校报名名册为待定（false无/true有）")
    private Boolean isEventRegistrationStatus;

    @ApiModelProperty(value = "报名学校数")
    private Long registerCount;

    @ApiModelProperty(value = "BD预算总费用")
    private BigDecimal bdSumAmount;

    @ApiModelProperty(value = "活动费用绑定Id")
    private Long fkEventCostId;

    @ApiModelProperty(value = "分配备注")
    private String eventCostRemark;


    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选")
    private String publicLevel;

    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;


    //==========实体类Event==============
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 活动类型Id
     */
    @ApiModelProperty(value = "活动类型Id")
    @Column(name = "fk_event_type_id")
    private Long fkEventTypeId;
    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    @Column(name = "num")
    private String num;
    /**
     * 活动时间
     */
    @ApiModelProperty(value = "活动时间")
    @Column(name = "event_time")
    @UpdateWithNull
    private Date eventTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @Column(name = "event_time_end")
    @UpdateWithNull
    private Date eventTimeEnd;
    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    @Column(name = "event_theme")
    private String eventTheme;

    @ApiModelProperty(value = "主题补充说明")
    private String eventThemeNote;

    /**
     * 活动目标对象
     */
    @ApiModelProperty(value = "活动目标对象")
    @Column(name = "event_target")
    private String eventTarget;
    /**
     * 活动目标对象负责人
     */
    @ApiModelProperty(value = "活动目标对象负责人")
    @Column(name = "event_target_leader")
    private String eventTargetLeader;
    /**
     * 活动举办国家Id
     */
    @ApiModelProperty(value = "活动举办国家Id")
    @Column(name = "fk_area_country_id_hold")
    private Long fkAreaCountryIdHold;
    /**
     * 活动举办州省Id
     */
    @ApiModelProperty(value = "活动举办州省Id")
    @Column(name = "fk_area_state_id_hold")
    private Long fkAreaStateIdHold;
    /**
     * 活动举办城市Id
     */
    @ApiModelProperty(value = "活动举办城市Id")
    @Column(name = "fk_area_city_id_hold")
    private Long fkAreaCityIdHold;
    /**
     * 员工Id（负责人1）
     */
    @ApiModelProperty(value = "员工Id（负责人1）")
    @Column(name = "fk_staff_id_leader1")
    private Long fkStaffIdLeader1;
    /**
     * 员工Id（负责人2）
     */
    @ApiModelProperty(value = "员工Id（负责人2）")
    @Column(name = "fk_staff_id_leader2")
    private Long fkStaffIdLeader2;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * BD预算场地费用
     */
    @ApiModelProperty(value = "BD预算场地费用")
    @Column(name = "bd_venue_amount")
    private BigDecimal bdVenueAmount;

    /**
     * BD预算餐饮费用
     */
    @ApiModelProperty(value = "BD预算餐饮费用")
    @Column(name = "bd_food_amount")
    private BigDecimal bdFoodAmount;

    /**
     * BD预算奖品费用
     */
    @ApiModelProperty(value = "BD预算奖品费用")
    @Column(name = "bd_prize_amount")
    private BigDecimal bdPrizeAmount;

    /**
     * BD预算其他费用
     */
    @ApiModelProperty(value = "BD预算其他费用")
    @Column(name = "bd_other_amount")
    private BigDecimal bdOtherAmount;

    /**
     * 预算金额
     */
    @ApiModelProperty(value = "预算金额")
    @Column(name = "budget_amount")
    private BigDecimal budgetAmount;
    /**
     * 实际金额
     */
    @ApiModelProperty(value = "实际金额")
    @Column(name = "actual_amount")
    private BigDecimal actualAmount;
    /**
     * 预算人数
     */
    @ApiModelProperty(value = "预算人数")
    @Column(name = "budget_count")
    private Integer budgetCount;
    /**
     * 参加人数
     */
    @ApiModelProperty(value = "参加人数")
    @Column(name = "attended_count")
    private Integer attendedCount;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0计划/1结束/2取消
     */
    @ApiModelProperty(value = "状态：0计划/1结束/2取消")
    @Column(name = "status")
    private Integer status;
}

