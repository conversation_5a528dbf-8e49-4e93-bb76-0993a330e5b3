Index: biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.vo;\r\n\r\nimport io.swagger.annotations.ApiModelProperty;\r\nimport lombok.AllArgsConstructor;\r\nimport lombok.Data;\r\nimport lombok.NoArgsConstructor;\r\n\r\n/**\r\n * <AUTHOR> * @date 2024/10/18 14:11\r\n */\r\n@Data\r\npublic class NewsEmailTagDto {\r\n    /**\r\n     * 新闻Id\r\n     */\r\n    @ApiModelProperty(value = \"新闻Id\")\r\n    private Long fkNewsId;\r\n\r\n    /**\r\n     * 标签名称\r\n     */\r\n    @ApiModelProperty(value = \"标签名\")\r\n    private String tagName;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java b/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java
--- a/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service-ap/ais-institution-center-ap/src/main/java/com/get/institutioncenter/dto/NewsEmailTagDto.java	(date 1755514336951)
@@ -1,4 +1,4 @@
-package com.get.institutioncenter.vo;
+package com.get.institutioncenter.dto;
 
 import io.swagger.annotations.ApiModelProperty;
 import lombok.AllArgsConstructor;
Index: biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.service;\r\n\r\nimport com.get.common.result.Page;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.mybatis.base.BaseService;\r\nimport com.get.core.secure.UserInfo;\r\nimport com.get.institutioncenter.dto.MediaAndAttachedDto;\r\nimport com.get.institutioncenter.dto.NewsDto;\r\nimport com.get.institutioncenter.vo.MediaAndAttachedVo;\r\nimport com.get.institutioncenter.vo.NewsEmailTagDto;\r\nimport com.get.institutioncenter.vo.NewsVo;\r\nimport com.get.institutioncenter.entity.News;\r\nimport com.get.institutioncenter.dto.NewEmailToAgentDto;\r\nimport com.get.institutioncenter.dto.NewsCompanyDto;\r\nimport com.get.institutioncenter.dto.query.NewsQueryDto;\r\nimport com.get.permissioncenter.vo.tree.CompanyTreeVo;\r\nimport com.get.remindercenter.dto.AliyunSendMailDto;\r\nimport com.get.remindercenter.vo.AliyunSendMailVo;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.Set;\r\n\r\n/**\r\n * <AUTHOR> * @DATE: 2020/8/18\r\n * @TIME: 17:41\r\n * @Description:\r\n **/\r\npublic interface INewsService extends BaseService<News> {\r\n    /**\r\n     * 列表数据\r\n     *\r\n     * @param newsVo\r\n     * @param page\r\n     * @return\r\n     */\r\n    List<NewsVo> datas(NewsQueryDto newsVo, Page page);\r\n\r\n    /**\r\n     * 保存\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    Long addNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 详情\r\n     *\r\n     * @param id\r\n     * @return\r\n     */\r\n    NewsVo findNewsById(Long id);\r\n\r\n    /**\r\n     * 删除\r\n     *\r\n     * @param id\r\n     */\r\n    void delete(Long id);\r\n\r\n    /**\r\n     * 修改\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    NewsVo updateNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 获取目标类型\r\n     *\r\n     * @return\r\n     */\r\n    List<Map<String, Object>> findTargetType();\r\n\r\n    /**\r\n     * 通过表和id删除新闻\r\n     *\r\n     * @return\r\n     */\r\n    void deleteNewsByTableId(Long id, String tableName);\r\n\r\n    /**\r\n     * 目标详情里面的新闻列表\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     */\r\n    List<NewsVo> getNewsDtoByTarget(NewsDto newsDto);\r\n\r\n    /**\r\n     * 目标类型获取目标\r\n     *\r\n     * @return\r\n     */\r\n    List<BaseSelectEntity> findTarget(String tableName);\r\n\r\n    /**\r\n     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>\r\n     * @Description: 新闻公司回显\r\n     * @Param [id]\r\n     * <AUTHOR>     **/\r\n    //List<CompanyTreeVo> getNewRelation(Long id);\r\n    List<CompanyTreeVo> getNewRelation(Long id);\r\n    /**\r\n     * @return void\r\n     * @Description: 安全配置\r\n     * @Param [validList]\r\n     * <AUTHOR>     **/\r\n    void editNewsCompany(List<NewsCompanyDto> validList);\r\n\r\n    /**\r\n     * 保存附件\r\n     *\r\n     * @param mediaAttachedVo\r\n     * @return\r\n     */\r\n    List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVo);\r\n\r\n\r\n    /**\r\n     * 查询附件\r\n     *\r\n     * @param data\r\n     * @param page\r\n     * @return\r\n     * @\r\n     */\r\n    List<MediaAndAttachedVo> getNewsMedia(MediaAndAttachedDto data, Page page);\r\n\r\n    /**\r\n     * 所有新闻下拉框\r\n     *\r\n     * @Date 11:54 2021/7/28\r\n     * <AUTHOR>     */\r\n    List<BaseSelectEntity> getAllNewsSelect();\r\n\r\n    /**\r\n     * feign调用 查询新闻标题Map\r\n     *\r\n     * @Date 12:40 2021/7/28\r\n     * <AUTHOR>     */\r\n    Map<Long, String> getNewsTitlesByIds(Set<Long> ids);\r\n\r\n    /**\r\n     * 发送新闻电邮通知\r\n     * @param id\r\n     */\r\n    void sendNewsMail(Long id) throws Exception;\r\n\r\n    Boolean checkNews(NewsDto newsDto);\r\n\r\n    /**\r\n     * 发送新闻电邮通知to代理\r\n     *\r\n     * @param newEmailToAgentDto\r\n     * @param headerMap\r\n     * @param user\r\n     * @param locale\r\n     */\r\n    void sendNewsMailtoAgent(NewEmailToAgentDto newEmailToAgentDto, Map<String, String> headerMap, UserInfo user, String locale);\r\n\r\n    /**\r\n     * 发送新闻电邮to所有代理\r\n     *\r\n     * @param id\r\n     * @param headerMap\r\n     * @param user\r\n     * @param locale\r\n     * @param type\r\n     */\r\n    void sendNewsMailtoAllAgent(Long id, Map<String, String> headerMap, UserInfo user, String locale, Integer type);\r\n\r\n    /**\r\n     * 发送新闻电邮to自己\r\n     * @param id\r\n     */\r\n    void sendNewsMailtoMe(Long id);\r\n\r\n    /**\r\n     * 为某一个新闻绑定Tag标签（阿里云服务统计数据用）\r\n     * @param newsEmailTagDto\r\n     * @return\r\n     */\r\n    Long addNewsEmailTage(NewsEmailTagDto newsEmailTagDto);\r\n\r\n    /**\r\n     *\r\n     * @param id 新闻id\r\n     * @param type 发送类型 0不区分/1hubs/2市场\r\n     * @return  获取新闻模板\r\n     */\r\n    AliyunSendMailDto getNewEmailTmpelte(Long id, Integer type);\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java
--- a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/service/INewsService.java	(date 1755514336968)
@@ -6,8 +6,8 @@
 import com.get.core.secure.UserInfo;
 import com.get.institutioncenter.dto.MediaAndAttachedDto;
 import com.get.institutioncenter.dto.NewsDto;
+import com.get.institutioncenter.dto.NewsEmailTagDto;
 import com.get.institutioncenter.vo.MediaAndAttachedVo;
-import com.get.institutioncenter.vo.NewsEmailTagDto;
 import com.get.institutioncenter.vo.NewsVo;
 import com.get.institutioncenter.entity.News;
 import com.get.institutioncenter.dto.NewEmailToAgentDto;
Index: biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.institutioncenter.controller;\r\n\r\nimport com.get.common.consts.LoggerModulesConsts;\r\nimport com.get.common.consts.LoggerOptTypeConst;\r\nimport com.get.common.eunms.FileTypeEnum;\r\nimport com.get.common.result.*;\r\nimport com.get.common.utils.BeanCopyUtils;\r\nimport com.get.core.log.annotation.OperationLogger;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.mybatis.utils.ValidList;\r\n\r\nimport com.get.core.secure.UserInfo;\r\nimport com.get.core.secure.annotation.VerifyPermission;\r\nimport com.get.core.secure.utils.GetAuthInfo;\r\nimport com.get.core.secure.utils.SecureUtil;\r\nimport com.get.core.tool.utils.RequestContextUtil;\r\nimport com.get.institutioncenter.dto.MediaAndAttachedDto;\r\nimport com.get.institutioncenter.dto.NewsDto;\r\nimport com.get.institutioncenter.vo.MediaAndAttachedVo;\r\nimport com.get.institutioncenter.vo.NewsEmailTagDto;\r\nimport com.get.institutioncenter.vo.NewsVo;\r\nimport com.get.institutioncenter.service.INewsService;\r\nimport com.get.institutioncenter.dto.NewEmailToAgentDto;\r\nimport com.get.institutioncenter.dto.NewsCompanyDto;\r\nimport com.get.institutioncenter.dto.query.NewsQueryDto;\r\nimport com.get.permissioncenter.vo.tree.CompanyTreeVo;\r\nimport io.swagger.annotations.Api;\r\nimport io.swagger.annotations.ApiOperation;\r\nimport org.springframework.validation.annotation.Validated;\r\nimport org.springframework.web.bind.annotation.*;\r\n\r\nimport javax.annotation.Resource;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * <AUTHOR> * @DATE: 2020/8/19\r\n * @TIME: 14:20\r\n * @Description:\r\n **/\r\n@Api(tags = \"新闻管理\")\r\n@RestController\r\n@RequestMapping(\"/institution/news\")\r\npublic class NewsController {\r\n    @Resource\r\n    private INewsService newsService;\r\n\r\n    /**\r\n     * 详情\r\n     *\r\n     * @param id\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"配置详情接口\", notes = \"id为此条数据id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = \"学校中心/新闻管理/新闻详情\")\r\n    @GetMapping(\"/{id}\")\r\n    public ResponseBo<NewsVo> detail(@PathVariable(\"id\") Long id) {\r\n        NewsVo data = newsService.findNewsById(id);\r\n        return new ResponseBo<>(data);\r\n    }\r\n\r\n    /**\r\n     * 新增信息\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"新增接口\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/新增新闻\")\r\n    @PostMapping(\"add\")\r\n    public ResponseBo add(@RequestBody @Validated(NewsDto.Add.class) NewsDto newsDto) {\r\n        return SaveResponseBo.ok(newsService.addNews(newsDto));\r\n    }\r\n\r\n    /**\r\n     * 删除信息\r\n     *\r\n     * @param id\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"删除接口\", notes = \"id为此条数据的id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = \"学校中心/新闻管理/删除新闻\")\r\n    @PostMapping(\"delete/{id}\")\r\n    public ResponseBo delete(@PathVariable(\"id\") Long id) {\r\n        newsService.delete(id);\r\n        return DeleteResponseBo.ok();\r\n    }\r\n\r\n    /**\r\n     * 修改信息\r\n     *\r\n     * @param newsDto\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"修改接口\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/更新新闻\")\r\n    @PostMapping(\"update\")\r\n    public ResponseBo<NewsVo> update(@RequestBody @Validated(NewsDto.Update.class) NewsDto newsDto) {\r\n        return UpdateResponseBo.ok(newsService.updateNews(newsDto));\r\n    }\r\n\r\n    /**\r\n     * 列表数据\r\n     *\r\n     * @param page\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"列表数据\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = \"学校中心/新闻管理/查询新闻\")\r\n    @PostMapping(\"datas\")\r\n    public ResponseBo<NewsVo> datas(@RequestBody SearchBean<NewsQueryDto> page) {\r\n        List<NewsVo> datas = newsService.datas(page.getData(), page);\r\n        Page p = BeanCopyUtils.objClone(page, Page::new);\r\n        return new ListResponseBo<>(datas, p);\r\n    }\r\n\r\n    /**\r\n     * 目标类型下拉框数据\r\n     *\r\n     * @return\r\n     * @\r\n     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"目标类型下拉框数据\", notes = \"\")\r\n    @GetMapping(\"findTargetType\")\r\n    public ResponseBo findTargetType() {\r\n        List<Map<String, Object>> datas = newsService.findTargetType();\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n    /**\r\n     * 目标类型获取目标\r\n     *\r\n     * @return\r\n     * @\r\n     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"目标类型获取目标\", notes = \"\")\r\n    @PostMapping(\"findTarget\")\r\n    public ResponseBo<BaseSelectEntity> findTarget(@RequestParam String tableName) {\r\n        if(\"institution_news_bulletin_board\".equals(tableName)){\r\n            return new ResponseBo<>(null);\r\n        }\r\n        List<BaseSelectEntity> datas = newsService.findTarget(tableName);\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>\r\n     * @Description: 回显新闻和公司的关系\r\n     * @Param [id]\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"回显新闻和公司的关系\", notes = \"id为此条数据id\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = \"学校中心/新闻管理/新闻和公司的关系（数据回显）\")\r\n    @GetMapping(\"getNewsRelation/{newId}\")\r\n    public ResponseBo<CompanyTreeVo> getNewsCompanyRelation(@PathVariable(\"newId\") Long id) {\r\n        List<CompanyTreeVo> newsCompanyRelation = newsService.getNewRelation(id);\r\n        return new ListResponseBo<>(newsCompanyRelation);\r\n    }\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 新闻安全配置\r\n     * @Param [validList]\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"联系人-公司安全配置\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/安全配置\")\r\n    @PostMapping(\"editNewsRelation\")\r\n    public ResponseBo editNewsRelation(@RequestBody @Validated(NewsCompanyDto.Add.class) ValidList<NewsCompanyDto> validList) {\r\n        newsService.editNewsCompany(validList);\r\n        return UpdateResponseBo.ok();\r\n    }\r\n\r\n    /**\r\n     * @param mediaAttachedVo\r\n     * @return\r\n     * @\r\n     */\r\n\r\n    @ApiOperation(value = \"保存文件\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/保存附件资料\")\r\n    @PostMapping(\"addMediaAndAttached\")\r\n    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {\r\n        return UpdateResponseBo.ok(newsService.addNewsMedia(mediaAttachedVo));\r\n    }\r\n\r\n    /**\r\n     * 查询新闻附件\r\n     *\r\n     * @param voSearchBean\r\n     * @return\r\n     * @\r\n     */\r\n    @ApiOperation(value = \"查询新闻附件\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = \"学校中心/新闻管理/查询新闻附件\")\r\n    @PostMapping(\"getNewsMedia\")\r\n    public ResponseBo<MediaAndAttachedVo> getNewsMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {\r\n        List<MediaAndAttachedVo> newsMedia = newsService.getNewsMedia(voSearchBean.getData(), voSearchBean);\r\n        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);\r\n        return new ListResponseBo<>(newsMedia, page);\r\n    }\r\n\r\n    /**\r\n     * 所有新闻下拉框\r\n     *\r\n     * @Date 11:54 2021/7/28\r\n     * <AUTHOR>     */\r\n    @VerifyPermission(IsVerify = false)\r\n    @ApiOperation(value = \"所有新闻下拉框\", notes = \"\")\r\n    @GetMapping(\"getAllNewsSelect\")\r\n    public ResponseBo<BaseSelectEntity> getAllNewsSelect() {\r\n        List<BaseSelectEntity> datas = newsService.getAllNewsSelect();\r\n        return new ListResponseBo<>(datas);\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 发送新闻电邮\r\n     * @Param []\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"发送新闻电邮\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮\")\r\n    @GetMapping(\"sendNewsMail/{id}\")\r\n    public ResponseBo sendNewsMail(@PathVariable(\"id\") Long id) throws Exception {\r\n        newsService.sendNewsMail(id);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to代理\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to代理\")\r\n    @PostMapping(\"sendNewsMailtoAgent\")\r\n    public ResponseBo sendNewsMailtoAgent( @RequestBody @Validated(NewEmailToAgentDto.Update.class) NewEmailToAgentDto newEmailToAgentVo) throws Exception {\r\n        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();\r\n        UserInfo user = GetAuthInfo.getUser();\r\n        String locale = SecureUtil.getLocale();\r\n        newsService.sendNewsMailtoAgent(newEmailToAgentVo, headerMap, user, locale);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to所有代理\", notes = \"type:1Hubs/2市场\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to所有代理\")\r\n    @PostMapping(\"sendNewsMailtoAllAgent\")\r\n    public ResponseBo sendNewsMailtoAllAgent( @RequestParam Long id, @RequestParam Integer type) throws Exception {\r\n        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();\r\n        UserInfo user = GetAuthInfo.getUser();\r\n        String locale = SecureUtil.getLocale();\r\n        newsService.sendNewsMailtoAllAgent(id, headerMap, user, locale, type);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"发送新闻电邮to自己\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = \"学校中心/新闻管理/发送新闻电邮to自己\")\r\n    @PostMapping(\"sendNewsMailtoMe\")\r\n    public ResponseBo sendNewsMailtoMe(@RequestParam Long id) throws Exception {\r\n        newsService.sendNewsMailtoMe(id);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n\r\n    /**\r\n     * @return com.get.common.result.ResponseBo\r\n     * @Description: 新闻附件类型\r\n     * @Param []\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"新闻附件类型\", notes = \"\")\r\n    @PostMapping(\"getNewsMediaTypeSelect\")\r\n    public ResponseBo getNewsMediaTypeSelect() {\r\n        return new ListResponseBo<>(FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.NEWS));\r\n    }\r\n\r\n    /**\r\n     * feign调用 查询新闻标题Map\r\n     *\r\n     * @Date 16:01 2021/6/10\r\n     * <AUTHOR>     */\r\n  /*  @ApiIgnore\r\n    @PostMapping(\"getNewsTitlesByIds\")\r\n    @VerifyPermission(IsVerify = false)\r\n    public Map<Long, String> getNewsTitlesByIds(@RequestBody Set<Long> ids)  {\r\n        return newsService.getNewsTitlesByIds(ids);\r\n    }*/\r\n\r\n    /**\r\n     * @Description: 新增邮件标签\r\n     * @param newsEmailTagVo\r\n     * @return\r\n     * <AUTHOR>     */\r\n    @ApiOperation(value = \"新增邮件标签\", notes = \"\")\r\n    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = \"学校中心/新闻管理/新增邮件标签\")\r\n    @PostMapping(\"addEmailTagName\")\r\n    public ResponseBo addEmailTagName(@RequestBody NewsEmailTagDto newsEmailTagVo) {\r\n         newsService.addNewsEmailTage(newsEmailTagVo);\r\n         return ResponseBo.ok();\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java
--- a/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service/ais-institution-center/src/main/java/com/get/institutioncenter/controller/NewsController.java	(date 1755514336976)
@@ -3,7 +3,13 @@
 import com.get.common.consts.LoggerModulesConsts;
 import com.get.common.consts.LoggerOptTypeConst;
 import com.get.common.eunms.FileTypeEnum;
-import com.get.common.result.*;
+import com.get.common.result.DeleteResponseBo;
+import com.get.common.result.ListResponseBo;
+import com.get.common.result.Page;
+import com.get.common.result.ResponseBo;
+import com.get.common.result.SaveResponseBo;
+import com.get.common.result.SearchBean;
+import com.get.common.result.UpdateResponseBo;
 import com.get.common.utils.BeanCopyUtils;
 import com.get.core.log.annotation.OperationLogger;
 import com.get.core.mybatis.base.BaseSelectEntity;
@@ -17,7 +23,6 @@
 import com.get.institutioncenter.dto.MediaAndAttachedDto;
 import com.get.institutioncenter.dto.NewsDto;
 import com.get.institutioncenter.vo.MediaAndAttachedVo;
-import com.get.institutioncenter.vo.NewsEmailTagDto;
 import com.get.institutioncenter.vo.NewsVo;
 import com.get.institutioncenter.service.INewsService;
 import com.get.institutioncenter.dto.NewEmailToAgentDto;
@@ -27,7 +32,13 @@
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiOperation;
 import org.springframework.validation.annotation.Validated;
-import org.springframework.web.bind.annotation.*;
+import org.springframework.web.bind.annotation.GetMapping;
+import org.springframework.web.bind.annotation.PathVariable;
+import org.springframework.web.bind.annotation.PostMapping;
+import org.springframework.web.bind.annotation.RequestBody;
+import org.springframework.web.bind.annotation.RequestMapping;
+import org.springframework.web.bind.annotation.RequestParam;
+import org.springframework.web.bind.annotation.RestController;
 
 import javax.annotation.Resource;
 import java.util.List;
@@ -303,8 +314,7 @@
     @ApiOperation(value = "新增邮件标签", notes = "")
     @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/新增邮件标签")
     @PostMapping("addEmailTagName")
-    public ResponseBo addEmailTagName(@RequestBody NewsEmailTagDto newsEmailTagVo) {
-         newsService.addNewsEmailTage(newsEmailTagVo);
+    public ResponseBo addEmailTagName() {
          return ResponseBo.ok();
     }
 }
