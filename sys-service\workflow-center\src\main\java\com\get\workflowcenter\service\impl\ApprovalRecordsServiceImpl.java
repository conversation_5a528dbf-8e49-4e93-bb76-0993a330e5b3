package com.get.workflowcenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.dto.BatchApproveDto;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.ApprovalRecordsService;
import com.get.workflowcenter.service.IWorkFlowInstitutionProviderService;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.service.MpayService;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.ApprovalRecordListVo;
import com.get.workflowcenter.vo.AssigneeVo;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ApprovalRecordsServiceImpl implements ApprovalRecordsService {

    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Resource
    private MpayService mpayService;
    @Resource
    private IWorkFlowInstitutionProviderService workflowInstitutionProviderService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Autowired
    private TaskService taskService;
    @Autowired
    private IWorkFlowService iWorkFlowService;


    /**
     * 审批列表
     *
     * @param approvalRecordDto
     * @return
     */
    @Override
    public List<ApprovalRecordListVo> datas(ApprovalRecordDto approvalRecordDto, Page page) {
        IPage<ApprovalRecordListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ApprovalRecordListVo> approvalRecordListVoList = actRuTaskMapper.getApprovalRecordsList(iPage, approvalRecordDto, SecureUtil.getStaffId());
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isNotEmpty(approvalRecordListVoList)) {
            //根据businessKey分组
            Map<String, List<ApprovalRecordListVo>> map = approvalRecordListVoList.stream().collect(Collectors.groupingBy(ApprovalRecordListVo::getBusinessKey));
            //迭代
            for (Map.Entry<String, List<ApprovalRecordListVo>> entry : map.entrySet()) {
                String businessKey = entry.getKey();
                List<ApprovalRecordListVo> list = entry.getValue();
                List<AssigneeVo> assigneeInfo = actRuTaskMapper.getAssigneeInfo(businessKey, list.stream().map(ApprovalRecordListVo::getId).collect(Collectors.toList()));
                for (ApprovalRecordListVo approvalRecordListVo : list) {
                    //查询task版本
                    ActRuTaskVo taskVersionByBusinessKey = mpayService.getTaskDataByBusinessKey(String.valueOf(approvalRecordListVo.getId()), businessKey);
                    if (taskVersionByBusinessKey != null) {
                        approvalRecordListVo.setTaskVersion(taskVersionByBusinessKey.getRev());
                        approvalRecordListVo.setTaskId(taskVersionByBusinessKey.getId());
                        approvalRecordListVo.setDeployId(taskVersionByBusinessKey.getDeployId());
                        approvalRecordListVo.setProcInstId(taskVersionByBusinessKey.getProcInstId());
                        if (GeneralTool.isNotEmpty(taskVersionByBusinessKey.getRev()) || GeneralTool.isNotEmpty(taskVersionByBusinessKey.getId())) {
                            //待修改和上面
                            int signOrGet = workflowInstitutionProviderService.getSignOrGet(taskVersionByBusinessKey.getId(), taskVersionByBusinessKey.getRev());
                            approvalRecordListVo.setSignOrGetStatus(signOrGet);
                        } else {
                            approvalRecordListVo.setSignOrGetStatus(2);
                        }
                    } else {
                        approvalRecordListVo.setSignOrGetStatus(2);

                    }
                    for (AssigneeVo assigneeVo : assigneeInfo) {
                        if (GeneralTool.isNotEmpty(assigneeInfo)) {
                            if (approvalRecordListVo.getId().equals(assigneeVo.getFormId())) {
                                approvalRecordListVo.setCurrentApproverName(assigneeVo.getAssigneeName());
                            }
                        }
                    }
                }


                if (GeneralTool.isNotEmpty(assigneeInfo)) {
                    for (ApprovalRecordListVo approvalRecordListVo : list) {
                        for (AssigneeVo assigneeVo : assigneeInfo) {
                            if (approvalRecordListVo.getId().equals(assigneeVo.getFormId())) {
                                approvalRecordListVo.setCurrentApproverName(assigneeVo.getAssigneeName());
                            }
                        }
                    }
                }
            }

        }
        return approvalRecordListVoList;
    }

    /**
     * 批量审批
     *
     * @param batchApproveDtoList
     */
    @Override
    public void batchApprove(List<BatchApproveDto> batchApproveDtoList) {
        for (BatchApproveDto batchApproveDto : batchApproveDtoList) {
            String taskId = batchApproveDto.getTaskId();
            //没签收的先签收
            if (batchApproveDto.getSignOrGetStatus() == 0) {
                ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(taskId, batchApproveDto.getTaskVersion());
                if (actRuTaskVo != null) {
                    taskService.claim(taskId, String.valueOf(GetAuthInfo.getStaffId()));
                    //签取了任务就把提醒删除
                    reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(taskId));
                }
            }

            //审批
            String approvalStatus = batchApproveDto.getApprovalStatus();
            String username = String.valueOf(GetAuthInfo.getStaffId());
            Authentication.setAuthenticatedUserId(username);
            taskService.addComment(taskId, batchApproveDto.getProcessInstId(), batchApproveDto.getMsg());
            Map<String, Object> map = new HashMap<>();
            map.put("sequenceFlowsStatus", approvalStatus);
            if ("0".equals(approvalStatus)) {
                taskService.setVariableLocal(taskId, "approvalAction", 0);
            } else {
                taskService.setVariableLocal(taskId, "approvalAction", 1);
            }
            taskService.complete(taskId, map);
            iWorkFlowService.getStatusToDoSingle(approvalStatus, batchApproveDto.getProcessInstId());
        }

    }

}
