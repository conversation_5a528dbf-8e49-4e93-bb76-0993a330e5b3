Index: biz-service/ais-sale-center/pom.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\r\n         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\r\n         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\r\n    <parent>\r\n        <artifactId>biz-service</artifactId>\r\n        <groupId>com.get</groupId>\r\n        <version>1.0.RELEASE</version>\r\n    </parent>\r\n    <modelVersion>4.0.0</modelVersion>\r\n\r\n    <artifactId>ais-sale-center</artifactId>\r\n    <name>${project.artifactId}</name>\r\n    <version>${get.project.version}</version>\r\n    <packaging>jar</packaging>\r\n\r\n    <dependencies>\r\n\r\n        <!-- Spring Boot Web -->\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-web</artifactId>\r\n        </dependency>\r\n\r\n        <!-- Thymeleaf 模板 -->\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-thymeleaf</artifactId>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-boot</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-swagger</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.itextpdf</groupId>\r\n            <artifactId>itextpdf</artifactId>\r\n            <version>5.5.13.2</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.itextpdf</groupId>\r\n            <artifactId>itext-asian</artifactId>\r\n            <version>5.2.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.itextpdf.tool</groupId>\r\n            <artifactId>xmlworker</artifactId>\r\n            <version>5.5.13.2</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.apache.xmlgraphics</groupId>\r\n            <artifactId>batik-transcoder</artifactId>\r\n            <version>1.16</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.apache.xmlgraphics</groupId>\r\n            <artifactId>batik-awt-util</artifactId>\r\n            <version>1.16</version>\r\n        </dependency>\r\n\r\n        <!-- XML Graphics 公共库（Batik 依赖） -->\r\n        <dependency>\r\n            <groupId>org.apache.xmlgraphics</groupId>\r\n            <artifactId>xmlgraphics-commons</artifactId>\r\n            <version>2.7</version>\r\n            <exclusions>\r\n                <!-- 排除其他模块引入的旧版本 -->\r\n                <exclusion>\r\n                    <groupId>org.apache.xmlgraphics</groupId>\r\n                    <artifactId>xmlgraphics-commons</artifactId>\r\n                </exclusion>\r\n            </exclusions>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>org.apache.xmlgraphics</groupId>\r\n            <artifactId>batik-codec</artifactId>\r\n            <version>1.16</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-freemarker</artifactId>\r\n            <version>2.3.12.RELEASE</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-sale-center-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>permission-center-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-finance-center-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-auto</artifactId>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-cloud</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-http</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.baomidou</groupId>\r\n            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>file-option-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>workflow-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>file-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <!--        <dependency>-->\r\n        <!--            <groupId>com.get</groupId>-->\r\n        <!--            <artifactId>institution-center-ap</artifactId>-->\r\n        <!--            <version>${get.project.version}</version>-->\r\n        <!--        </dependency>-->\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-reminder-center-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n        </dependency>\r\n        <!--        <dependency>-->\r\n        <!--            <groupId>com.get</groupId>-->\r\n        <!--            <artifactId>platform-config-center-ap</artifactId>-->\r\n        <!--            <version>1.0.RELEASE</version>-->\r\n        <!--            <scope>compile</scope>-->\r\n        <!--        </dependency>-->\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-platform-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>i18n-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-institution-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>io.netty</groupId>\r\n            <artifactId>netty-all</artifactId>\r\n            <version>4.1.33.Final</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.nlpcn</groupId>\r\n            <artifactId>nlp-lang</artifactId>\r\n            <version>1.7.9</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-report-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n\r\n        <!--hutool mail-->\r\n        <dependency>\r\n            <groupId>javax.mail</groupId>\r\n            <artifactId>mail</artifactId>\r\n            <version>1.4.7</version>\r\n        </dependency>\r\n        <!-- springboot 邮件mail -->\r\n        <dependency>\r\n            <groupId>org.springframework.boot</groupId>\r\n            <artifactId>spring-boot-starter-mail</artifactId>\r\n            <version>${spring.boot.version}</version>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>com.belerweb</groupId>\r\n            <artifactId>pinyin4j</artifactId>\r\n            <version>2.5.1</version>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>org.nlpcn</groupId>\r\n            <artifactId>nlp-lang</artifactId>\r\n            <version>1.7.9</version>\r\n        </dependency>\r\n\r\n        <dependency>\r\n            <groupId>com.oracle.database.jdbc</groupId>\r\n            <artifactId>ojdbc8</artifactId>\r\n            <version>21.5.0.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>cn.easyproject</groupId>\r\n            <artifactId>orai18n</artifactId>\r\n            <version>12.1.0.2.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>io.seata</groupId>\r\n            <artifactId>seata-all</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-registration-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.github.binarywang</groupId>\r\n            <artifactId>weixin-java-pay</artifactId>\r\n            <version>4.1.5.B</version>\r\n        </dependency>\r\n        <!--二维码-->\r\n        <dependency>\r\n            <groupId>com.google.zxing</groupId>\r\n            <artifactId>core</artifactId>\r\n            <version>3.0.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.springframework</groupId>\r\n            <artifactId>spring-test</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>me.xdrop</groupId>\r\n            <artifactId>fuzzywuzzy</artifactId>\r\n            <version>1.4.0</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-pmp-center-ap</artifactId>\r\n            <version>1.0.RELEASE</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>ais-partner-center-ap</artifactId>\r\n            <version>${get.project.version}</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n    </dependencies>\r\n\r\n    <build>\r\n        <finalName>${project.name}</finalName>\r\n        <resources>\r\n            <resource>\r\n                <directory>src/main/resources</directory>\r\n            </resource>\r\n            <resource>\r\n                <directory>src/main/java</directory>\r\n                <includes>\r\n                    <include>**/*.xml</include>\r\n                </includes>\r\n            </resource>\r\n        </resources>\r\n        <pluginManagement>\r\n            <plugins>\r\n                <plugin>\r\n                    <groupId>org.springframework.boot</groupId>\r\n                    <artifactId>spring-boot-maven-plugin</artifactId>\r\n                    <version>${spring.boot.version}</version>\r\n                    <configuration>\r\n                        <fork>true</fork>\r\n                        <finalName>${project.build.finalName}</finalName>\r\n                    </configuration>\r\n                    <executions>\r\n                        <execution>\r\n                            <goals>\r\n                                <goal>repackage</goal>\r\n                            </goals>\r\n                        </execution>\r\n                    </executions>\r\n                </plugin>\r\n                <plugin>\r\n                    <groupId>com.spotify</groupId>\r\n                    <artifactId>docker-maven-plugin</artifactId>\r\n                    <version>1.2.2</version>\r\n                    <executions>\r\n                        <execution>\r\n                            <id>build-image</id>\r\n                            <phase>package</phase>\r\n                            <goals>\r\n                                <goal>build</goal>\r\n                            </goals>\r\n                        </execution>\r\n                    </executions>\r\n                    <configuration>\r\n                        <!--打包docker镜像的docker服务器-->\r\n                        <dockerHost>${docker-url}</dockerHost>\r\n                        <!--镜像名，这里用工程名 -->\r\n                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}\r\n                        </imageName>\r\n                        <!--nexus3 hosted 仓库地址-->\r\n                        <registryUrl>${registry-url}</registryUrl>\r\n                        <!-- ca认证正书-->\r\n                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->\r\n                        <!--TAG,这里用工程版本号-->\r\n                        <imageTags>\r\n                            <!-- 指定镜像标签,可以排至多个标签 -->\r\n                            <imageTag>${nexus.version}</imageTag>\r\n                            <imageTag>latest</imageTag>\r\n                        </imageTags>\r\n                        <!--是否强制覆盖已有镜像-->\r\n                        <forceTags>true</forceTags>\r\n                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->\r\n                        <dockerDirectory>biz-service/${project.artifactId}/src/main/docker</dockerDirectory>\r\n                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->\r\n                        <buildArgs>\r\n                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>\r\n                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>\r\n                        </buildArgs>\r\n                        <resources>\r\n                            <resource>\r\n                                <targetPath>/</targetPath>\r\n                                <directory>${project.build.directory}</directory>\r\n                                <include>${project.build.finalName}.jar</include>\r\n                            </resource>\r\n                        </resources>\r\n                        <serverId>nexus-docker-prod</serverId>\r\n                    </configuration>\r\n                </plugin>\r\n                <plugin>\r\n                    <groupId>org.apache.maven.plugins</groupId>\r\n                    <artifactId>maven-antrun-plugin</artifactId>\r\n                    <executions>\r\n                        <execution>\r\n                            <phase>package</phase>\r\n                            <goals>\r\n                                <goal>run</goal>\r\n                            </goals>\r\n                            <configuration>\r\n                                <tasks>\r\n                                    <!--suppress UnresolvedMavenProperty -->\r\n                                    <copy overwrite=\"true\"\r\n                                          tofile=\"${session.executionRootDirectory}/target/${project.artifactId}.jar\"\r\n                                          file=\"${project.build.directory}/${project.artifactId}.jar\"/>\r\n                                </tasks>\r\n                            </configuration>\r\n                        </execution>\r\n                    </executions>\r\n                </plugin>\r\n            </plugins>\r\n        </pluginManagement>\r\n        <plugins>\r\n            <plugin>\r\n                <groupId>com.spotify</groupId>\r\n                <artifactId>dockerfile-maven-plugin</artifactId>\r\n                <configuration>\r\n                    <skip>true</skip>\r\n                </configuration>\r\n            </plugin>\r\n            <plugin>\r\n                <groupId>org.springframework.boot</groupId>\r\n                <artifactId>spring-boot-maven-plugin</artifactId>\r\n            </plugin>\r\n            <plugin>\r\n                <artifactId>maven-compiler-plugin</artifactId>\r\n                <version>${maven.plugin.version}</version>\r\n                <configuration>\r\n                    <source>${java.version}</source>\r\n                    <target>${java.version}</target>\r\n                    <encoding>UTF-8</encoding>\r\n                    <compilerArgs>\r\n                        <arg>-parameters</arg>\r\n                    </compilerArgs>\r\n                </configuration>\r\n            </plugin>\r\n        </plugins>\r\n    </build>\r\n\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-sale-center/pom.xml b/biz-service/ais-sale-center/pom.xml
--- a/biz-service/ais-sale-center/pom.xml	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service/ais-sale-center/pom.xml	(date 1755514337928)
@@ -265,6 +265,11 @@
             <version>${get.project.version}</version>
             <scope>compile</scope>
         </dependency>
+        <dependency>
+            <groupId>junit</groupId>
+            <artifactId>junit</artifactId>
+            <scope>test</scope>
+        </dependency>
     </dependencies>
 
     <build>
Index: common/src/main/java/com/get/common/start/StartServiceImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.common.start;\r\n\r\nimport com.get.common.constant.LauncherStart;\r\nimport com.get.core.auto.service.AutoService;\r\nimport com.get.core.start.constant.AppConstant;\r\nimport com.get.core.start.constant.NacosConstant;\r\nimport com.get.core.start.service.StartService;\r\nimport com.get.core.start.utils.PropsUtil;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.boot.builder.SpringApplicationBuilder;\r\n\r\nimport java.util.Properties;\r\n\r\n@Slf4j\r\n@AutoService(StartService.class)\r\npublic class StartServiceImpl implements StartService {\r\n\r\n    @Override\r\n    public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {\r\n        Properties props = System.getProperties();\r\n\r\n        // 通用注册\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.server-addr\", LauncherStart.nacosAddr(profile));\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.server-addr\", LauncherStart.nacosAddr(profile));\r\n        PropsUtil.setProperty(props, \"spring.cloud.sentinel.transport.dashboard\", LauncherStart.sentinelAddr(profile));\r\n        PropsUtil.setProperty(props, \"spring.zipkin.base-url\", LauncherStart.zipkinAddr(profile));\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.username\", \"nacos\");\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.password\", \"nacos\");\r\n\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].data-id\", NacosConstant.sharedDataId());\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].refresh\", NacosConstant.NACOS_CONFIG_REFRESH);\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].data-id\", NacosConstant.sharedDataId(profile));\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].refresh\", NacosConstant.NACOS_CONFIG_REFRESH);\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);\r\n        PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);\r\n        PropsUtil.setProperty(props,\"spring.cloud.nacos.config.ext-config[0].data-id\",NacosConstant.dataId(appName,profile));\r\n\r\n        if (profile.equals(AppConstant.TEST_CODE)) {\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.namespace\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.namespace\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.group\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.group\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_NAMESPACE_TEST);\r\n        }else if (profile.equals(AppConstant.GRAY_CODE)) {\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.namespace\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.namespace\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.group\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.group\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_NAMESPACE_GRAY);\r\n        }else if (profile.equals(AppConstant.LOCAL_CODE)) {\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.namespace\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.namespace\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.group\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.group\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_NAMESPACE_LOCAL);\r\n        }else{\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.namespace\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.namespace\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.group\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.group\", NacosConstant.NACOS_NAMESPACE_DEV);\r\n        }\r\n\r\n\r\n\r\n//        //如为灰度环境则使用灰度分组\r\n//        if (profile.equals(AppConstant.GRAY_CODE)) {\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_CONFIG_GRAY_GROUP);\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_CONFIG_GRAY_GROUP);\r\n//\r\n////\t\t\tPropsUtil.setProperty(props,\"spring.cloud.nacos.config.ext-config[0].data-id\",NacosConstant.dataId(\"get\",profile));\r\n////\t\t\tPropsUtil.setProperty(props,\"spring.cloud.nacos.config.ext-config[0].group\",NacosConstant.NACOS_CONFIG_GRAY_GROUP);\r\n////\t\t\tPropsUtil.setProperty(props,\"spring.cloud.nacos.config.ext-config[0].refresh\",NacosConstant.NACOS_CONFIG_REFRESH);\r\n//\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.namespace\", \"efcd69b7-ddab-4ca4-8f24-76ce976e135a\");\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.namespace\", \"efcd69b7-ddab-4ca4-8f24-76ce976e135a\");\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.group\", NacosConstant.NACOS_CONFIG_GRAY_GROUP);\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.discovery.group\", NacosConstant.NACOS_CONFIG_GRAY_GROUP);\r\n//        } else {\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[0].group\", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);\r\n//            PropsUtil.setProperty(props, \"spring.cloud.nacos.config.shared-configs[1].group\", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);\r\n//        }\r\n        System.out.println(\"================nacos配置地址：================>\" + LauncherStart.nacosAddr(profile));\r\n        System.out.println(\"================配置信息：================>\" + props.toString());\r\n\r\n\r\n        //生产环境和IAE环境elk写入日志\r\n        if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)) {\r\n            // 开启elk日志\r\n            PropsUtil.setProperty(props, \"get.log.elk.destination\", LauncherStart.elkAddr(profile));\r\n        } else {\r\n            //内网使用简单的file模式，只需要启动seata服务即可\r\n            PropsUtil.setProperty(props, \"seata.registry.type\", \"file\");\r\n            PropsUtil.setProperty(props, \"seata.config.type\", \"file\");\r\n            // seata注册地址\r\n            PropsUtil.setProperty(props, \"seata.service.grouplist.default\", LauncherStart.seataAddr(profile));\r\n            PropsUtil.setProperty(props, \"seata.tx-service-group\", \"get-tx-group\");\r\n            PropsUtil.setProperty(props, \"seata.service.vgroup-mapping.get-tx-group\", \"default\");\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/start/StartServiceImpl.java b/common/src/main/java/com/get/common/start/StartServiceImpl.java
--- a/common/src/main/java/com/get/common/start/StartServiceImpl.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/common/src/main/java/com/get/common/start/StartServiceImpl.java	(date 1755514337994)
@@ -9,14 +9,64 @@
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.boot.builder.SpringApplicationBuilder;
 
+import java.io.InputStream;
+import java.util.Optional;
 import java.util.Properties;
 
 @Slf4j
 @AutoService(StartService.class)
 public class StartServiceImpl implements StartService {
 
+    private String profile;
+
+    private String nacosServerAddr;
+
+    private String nacosConfigServerAddr;
+
+
+    {
+        try {
+            Properties props = System.getProperties();
+            InputStream in = this.getClass().getClassLoader().getResourceAsStream("von.properties");
+            if (in != null) {
+                props.load(in);
+                in.close();
+
+                profile = props.getProperty("von.local.profile");
+                nacosServerAddr = props.getProperty("von.nacos.discovery.server-addr");
+                nacosConfigServerAddr = props.getProperty("von.nacos.config.server-addr");
+
+                log.info("成功从von.properties加载配置");
+
+                props.setProperty("spring.profiles.active", profile);
+                props.setProperty("get.env", profile);
+                if (!profile.equals("prod") && !profile.equals("gray") && !profile.equals("iae") && !profile.equals("tw")) {
+                    props.setProperty("get.dev-mode", "true");
+                } else {
+                    props.setProperty("get.dev-mode", "false");
+                }
+            } else {
+                log.warn("未找到von.properties文件");
+            }
+        } catch (Exception e) {
+            log.error("加载von.properties失败", e);
+        }
+
+        Properties props = System.getProperties();
+        String nacosAddr = Optional.ofNullable(nacosServerAddr).orElse(LauncherStart.nacosAddr(profile));
+        PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.server-addr", nacosAddr);
+        PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr", Optional.of(nacosConfigServerAddr).orElse(LauncherStart.nacosAddr(profile)));
+        PropsUtil.setProperty(props, "mybatis-plus.configuration.log-impl", "org.apache.ibatis.logging.stdout.StdOutImpl");
+    }
+
     @Override
     public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
+        Properties defaultProperties = new Properties();
+        profile = Optional.ofNullable(this.profile).orElse(profile);
+
+        builder.properties(defaultProperties);
+        defaultProperties.setProperty("spring.cloud.nacos.config.shared-configs[1].data-id", NacosConstant.sharedDataId(this.profile));
+        builder.properties(defaultProperties);
         Properties props = System.getProperties();
 
         // 通用注册
@@ -24,8 +74,8 @@
         PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr", LauncherStart.nacosAddr(profile));
         PropsUtil.setProperty(props, "spring.cloud.sentinel.transport.dashboard", LauncherStart.sentinelAddr(profile));
         PropsUtil.setProperty(props, "spring.zipkin.base-url", LauncherStart.zipkinAddr(profile));
-        PropsUtil.setProperty(props, "spring.cloud.nacos.username", "nacos");
-        PropsUtil.setProperty(props, "spring.cloud.nacos.password", "nacos");
+//        PropsUtil.setProperty(props, "spring.cloud.nacos.username", "nacos");
+//        PropsUtil.setProperty(props, "spring.cloud.nacos.password", "nacos");
 
         PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].data-id", NacosConstant.sharedDataId());
         PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].refresh", NacosConstant.NACOS_CONFIG_REFRESH);
Index: common/pom.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\r\n         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\r\n         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\r\n    <parent>\r\n        <artifactId>hti-java-ais</artifactId>\r\n        <groupId>com.get</groupId>\r\n        <version>1.0.RELEASE</version>\r\n    </parent>\r\n    <modelVersion>4.0.0</modelVersion>\r\n\r\n    <artifactId>common</artifactId>\r\n    <name>${project.artifactId}</name>\r\n    <version>${get.project.version}</version>\r\n    <packaging>jar</packaging>\r\n\r\n    <dependencies>\r\n        <!--httpClient -->\r\n        <dependency>\r\n            <groupId>org.apache.httpcomponents</groupId>\r\n            <artifactId>httpclient</artifactId>\r\n            <version>4.5.9</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.apache.httpcomponents</groupId>\r\n            <artifactId>httpmime</artifactId>\r\n            <version>4.5.9</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.alibaba</groupId>\r\n            <artifactId>fastjson</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-start</artifactId>\r\n            <version>${get.project.version}</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-ribbon</artifactId>\r\n            <version>${get.project.version}</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-auto</artifactId>\r\n            <version>${get.project.version}</version>\r\n            <scope>provided</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>org.apache.shiro</groupId>\r\n            <artifactId>shiro-core</artifactId>\r\n            <version>1.3.2</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>net.sf.json-lib</groupId>\r\n            <artifactId>json-lib</artifactId>\r\n            <version>2.4</version>\r\n            <classifier>jdk15</classifier>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>io.swagger</groupId>\r\n            <artifactId>swagger-annotations</artifactId>\r\n            <version>1.5.20</version>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>jakarta.validation</groupId>\r\n            <artifactId>jakarta.validation-api</artifactId>\r\n        </dependency>\r\n        <dependency>\r\n            <groupId>com.get</groupId>\r\n            <artifactId>core-secure</artifactId>\r\n            <version>${get.project.version}</version>\r\n            <scope>compile</scope>\r\n        </dependency>\r\n        <!--        <dependency>-->\r\n<!--            <groupId>com.get</groupId>-->\r\n<!--            <artifactId>core-log</artifactId>-->\r\n<!--        </dependency>-->\r\n        <!--        <dependency>-->\r\n<!--            <groupId>jakarta.validation</groupId>-->\r\n<!--            <artifactId>jakarta.validation-api</artifactId>-->\r\n<!--        </dependency>-->\r\n<!--        <dependency>-->\r\n<!--            <groupId>com.get</groupId>-->\r\n<!--            <artifactId>core-tool</artifactId>-->\r\n<!--        </dependency>-->\r\n        <!--        <dependency>-->\r\n<!--            <groupId>com.get</groupId>-->\r\n<!--            <artifactId>core-log</artifactId>-->\r\n<!--        </dependency>-->\r\n    </dependencies>\r\n\r\n\r\n    <build>\r\n        <plugins>\r\n            <plugin>\r\n                <groupId>org.springframework.boot</groupId>\r\n                <artifactId>spring-boot-maven-plugin</artifactId>\r\n                <configuration>\r\n                    <skip>true</skip>\r\n                    <finalName>${project.name}</finalName>\r\n                </configuration>\r\n            </plugin>\r\n\r\n            <plugin>\r\n                <groupId>com.spotify</groupId>\r\n                <artifactId>docker-maven-plugin</artifactId>\r\n                <version>1.2.2</version>\r\n                <configuration>\r\n                    <skipDockerBuild>true</skipDockerBuild>\r\n                </configuration>\r\n            </plugin>\r\n        </plugins>\r\n    </build>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/pom.xml b/common/pom.xml
--- a/common/pom.xml	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/common/pom.xml	(date 1755514338015)
@@ -15,6 +15,11 @@
     <packaging>jar</packaging>
 
     <dependencies>
+        <dependency>
+            <groupId>com.get</groupId>
+            <artifactId>core-mybatis</artifactId>
+            <version>${get.project.version}</version>
+        </dependency>
         <!--httpClient -->
         <dependency>
             <groupId>org.apache.httpcomponents</groupId>
Index: common/src/main/java/com/get/common/query/service/DynamicQueryBuilder.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/service/DynamicQueryBuilder.java b/common/src/main/java/com/get/common/query/service/DynamicQueryBuilder.java
new file mode 100644
--- /dev/null	(date 1755514337972)
+++ b/common/src/main/java/com/get/common/query/service/DynamicQueryBuilder.java	(date 1755514337972)
@@ -0,0 +1,799 @@
+package com.get.common.query.service;
+
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+import com.get.common.query.annotation.QueryField;
+import com.get.common.query.cache.QueryCacheManager;
+import com.get.common.query.enums.LogicType;
+import com.get.common.query.enums.QueryType;
+import com.get.common.query.exception.*;
+import com.get.common.query.model.FieldInfo;
+import com.get.common.query.monitor.QueryMetricsCollector;
+import com.get.common.query.security.SecurityValidator;
+import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
+import org.springframework.stereotype.Component;
+
+import java.lang.reflect.Field;
+import java.math.BigDecimal;
+import java.time.LocalDate;
+import java.time.LocalDateTime;
+import java.util.*;
+import java.util.stream.Collectors;
+
+/**
+ * 基于注解自动构建MyBatis Plus查询条件，支持复杂查询逻辑
+ * 
+ * 核心特性：
+ * - 支持完整的AND/OR逻辑组合
+ * - 支持复杂的分组查询：(A AND B) OR (C AND D)
+ * - 智能参数验证和类型安全
+ * - 高性能缓存机制
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Component
+@ConditionalOnBean({QueryCacheManager.class, QueryMetricsCollector.class, SecurityValidator.class})
+@RequiredArgsConstructor
+public class DynamicQueryBuilder {
+    
+    private final QueryCacheManager cacheManager;
+    private final QueryMetricsCollector metricsCollector;
+    private final SecurityValidator securityValidator;
+    
+    /**
+     * 根据查询对象动态构建 QueryWrapper
+     *
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类Class
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     * @throws QueryBuildException 构建查询失败时抛出
+     */
+    public <T> QueryWrapper<T> buildQueryWrapper(Object queryDto, Class<T> entityClass) {
+        if (queryDto == null) {
+            log.debug("查询DTO为null，返回空查询条件");
+            return new QueryWrapper<>();
+        }
+        
+        // 开始监控
+        QueryMetricsCollector.QueryExecutionContext context = 
+            metricsCollector.startQuery(queryDto.getClass(), entityClass);
+        
+        try {
+            log.debug("开始构建动态查询 - DTO: {}, Entity: {}", 
+                queryDto.getClass().getSimpleName(), entityClass.getSimpleName());
+            
+            QueryWrapper<T> wrapper = new QueryWrapper<>();
+            
+            // 获取并验证字段信息
+            List<FieldInfo> fieldInfos = getFieldInfos(queryDto.getClass());
+            if (fieldInfos.isEmpty()) {
+                log.debug("未找到任何@QueryField注解字段，返回空查询条件");
+                return wrapper;
+            }
+            
+            // 按优先级排序
+            fieldInfos = sortFieldsByPriority(fieldInfos);
+            
+            // 构建分组查询条件
+            buildGroupedConditions(wrapper, fieldInfos, queryDto, entityClass);
+            
+            log.debug("查询条件构建完成 - 字段数: {}", fieldInfos.size());
+            
+            // 结束监控
+            metricsCollector.endQuery(context, true, null);
+            
+            return wrapper;
+            
+        } catch (Exception e) {
+            // 结束监控（失败）
+            metricsCollector.endQuery(context, false, e.getMessage());
+            throw QueryBuildException.buildFailed(queryDto.getClass(), entityClass, e);
+        }
+    }
+    
+    /**
+     * 按优先级排序字段信息
+     */
+    private List<FieldInfo> sortFieldsByPriority(List<FieldInfo> fieldInfos) {
+        return fieldInfos.stream()
+                .sorted(Comparator.comparingInt(f -> f.getQueryField().priority()))
+                .collect(Collectors.toList());
+    }
+    
+    /**
+     * 按组构建查询条件，支持复杂的分组逻辑
+     * 分组内使用AND连接，分组间使用OR连接
+     */
+    private <T> void buildGroupedConditions(QueryWrapper<T> wrapper, List<FieldInfo> fieldInfos,
+                                           Object queryDto, Class<T> entityClass) {
+        
+        // 按组分类字段
+        Map<String, List<FieldInfo>> groupedFields = fieldInfos.stream()
+                .collect(Collectors.groupingBy(f -> f.getQueryField().group()));
+        
+        log.debug("分组查询条件构建 - 分组数: {}, 分组: {}", 
+            groupedFields.size(), groupedFields.keySet());
+        
+        boolean isFirstGroup = true;
+        
+        for (Map.Entry<String, List<FieldInfo>> entry : groupedFields.entrySet()) {
+            String groupName = entry.getKey();
+            List<FieldInfo> groupFields = entry.getValue();
+            
+            try {
+                // 过滤有效字段（非空值字段）
+                List<FieldInfo> validFields = getValidFields(groupFields, queryDto);
+                
+                if (validFields.isEmpty()) {
+                    log.debug("分组 {} 中没有有效字段，跳过", groupName);
+                    continue;
+                }
+                
+                log.debug("处理分组 {} - 有效字段数: {}", groupName, validFields.size());
+                
+                if ("default".equals(groupName)) {
+                    // 默认组，不分组，按原逻辑处理
+                    buildDefaultGroupConditions(wrapper, validFields, queryDto, entityClass);
+                } else {
+                    // 分组处理
+                    buildNamedGroupConditions(wrapper, validFields, queryDto, entityClass, groupName, isFirstGroup);
+                    isFirstGroup = false;
+                }
+                
+            } catch (Exception e) {
+                throw QueryBuildException.groupBuildFailed(groupName, e);
+            }
+        }
+    }
+    
+    /**
+     * 获取有效字段（非空值字段）
+     * 过滤掉值为空或应被忽略的字段
+     * 
+     * @param groupFields 待过滤的字段信息列表
+     * @param queryDto 查询DTO对象，用于获取字段值
+     * @return 有效的字段信息列表
+     * @throws FieldAccessException 当字段访问失败时抛出
+     */
+    private List<FieldInfo> getValidFields(List<FieldInfo> groupFields, Object queryDto) {
+        return groupFields.stream()
+                .filter(fieldInfo -> {
+                    try {
+                        Object value = fieldInfo.getField().get(queryDto);
+                        return !shouldIgnoreValue(value, fieldInfo.getQueryField());
+                    } catch (IllegalAccessException e) {
+                        throw FieldAccessException.groupAccessFailed(fieldInfo.getField(), 
+                            fieldInfo.getQueryField().group(), e);
+                    }
+                })
+                .collect(Collectors.toList());
+    }
+    
+    /**
+     * 构建默认组查询条件
+     * 默认组中，字段按照各自的logic属性进行连接
+     * 
+     * @param wrapper QueryWrapper实例
+     * @param validFields 有效字段列表
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类
+     */
+    private <T> void buildDefaultGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> validFields,
+                                                Object queryDto, Class<T> entityClass) {
+        for (FieldInfo fieldInfo : validFields) {
+            buildSingleCondition(wrapper, fieldInfo, queryDto);
+        }
+    }
+    
+    /**
+     * 构建命名分组查询条件
+     */
+    private <T> void buildNamedGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> validFields,
+                                              Object queryDto, Class<T> entityClass, String groupName, boolean isFirstGroup) {
+        if (!isFirstGroup) {
+            // 非第一组，使用OR连接
+            wrapper.or(nestedWrapper -> {
+                buildGroupConditions(nestedWrapper, validFields, queryDto);
+            });
+        } else {
+            // 第一组，直接添加条件
+            buildGroupConditions(wrapper, validFields, queryDto);
+        }
+    }
+    
+    /**
+     * 构建组内查询条件（组内强制使用AND连接）
+     * 在分组查询中，忽略字段的logic属性，组内所有条件都使用AND连接
+     * 
+     * @param wrapper QueryWrapper实例
+     * @param fieldInfos 字段信息列表
+     * @param queryDto 查询DTO对象
+     */
+    private <T> void buildGroupConditions(QueryWrapper<T> wrapper, List<FieldInfo> fieldInfos, Object queryDto) {
+        for (FieldInfo fieldInfo : fieldInfos) {
+            // 在分组查询中，强制使用AND逻辑，忽略字段的logic属性
+            buildSingleConditionWithFixedLogic(wrapper, fieldInfo, queryDto, LogicType.AND);
+        }
+    }
+    
+    /**
+     * 构建单个字段的查询条件（使用字段自身的logic属性）
+     */
+    private <T> void buildSingleCondition(QueryWrapper<T> wrapper, FieldInfo fieldInfo, Object queryDto) {
+        LogicType logicType = fieldInfo.getQueryField().logic();
+        buildSingleConditionWithFixedLogic(wrapper, fieldInfo, queryDto, logicType);
+    }
+    
+    /**
+     * 构建单个字段的查询条件（使用指定的逻辑类型）
+     * 包含参数安全验证和转义处理
+     * 
+     * @param wrapper QueryWrapper实例
+     * @param fieldInfo 字段信息
+     * @param queryDto 查询DTO对象
+     * @param forceLogicType 强制使用的逻辑类型
+     */
+    private <T> void buildSingleConditionWithFixedLogic(QueryWrapper<T> wrapper, FieldInfo fieldInfo, 
+                                                        Object queryDto, LogicType forceLogicType) {
+        try {
+            Object value = fieldInfo.getField().get(queryDto);
+            
+            if (shouldIgnoreValue(value, fieldInfo.getQueryField())) {
+                return;
+            }
+            
+            // 参数安全验证
+            securityValidator.validateParameterValue(value);
+            
+            QueryType queryType = fieldInfo.getQueryField().type();
+            String columnName = fieldInfo.getColumnName();
+            
+            // 验证查询类型与参数值的兼容性
+            securityValidator.validateQueryTypeCompatibility(queryType, value);
+            
+            // 根据字段类型进行特定验证
+            performTypeSpecificValidation(value, fieldInfo);
+            
+            // 对LIKE查询进行特殊的转义处理
+            Object processedValue = value;
+            if (isLikeQuery(queryType) && value instanceof String) {
+                processedValue = securityValidator.escapeLikeValue((String) value);
+                log.debug("LIKE查询值已转义 - 原值: {}, 转义后: {}", value, processedValue);
+            }
+            
+            log.debug("构建查询条件 - 字段: {}, 类型: {}, 逻辑: {}, 值: {}", 
+                fieldInfo.getField().getName(), queryType, forceLogicType, 
+                processedValue.toString().length() > 100 ? 
+                processedValue.toString().substring(0, 100) + "..." : processedValue);
+            
+            buildConditionByType(wrapper, queryType, columnName, processedValue, fieldInfo, forceLogicType);
+            
+        } catch (IllegalAccessException e) {
+            throw FieldAccessException.accessFailed(fieldInfo.getField(), "构建查询条件", e);
+        }
+    }
+    
+    /**
+     * 判断是否为LIKE类型的查询
+     */
+    private boolean isLikeQuery(QueryType queryType) {
+        return queryType == QueryType.LIKE || 
+               queryType == QueryType.LIKE_LEFT || 
+               queryType == QueryType.LIKE_RIGHT;
+    }
+    
+    /**
+     * 根据查询类型和逻辑类型构建具体的查询条件
+     */
+    private <T> void buildConditionByType(QueryWrapper<T> wrapper, QueryType queryType,
+                                         String columnName, Object value, FieldInfo fieldInfo, LogicType logicType) {
+        
+        boolean useOr = (logicType == LogicType.OR);
+        
+        switch (queryType) {
+            case EQ:
+                applyCondition(wrapper, useOr, w -> w.eq(columnName, value));
+                break;
+            case NE:
+                applyCondition(wrapper, useOr, w -> w.ne(columnName, value));
+                break;
+            case LIKE:
+                applyCondition(wrapper, useOr, w -> w.like(columnName, value));
+                break;
+            case LIKE_LEFT:
+                applyCondition(wrapper, useOr, w -> w.likeLeft(columnName, value));
+                break;
+            case LIKE_RIGHT:
+                applyCondition(wrapper, useOr, w -> w.likeRight(columnName, value));
+                break;
+            case GT:
+                applyCondition(wrapper, useOr, w -> w.gt(columnName, value));
+                break;
+            case GE:
+                applyCondition(wrapper, useOr, w -> w.ge(columnName, value));
+                break;
+            case LT:
+                applyCondition(wrapper, useOr, w -> w.lt(columnName, value));
+                break;
+            case LE:
+                applyCondition(wrapper, useOr, w -> w.le(columnName, value));
+                break;
+            case IN:
+                handleCollectionQuery(wrapper, columnName, value, useOr, true, fieldInfo);
+                break;
+            case NOT_IN:
+                handleCollectionQuery(wrapper, columnName, value, useOr, false, fieldInfo);
+                break;
+            case IS_NULL:
+                applyCondition(wrapper, useOr, w -> w.isNull(columnName));
+                break;
+            case IS_NOT_NULL:
+                applyCondition(wrapper, useOr, w -> w.isNotNull(columnName));
+                break;
+            case BETWEEN:
+                handleRangeQuery(wrapper, columnName, value, useOr, true, fieldInfo);
+                break;
+            case NOT_BETWEEN:
+                handleRangeQuery(wrapper, columnName, value, useOr, false, fieldInfo);
+                break;
+            case ORDER_BY:
+                handleOrderBy(wrapper, columnName, fieldInfo);
+                break;
+            case IGNORE:
+                // 忽略该字段，不做任何处理
+                log.debug("忽略字段: {}", fieldInfo.getField().getName());
+                break;
+            default:
+                throw QueryTypeNotSupportedException.unsupported(queryType, 
+                    fieldInfo.getField().getName(), columnName);
+        }
+    }
+    
+    /**
+     * 应用查询条件（处理AND/OR逻辑）
+     * 修复AND逻辑处理，确保逻辑连接符的正确应用
+     * 
+     * @param wrapper QueryWrapper实例
+     * @param useOr 是否使用OR逻辑，false表示使用AND逻辑
+     * @param conditionApplier 条件应用函数
+     */
+    private <T> void applyCondition(QueryWrapper<T> wrapper, boolean useOr, 
+                                   java.util.function.Consumer<QueryWrapper<T>> conditionApplier) {
+        // 检查是否已有查询条件
+        boolean hasExistingConditions = !wrapper.getExpression().getNormal().isEmpty();
+        
+        if (hasExistingConditions && useOr) {
+            // 只有在需要OR逻辑且已有条件时才调用or()
+            // MyBatis Plus默认使用AND连接，所以不需要显式调用and()
+            wrapper.or();
+        }
+        // 对于AND逻辑或者没有现有条件的情况，直接添加新条件
+        // MyBatis Plus会自动使用AND连接多个条件
+        
+        conditionApplier.accept(wrapper);
+    }
+    
+    /**
+     * 处理集合类型查询（IN/NOT_IN）
+     * 包含集合参数的安全验证
+     */
+    private <T> void handleCollectionQuery(QueryWrapper<T> wrapper, String columnName, Object value, 
+                                          boolean useOr, boolean isIn, FieldInfo fieldInfo) {
+        if (!(value instanceof Collection)) {
+            throw ParameterValidationException.invalidCollectionType(
+                isIn ? QueryType.IN : QueryType.NOT_IN, value);
+        }
+        
+        Collection<?> collection = (Collection<?>) value;
+        if (collection.isEmpty()) {
+            log.debug("{}查询的集合为空，忽略该条件 - 字段: {}", 
+                isIn ? "IN" : "NOT_IN", fieldInfo.getField().getName());
+            return;
+        }
+        
+        // 集合参数安全验证（限制集合大小为1000）
+        securityValidator.validateCollectionParameter(collection, 1000);
+        
+        log.debug("集合查询参数验证通过 - 类型: {}, 大小: {}, 字段: {}", 
+            isIn ? "IN" : "NOT_IN", collection.size(), fieldInfo.getField().getName());
+        
+        if (isIn) {
+            applyCondition(wrapper, useOr, w -> w.in(columnName, collection));
+        } else {
+            applyCondition(wrapper, useOr, w -> w.notIn(columnName, collection));
+        }
+    }
+    
+    /**
+     * 处理范围类型查询（BETWEEN/NOT_BETWEEN）
+     */
+    private <T> void handleRangeQuery(QueryWrapper<T> wrapper, String columnName, Object value, 
+                                     boolean useOr, boolean isBetween, FieldInfo fieldInfo) {
+        if (!(value instanceof List)) {
+            throw ParameterValidationException.invalidRangeType(
+                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, value);
+        }
+        
+        List<?> range = (List<?>) value;
+        if (range.size() != 2) {
+            throw ParameterValidationException.invalidRangeSize(
+                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, range.size());
+        }
+        
+        Object start = range.get(0);
+        Object end = range.get(1);
+        
+        if (start == null || end == null) {
+            throw ParameterValidationException.rangeContainsNull(
+                isBetween ? QueryType.BETWEEN : QueryType.NOT_BETWEEN, start, end);
+        }
+        
+        // 验证和修正范围值顺序
+        RangeValues rangeValues = validateAndFixRange(start, end);
+        
+        if (isBetween) {
+            applyCondition(wrapper, useOr, w -> w.between(columnName, rangeValues.getStart(), rangeValues.getEnd()));
+        } else {
+            applyCondition(wrapper, useOr, w -> w.notBetween(columnName, rangeValues.getStart(), rangeValues.getEnd()));
+        }
+    }
+    
+    /**
+     * 处理排序条件
+     * 根据字段注解中的asc属性添加升序或降序排序
+     * 
+     * @param wrapper QueryWrapper实例
+     * @param columnName 数据库字段名
+     * @param fieldInfo 字段信息，包含排序方向配置
+     */
+    private <T> void handleOrderBy(QueryWrapper<T> wrapper, String columnName, FieldInfo fieldInfo) {
+        if (fieldInfo.getQueryField().asc()) {
+            wrapper.orderByAsc(columnName);
+            log.debug("添加升序排序 - 字段: {}", columnName);
+        } else {
+            wrapper.orderByDesc(columnName);
+            log.debug("添加降序排序 - 字段: {}", columnName);
+        }
+    }
+    
+    /**
+     * 验证并修正范围值的顺序
+     * 确保范围查询的起始值小于等于结束值，如果顺序错误则自动交换
+     * 
+     * @param start 范围开始值
+     * @param end 范围结束值
+     * @return 修正后的范围值对象
+     * @throws ParameterValidationException 当范围值类型不兼容时抛出
+     */
+    private RangeValues validateAndFixRange(Object start, Object end) {
+        try {
+            if (start instanceof Comparable && end instanceof Comparable && 
+                start.getClass().equals(end.getClass())) {
+                
+                @SuppressWarnings("unchecked")
+                Comparable<Object> startComp = (Comparable<Object>) start;
+                
+                if (startComp.compareTo(end) <= 0) {
+                    return new RangeValues(start, end);
+                } else {
+                    log.warn("范围查询值顺序错误: [{}, {}]，已自动交换", start, end);
+                    return new RangeValues(end, start);
+                }
+            }
+        } catch (ClassCastException e) {
+            throw ParameterValidationException.incompatibleRangeTypes(start, end);
+        }
+        
+        // 无法比较时，保持原顺序
+        return new RangeValues(start, end);
+    }
+    
+    /**
+     * 判断是否应该忽略该值
+     * 根据QueryField注解的ignoreEmpty属性和值的实际情况判断是否应该忽略该字段
+     * 
+     * @param value 字段值
+     * @param queryField 查询字段注解
+     * @return true表示应该忽略该值，false表示不应忽略
+     */
+    private boolean shouldIgnoreValue(Object value, QueryField queryField) {
+        if (!queryField.ignoreEmpty()) {
+            return false;
+        }
+        
+        if (value == null) {
+            return true;
+        }
+        
+        if (value instanceof String && ((String) value).trim().isEmpty()) {
+            return true;
+        }
+        
+        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
+            return true;
+        }
+        
+        if (value.getClass().isArray()) {
+            return java.lang.reflect.Array.getLength(value) == 0;
+        }
+        
+        return false;
+    }
+    
+    /**
+     * 获取字段信息（带缓存）
+     */
+    private List<FieldInfo> getFieldInfos(Class<?> clazz) {
+        List<FieldInfo> fieldInfos = cacheManager.getFieldInfos(clazz);
+        if (fieldInfos == null) {
+            // 缓存未命中
+            metricsCollector.recordCacheMiss(clazz);
+            fieldInfos = parseFieldInfos(clazz);
+            cacheManager.cacheFieldInfos(clazz, fieldInfos);
+        } else {
+            // 缓存命中
+            metricsCollector.recordCacheHit(clazz);
+        }
+        return fieldInfos;
+    }
+    
+    /**
+     * 解析字段信息
+     * 包含安全验证，确保字段名符合安全要求
+     */
+    private List<FieldInfo> parseFieldInfos(Class<?> clazz) {
+        List<FieldInfo> fieldInfos = new ArrayList<>();
+        List<Field> allFields = getAllFields(clazz);
+        
+        for (Field field : allFields) {
+            QueryField queryField = field.getAnnotation(QueryField.class);
+            if (queryField != null) {
+                field.setAccessible(true);
+                
+                // 创建字段信息对象
+                FieldInfo fieldInfo = new FieldInfo(field, queryField);
+                
+                // 验证字段名的安全性
+                try {
+                    securityValidator.validateColumnName(fieldInfo.getColumnName());
+                    fieldInfos.add(fieldInfo);
+                    log.debug("字段安全验证通过 - 字段: {}, 数据库字段: {}", 
+                        field.getName(), fieldInfo.getColumnName());
+                } catch (Exception e) {
+                    log.error("字段安全验证失败 - 字段: {}, 数据库字段: {}, 错误: {}", 
+                        field.getName(), fieldInfo.getColumnName(), e.getMessage());
+                    throw new QueryBuildException(
+                        String.format("字段安全验证失败 - %s.%s: %s", 
+                            clazz.getSimpleName(), field.getName(), e.getMessage()), e);
+                }
+            }
+        }
+        
+        log.debug("解析字段信息完成 - 类: {}, 总字段数: {}, 注解字段数: {}", 
+            clazz.getSimpleName(), allFields.size(), fieldInfos.size());
+        
+        return fieldInfos;
+    }
+    
+    /**
+     * 获取类的所有字段，包括父类字段
+     */
+    private List<Field> getAllFields(Class<?> clazz) {
+        List<Field> fields = new ArrayList<>();
+        
+        while (clazz != null && clazz != Object.class) {
+            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
+            clazz = clazz.getSuperclass();
+        }
+        
+        return fields;
+    }
+    
+    
+    /**
+     * 清空缓存
+     */
+    public void clearCache() {
+        cacheManager.clearAll();
+    }
+    
+    /**
+     * 获取缓存统计信息
+     */
+    public Map<String, Object> getCacheStats() {
+        return cacheManager.getCacheStats();
+    }
+    
+    /**
+     * 获取详细的诊断信息
+     */
+    public Map<String, Object> getDiagnosticInfo(Class<?> queryDtoClass) {
+        Map<String, Object> diagnostic = new HashMap<>();
+        
+        try {
+            List<FieldInfo> fieldInfos = getFieldInfos(queryDtoClass);
+            
+            diagnostic.put("className", queryDtoClass.getSimpleName());
+            diagnostic.put("totalFields", fieldInfos.size());
+            
+            // 按查询类型统计
+            Map<QueryType, Long> typeStats = fieldInfos.stream()
+                    .collect(Collectors.groupingBy(f -> f.getQueryField().type(), Collectors.counting()));
+            diagnostic.put("queryTypeStats", typeStats);
+            
+            // 按逻辑类型统计
+            Map<LogicType, Long> logicStats = fieldInfos.stream()
+                    .collect(Collectors.groupingBy(f -> f.getQueryField().logic(), Collectors.counting()));
+            diagnostic.put("logicTypeStats", logicStats);
+            
+            // 按分组统计
+            Map<String, Long> groupStats = fieldInfos.stream()
+                    .collect(Collectors.groupingBy(f -> f.getQueryField().group(), Collectors.counting()));
+            diagnostic.put("groupStats", groupStats);
+            
+            // 字段详情
+            List<Map<String, Object>> fieldDetails = fieldInfos.stream().map(fieldInfo -> {
+                Map<String, Object> detail = new HashMap<>();
+                detail.put("fieldName", fieldInfo.getField().getName());
+                detail.put("fieldType", fieldInfo.getFieldType().getSimpleName());
+                detail.put("columnName", fieldInfo.getColumnName());
+                detail.put("queryType", fieldInfo.getQueryField().type());
+                detail.put("logicType", fieldInfo.getQueryField().logic());
+                detail.put("group", fieldInfo.getQueryField().group());
+                detail.put("priority", fieldInfo.getQueryField().priority());
+                detail.put("ignoreEmpty", fieldInfo.getQueryField().ignoreEmpty());
+                detail.put("isCollection", fieldInfo.isCollection());
+                return detail;
+            }).collect(Collectors.toList());
+            diagnostic.put("fieldDetails", fieldDetails);
+            
+            // 添加缓存信息
+            diagnostic.put("cacheInfo", cacheManager.getCacheStats());
+            diagnostic.put("cacheHealth", cacheManager.getHealthStatus());
+            
+        } catch (Exception e) {
+            diagnostic.put("error", e.getMessage());
+            log.error("获取诊断信息失败: {}", queryDtoClass.getSimpleName(), e);
+        }
+        
+        return diagnostic;
+    }
+    
+    /**
+     * 范围值封装类
+     * 用于封装BETWEEN查询的开始值和结束值
+     * 
+     * <AUTHOR>
+     * @since 2025-01-30
+     */
+    private static class RangeValues {
+        private final Object start;
+        private final Object end;
+        
+        /**
+         * 构造函数
+         * 
+         * @param start 范围开始值
+         * @param end 范围结束值
+         */
+        public RangeValues(Object start, Object end) {
+            this.start = start;
+            this.end = end;
+        }
+        
+        /**
+         * 获取范围开始值
+         * 
+         * @return 开始值
+         */
+        public Object getStart() {
+            return start;
+        }
+        
+        /**
+         * 获取范围结束值
+         * 
+         * @return 结束值
+         */
+        public Object getEnd() {
+            return end;
+        }
+    }
+    
+    /**
+     * 根据字段类型执行特定的参数验证
+     * 
+     * @param value 参数值
+     * @param fieldInfo 字段信息
+     */
+    private void performTypeSpecificValidation(Object value, FieldInfo fieldInfo) {
+        if (value == null) {
+            return;
+        }
+        
+        Class<?> fieldType = fieldInfo.getFieldType();
+        QueryType queryType = fieldInfo.getQueryField().type();
+        
+        try {
+            // 数字类型验证
+            if (Number.class.isAssignableFrom(fieldType) || fieldType.isPrimitive()) {
+                if (fieldType == int.class || fieldType == Integer.class) {
+                    securityValidator.validateNumericParameter(value, Integer.MIN_VALUE, Integer.MAX_VALUE);
+                } else if (fieldType == long.class || fieldType == Long.class) {
+                    securityValidator.validateNumericParameter(value, Long.MIN_VALUE, Long.MAX_VALUE);
+                } else if (fieldType == double.class || fieldType == Double.class ||
+                          fieldType == float.class || fieldType == Float.class) {
+                    securityValidator.validateNumericParameter(value, null, null); // 不限制范围，只检查NaN和无穷大
+                } else if (fieldType == BigDecimal.class) {
+                    securityValidator.validateNumericParameter(value, null, null);
+                }
+            }
+            // 日期类型验证
+            else if (LocalDate.class.isAssignableFrom(fieldType) || 
+                     LocalDateTime.class.isAssignableFrom(fieldType) ||
+                     java.util.Date.class.isAssignableFrom(fieldType)) {
+                // 默认允许过去和将来的日期，具体业务可通过自定义验证器进一步限制
+                securityValidator.validateDateParameter(value, true, true);
+            }
+            // 字符串类型验证
+            else if (String.class == fieldType) {
+                String stringValue = (String) value;
+                
+                // 如果是LIKE查询，允许通配符
+                if (isLikeQuery(queryType)) {
+                    // LIKE查询允许 % 和 _ 通配符
+                    Set<Character> allowedChars = new HashSet<>();
+                    allowedChars.add('%');
+                    allowedChars.add('_');
+                    allowedChars.add('-'); // 连字符
+                    allowedChars.add('.');
+                    allowedChars.add('@'); // 邮箱
+                    allowedChars.add('(');
+                    allowedChars.add(')');
+                    allowedChars.add('+');
+                    
+                    securityValidator.validateStringSpecialChars(stringValue, allowedChars);
+                } else {
+                    // 非LIKE查询，限制特殊字符
+                    Set<Character> allowedChars = new HashSet<>();
+                    allowedChars.add('-'); // 连字符
+                    allowedChars.add('.');
+                    allowedChars.add('@'); // 邮箱
+                    allowedChars.add('(');
+                    allowedChars.add(')');
+                    allowedChars.add('+');
+                    allowedChars.add(',');
+                    allowedChars.add(':');
+                    
+                    securityValidator.validateStringSpecialChars(stringValue, allowedChars);
+                }
+            }
+            // 集合类型验证（IN/NOT_IN查询已在其他地方验证）
+            else if (java.util.Collection.class.isAssignableFrom(fieldType)) {
+                if (queryType == QueryType.IN || queryType == QueryType.NOT_IN) {
+                    Collection<?> collection = (Collection<?>) value;
+                    // 递归验证集合中的每个元素
+                    for (Object item : collection) {
+                        if (item != null) {
+                            securityValidator.validateParameterValue(item);
+                        }
+                    }
+                }
+            }
+            
+            log.debug("字段类型特定验证通过 - 字段: {}, 类型: {}, 查询类型: {}", 
+                fieldInfo.getField().getName(), fieldType.getSimpleName(), queryType);
+                
+        } catch (Exception e) {
+            log.error("字段类型特定验证失败 - 字段: {}, 类型: {}, 查询类型: {}, 错误: {}", 
+                fieldInfo.getField().getName(), fieldType.getSimpleName(), queryType, e.getMessage());
+            throw new QueryBuildException(
+                String.format("字段 %s 的类型特定验证失败: %s", 
+                    fieldInfo.getField().getName(), e.getMessage()), e);
+        }
+    }
+}
\ No newline at end of file
Index: biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/EncryptionKeyUtils.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.insurancecenter.utils;\r\n\r\nimport cn.hutool.http.HttpUtil;\r\nimport com.alibaba.fastjson.JSONObject;\r\nimport com.alibaba.nacos.common.utils.StringUtils;\r\nimport com.common.core.util.EncoderUtil;\r\nimport com.get.insurancecenter.config.EncryptionConfig;\r\nimport com.get.insurancecenter.vo.encryption.EncryptionResult;\r\nimport io.jsonwebtoken.Claims;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.cache.annotation.CacheEvict;\r\nimport org.springframework.cache.annotation.Cacheable;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.security.Key;\r\nimport java.util.Objects;\r\n\r\n\r\n/**\r\n * @Author:Oliver\r\n * @Date: 2025/8/6\r\n * @Version 1.0\r\n * @apiNote:加密密钥工具类\r\n */\r\n@Component\r\n@Slf4j\r\npublic class EncryptionKeyUtils {\r\n\r\n    @Autowired\r\n    private EncryptionConfig encryptionConfig;\r\n\r\n    /**\r\n     * 获取密钥（缓存 2 小时，Spring 自动管理）\r\n     */\r\n    @Cacheable(value = \"secretCache\", key = \"'secret'\")\r\n    public String getSecret() {\r\n        log.info(\"【密钥】缓存未命中，开始请求远程密钥...\");\r\n        return fetchSecretFromRemote();\r\n    }\r\n\r\n    /**\r\n     * 主动刷新密钥缓存（使用 CacheManager 清除缓存 + 强制更新）\r\n     */\r\n    @CacheEvict(value = \"secretCache\", key = \"'secret'\")\r\n    public String refreshSecret() {\r\n        log.info(\"【密钥】主动刷新缓存，清除原缓存...\");\r\n        return getSecret(); // 清除后重新走 @Cacheable 流程\r\n    }\r\n\r\n    /**\r\n     * 实际从远程服务拉取密钥\r\n     */\r\n    private String fetchSecretFromRemote() {\r\n        log.info(\"请求远程获取密钥，host:{}\", encryptionConfig.getHost());\r\n        String resultStr = HttpUtil.get(encryptionConfig.getHost());\r\n        log.info(\"获取密钥结果:{}\", resultStr);\r\n\r\n        EncryptionResult encryptionResult = JSONObject.parseObject(resultStr, EncryptionResult.class);\r\n        if (!Boolean.TRUE.equals(encryptionResult.getSuccess())) {\r\n            log.error(\"远程密钥获取失败: {}\", resultStr);\r\n            throw new RuntimeException(\"远程密钥获取失败\");\r\n        }\r\n        if (StringUtils.isBlank(encryptionResult.getKey())) {\r\n            log.error(\"密钥内容为空: {}\", resultStr);\r\n            throw new RuntimeException(\"密钥内容为空\");\r\n        }\r\n        return phaseKey(encryptionResult);\r\n    }\r\n\r\n    /**\r\n     * 解析密钥 token，提取 key 字段\r\n     */\r\n    private String phaseKey(EncryptionResult encryptionResult) {\r\n        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());\r\n        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);\r\n        Object keyObj = claims.get(\"key\");\r\n        if (Objects.isNull(keyObj)) {\r\n            log.error(\"Token 中不包含 key 字段\");\r\n            throw new IllegalArgumentException(\"Token 中不包含 key 字段\");\r\n        }\r\n        return keyObj.toString();\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/EncryptionKeyUtils.java b/biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/EncryptionKeyUtils.java
--- a/biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/EncryptionKeyUtils.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/EncryptionKeyUtils.java	(date 1755514338069)
@@ -3,19 +3,14 @@
 import cn.hutool.http.HttpUtil;
 import com.alibaba.fastjson.JSONObject;
 import com.alibaba.nacos.common.utils.StringUtils;
-import com.common.core.util.EncoderUtil;
 import com.get.insurancecenter.config.EncryptionConfig;
 import com.get.insurancecenter.vo.encryption.EncryptionResult;
-import io.jsonwebtoken.Claims;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.cache.annotation.CacheEvict;
 import org.springframework.cache.annotation.Cacheable;
 import org.springframework.stereotype.Component;
 
-import java.security.Key;
-import java.util.Objects;
-
 
 /**
  * @Author:Oliver
@@ -72,13 +67,14 @@
      * 解析密钥 token，提取 key 字段
      */
     private String phaseKey(EncryptionResult encryptionResult) {
-        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());
-        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);
-        Object keyObj = claims.get("key");
-        if (Objects.isNull(keyObj)) {
-            log.error("Token 中不包含 key 字段");
-            throw new IllegalArgumentException("Token 中不包含 key 字段");
-        }
-        return keyObj.toString();
+//        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());
+//        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);
+//        Object keyObj = claims.get("key");
+//        if (Objects.isNull(keyObj)) {
+//            log.error("Token 中不包含 key 字段");
+//            throw new IllegalArgumentException("Token 中不包含 key 字段");
+//        }
+//        return keyObj.toString();
+        return null;
     }
 }
Index: common/src/main/java/com/get/common/query/security/SecurityValidator.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/security/SecurityValidator.java b/common/src/main/java/com/get/common/query/security/SecurityValidator.java
new file mode 100644
--- /dev/null	(date 1755514337960)
+++ b/common/src/main/java/com/get/common/query/security/SecurityValidator.java	(date 1755514337960)
@@ -0,0 +1,562 @@
+package com.get.common.query.security;
+
+import com.get.common.query.enums.QueryType;
+import com.get.common.query.exception.ParameterValidationException;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Value;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
+import org.springframework.stereotype.Component;
+
+import javax.annotation.PostConstruct;
+import java.math.BigDecimal;
+import java.time.LocalDate;
+import java.time.LocalDateTime;
+import java.util.*;
+import java.util.regex.Pattern;
+
+/**
+ * SQL安全验证器
+ * 防止SQL注入攻击，验证字段名和参数值的安全性
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Component
+@ConditionalOnProperty(
+    name = "dynamic.query.security.enabled",
+    havingValue = "true",
+    matchIfMissing = true
+)
+public class SecurityValidator {
+    
+    /**
+     * 字段名最大长度
+     */
+    @Value("${dynamic.query.security.max-column-length:64}")
+    private int maxColumnLength;
+    
+    /**
+     * 参数值最大长度
+     */
+    @Value("${dynamic.query.security.max-value-length:4000}")
+    private int maxValueLength;
+    
+    /**
+     * 是否启用严格模式
+     */
+    @Value("${dynamic.query.security.strict-mode:true}")
+    private boolean strictMode;
+    
+    /**
+     * 字段名白名单正则（字母、数字、下划线）
+     */
+    private static final Pattern COLUMN_NAME_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
+    
+    /**
+     * SQL关键字黑名单
+     */
+    private static final Set<String> SQL_KEYWORDS = new HashSet<>(Arrays.asList(
+        "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER", "GRANT", "REVOKE",
+        "UNION", "EXEC", "EXECUTE", "SCRIPT", "DECLARE", "CURSOR", "TRUNCATE", "MERGE",
+        "INFORMATION_SCHEMA", "SYSOBJECTS", "SYSCOLUMNS", "XP_", "SP_", "WAITFOR",
+        "CHAR", "NCHAR", "VARCHAR", "NVARCHAR", "ASCII", "SUBSTRING", "CONVERT",
+        "CAST", "CONCAT", "CASE", "WHEN", "THEN", "ELSE", "END"
+    ));
+    
+    /**
+     * LIKE查询中需要转义的特殊字符
+     */
+    private static final Map<String, String> LIKE_ESCAPE_MAP = new HashMap<>();
+    
+    static {
+        LIKE_ESCAPE_MAP.put("%", "\\%");
+        LIKE_ESCAPE_MAP.put("_", "\\_");
+        LIKE_ESCAPE_MAP.put("\\", "\\\\");
+        LIKE_ESCAPE_MAP.put("'", "''");
+        LIKE_ESCAPE_MAP.put("[", "\\[");
+        LIKE_ESCAPE_MAP.put("]", "\\]");
+    }
+    
+    @PostConstruct
+    public void init() {
+        log.info("SQL安全验证器初始化完成 - 字段名长度限制: {}, 参数值长度限制: {}, 严格模式: {}", 
+            maxColumnLength, maxValueLength, strictMode);
+    }
+    
+    /**
+     * 验证字段名的安全性
+     * 
+     * @param columnName 字段名
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateColumnName(String columnName) {
+        if (columnName == null || columnName.trim().isEmpty()) {
+            throw new ParameterValidationException("字段名不能为空");
+        }
+        
+        String trimmedName = columnName.trim();
+        
+        // 长度检查
+        if (trimmedName.length() > maxColumnLength) {
+            throw new ParameterValidationException(
+                String.format("字段名长度超过限制: %d > %d", trimmedName.length(), maxColumnLength));
+        }
+        
+        // 格式检查
+        if (!COLUMN_NAME_PATTERN.matcher(trimmedName).matches()) {
+            throw new ParameterValidationException(
+                String.format("字段名格式不合法: %s（只允许字母、数字、下划线，且必须以字母开头）", trimmedName));
+        }
+        
+        // SQL关键字检查
+        if (strictMode && containsSqlKeywords(trimmedName)) {
+            throw new ParameterValidationException(
+                String.format("字段名包含SQL关键字: %s", trimmedName));
+        }
+        
+        log.debug("字段名验证通过: {}", trimmedName);
+    }
+    
+    /**
+     * 验证参数值的安全性
+     * 
+     * @param value 参数值
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateParameterValue(Object value) {
+        if (value == null) {
+            return; // null值是安全的
+        }
+        
+        String stringValue = value.toString();
+        
+        // 长度检查
+        if (stringValue.length() > maxValueLength) {
+            throw new ParameterValidationException(
+                String.format("参数值长度超过限制: %d > %d", stringValue.length(), maxValueLength));
+        }
+        
+        // 严格模式下的SQL注入检查
+        if (strictMode && (containsPotentialSqlInjection(stringValue) || detectAdvancedSqlInjection(stringValue))) {
+            log.warn("检测到潜在的SQL注入攻击: {}", stringValue.length() > 50 ? 
+                stringValue.substring(0, 50) + "..." : stringValue);
+            throw new ParameterValidationException("参数值包含潜在的SQL注入代码");
+        }
+        
+        log.debug("参数值验证通过: {}", stringValue.length() > 100 ? 
+            stringValue.substring(0, 100) + "..." : stringValue);
+    }
+    
+    /**
+     * 转义LIKE查询中的特殊字符
+     * 
+     * @param value LIKE查询的值
+     * @return 转义后的值
+     */
+    public String escapeLikeValue(String value) {
+        if (value == null || value.isEmpty()) {
+            return value;
+        }
+        
+        String escapedValue = value;
+        for (Map.Entry<String, String> entry : LIKE_ESCAPE_MAP.entrySet()) {
+            escapedValue = escapedValue.replace(entry.getKey(), entry.getValue());
+        }
+        
+        log.debug("LIKE值转义: {} -> {}", value, escapedValue);
+        return escapedValue;
+    }
+    
+    /**
+     * 验证集合参数的安全性
+     * 
+     * @param collection 集合参数
+     * @param maxSize 最大集合大小
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateCollectionParameter(Collection<?> collection, int maxSize) {
+        if (collection == null) {
+            return;
+        }
+        
+        if (collection.size() > maxSize) {
+            throw new ParameterValidationException(
+                String.format("集合参数大小超过限制: %d > %d", collection.size(), maxSize));
+        }
+        
+        // 验证集合中的每个元素
+        for (Object item : collection) {
+            validateParameterValue(item);
+        }
+        
+        log.debug("集合参数验证通过 - 大小: {}", collection.size());
+    }
+    
+    /**
+     * 检查字符串是否包含SQL关键字
+     */
+    private boolean containsSqlKeywords(String value) {
+        String upperValue = value.toUpperCase();
+        return SQL_KEYWORDS.stream().anyMatch(upperValue::contains);
+    }
+    
+    /**
+     * 检查字符串是否包含潜在的SQL注入代码
+     */
+    private boolean containsPotentialSqlInjection(String value) {
+        if (value == null || value.trim().isEmpty()) {
+            return false;
+        }
+        
+        String upperValue = value.toUpperCase();
+        
+        // 检查常见的SQL注入模式
+        String[] injectionPatterns = {
+            "'.*OR.*'",
+            "'.*AND.*'",
+            "';.*--",
+            "'.*UNION.*SELECT",
+            "'.*DROP.*TABLE",
+            "'.*INSERT.*INTO",
+            "'.*UPDATE.*SET",
+            "'.*DELETE.*FROM",
+            "EXEC\\s*\\(",
+            "EXECUTE\\s*\\(",
+            "XP_\\w+",
+            "SP_\\w+",
+            "WAITFOR\\s+DELAY",
+            "INFORMATION_SCHEMA"
+        };
+        
+        for (String pattern : injectionPatterns) {
+            if (upperValue.matches(".*" + pattern + ".*")) {
+                return true;
+            }
+        }
+        
+        // 检查多个连续的单引号或双引号
+        if (value.contains("''") || value.contains("\"\"")) {
+            return true;
+        }
+        
+        // 检查注释符号
+        if (upperValue.contains("--") || upperValue.contains("/*") || upperValue.contains("*/")) {
+            return true;
+        }
+        
+        return false;
+    }
+    
+    /**
+     * 验证数字类型参数
+     * 
+     * @param value 数字值
+     * @param minValue 最小值（可选）
+     * @param maxValue 最大值（可选）
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateNumericParameter(Object value, Number minValue, Number maxValue) {
+        if (value == null) {
+            return;
+        }
+        
+        if (!(value instanceof Number)) {
+            throw new ParameterValidationException(
+                String.format("参数不是有效的数字类型: %s", value.getClass().getSimpleName()));
+        }
+        
+        Number numValue = (Number) value;
+        
+        // 检查是否为有效数字（排除NaN和无穷大）
+        if (numValue instanceof Double) {
+            Double doubleValue = (Double) numValue;
+            if (doubleValue.isNaN() || doubleValue.isInfinite()) {
+                throw new ParameterValidationException("数字参数不能为NaN或无穷大");
+            }
+        }
+        if (numValue instanceof Float) {
+            Float floatValue = (Float) numValue;
+            if (floatValue.isNaN() || floatValue.isInfinite()) {
+                throw new ParameterValidationException("数字参数不能为NaN或无穷大");
+            }
+        }
+        
+        // 范围检查
+        if (minValue != null && numValue.doubleValue() < minValue.doubleValue()) {
+            throw new ParameterValidationException(
+                String.format("数字参数小于最小值: %s < %s", numValue, minValue));
+        }
+        
+        if (maxValue != null && numValue.doubleValue() > maxValue.doubleValue()) {
+            throw new ParameterValidationException(
+                String.format("数字参数大于最大值: %s > %s", numValue, maxValue));
+        }
+        
+        log.debug("数字参数验证通过: {}", numValue);
+    }
+    
+    /**
+     * 验证日期类型参数
+     * 
+     * @param value 日期值
+     * @param allowFuture 是否允许将来日期
+     * @param allowPast 是否允许过去日期
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateDateParameter(Object value, boolean allowFuture, boolean allowPast) {
+        if (value == null) {
+            return;
+        }
+        
+        LocalDateTime dateTime = null;
+        
+        try {
+            if (value instanceof LocalDateTime) {
+                dateTime = (LocalDateTime) value;
+            } else if (value instanceof LocalDate) {
+                dateTime = ((LocalDate) value).atStartOfDay();
+            } else if (value instanceof java.util.Date) {
+                dateTime = LocalDateTime.ofInstant(((java.util.Date) value).toInstant(), 
+                    java.time.ZoneId.systemDefault());
+            } else if (value instanceof java.sql.Timestamp) {
+                dateTime = ((java.sql.Timestamp) value).toLocalDateTime();
+            } else {
+                throw new ParameterValidationException(
+                    String.format("参数不是有效的日期类型: %s", value.getClass().getSimpleName()));
+            }
+        } catch (Exception e) {
+            throw new ParameterValidationException(
+                String.format("日期参数转换失败: %s", e.getMessage()));
+        }
+        
+        LocalDateTime now = LocalDateTime.now();
+        
+        if (!allowFuture && dateTime.isAfter(now)) {
+            throw new ParameterValidationException("不允许将来日期参数");
+        }
+        
+        if (!allowPast && dateTime.isBefore(now)) {
+            throw new ParameterValidationException("不允许过去日期参数");
+        }
+        
+        log.debug("日期参数验证通过: {}", dateTime);
+    }
+    
+    /**
+     * 验证字符串参数的特殊字符
+     * 
+     * @param value 字符串值
+     * @param allowedSpecialChars 允许的特殊字符集合
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateStringSpecialChars(String value, Set<Character> allowedSpecialChars) {
+        if (value == null || value.isEmpty()) {
+            return;
+        }
+        
+        Set<Character> foundSpecialChars = new HashSet<>();
+        
+        for (char c : value.toCharArray()) {
+            if (!Character.isLetterOrDigit(c) && !Character.isWhitespace(c)) {
+                if (allowedSpecialChars == null || !allowedSpecialChars.contains(c)) {
+                    foundSpecialChars.add(c);
+                }
+            }
+        }
+        
+        if (!foundSpecialChars.isEmpty()) {
+            throw new ParameterValidationException(
+                String.format("字符串包含不允许的特殊字符: %s", foundSpecialChars));
+        }
+        
+        log.debug("字符串特殊字符验证通过: {}", value.length() > 50 ? 
+            value.substring(0, 50) + "..." : value);
+    }
+    
+    /**
+     * 增强的SQL注入检测
+     * 
+     * @param value 待检测的值
+     * @return true表示检测到SQL注入风险，false表示安全
+     */
+    public boolean detectAdvancedSqlInjection(String value) {
+        if (value == null || value.trim().isEmpty()) {
+            return false;
+        }
+        
+        String upperValue = value.toUpperCase().replaceAll("\\s+", " ");
+        
+        // 高风险SQL注入模式
+        String[] highRiskPatterns = {
+            // 经典注入模式
+            "'.*(OR|AND).*'.*=.*'",
+            "'.*(OR|AND).*1.*=.*1",
+            "'.*(OR|AND).*'.*'.*=.*'.*'",
+            
+            // UNION注入
+            "UNION.*SELECT",
+            "UNION.*ALL.*SELECT",
+            
+            // 时间盲注
+            "WAITFOR.*DELAY",
+            "SLEEP\\s*\\(",
+            "BENCHMARK\\s*\\(",
+            
+            // 堆叠查询
+            ";.*CREATE",
+            ";.*DROP",
+            ";.*INSERT",
+            ";.*UPDATE",
+            ";.*DELETE",
+            ";.*ALTER",
+            ";.*EXEC",
+            ";.*EXECUTE",
+            
+            // 系统函数调用
+            "XP_CMDSHELL",
+            "SP_EXECUTESQL",
+            "OPENROWSET",
+            "OPENDATASOURCE",
+            
+            // 注释绕过
+            "/\\*.*\\*/",
+            "--.*",
+            "#.*",
+            
+            // 编码绕过
+            "CHAR\\s*\\(",
+            "ASCII\\s*\\(",
+            "UNHEX\\s*\\(",
+            "HEX\\s*\\(",
+            
+            // 子查询注入
+            "\\(.*SELECT.*\\)",
+            "EXISTS\\s*\\(.*SELECT",
+            
+            // 条件注入
+            "CASE.*WHEN.*THEN",
+            "IF\\s*\\(.*,.*,.*\\)",
+            
+            // 错误注入
+            "EXTRACTVALUE\\s*\\(",
+            "UPDATEXML\\s*\\(",
+            "XMLTYPE\\s*\\("
+        };
+        
+        for (String pattern : highRiskPatterns) {
+            if (upperValue.matches(".*" + pattern + ".*")) {
+                log.warn("检测到高风险SQL注入模式: {} - 值: {}", pattern, 
+                    value.length() > 100 ? value.substring(0, 100) + "..." : value);
+                return true;
+            }
+        }
+        
+        // 检查可疑的字符组合
+        int suspiciousScore = 0;
+        
+        if (upperValue.contains("'")) suspiciousScore += 1;
+        if (upperValue.contains("\"")) suspiciousScore += 1;
+        if (upperValue.contains("--")) suspiciousScore += 2;
+        if (upperValue.contains("/*")) suspiciousScore += 2;
+        if (upperValue.contains(";")) suspiciousScore += 1;
+        if (upperValue.contains("OR")) suspiciousScore += 1;
+        if (upperValue.contains("AND")) suspiciousScore += 1;
+        if (upperValue.contains("SELECT")) suspiciousScore += 2;
+        if (upperValue.contains("FROM")) suspiciousScore += 1;
+        if (upperValue.contains("WHERE")) suspiciousScore += 1;
+        if (upperValue.contains("DROP")) suspiciousScore += 3;
+        if (upperValue.contains("DELETE")) suspiciousScore += 2;
+        if (upperValue.contains("UPDATE")) suspiciousScore += 2;
+        if (upperValue.contains("INSERT")) suspiciousScore += 2;
+        if (upperValue.contains("EXEC")) suspiciousScore += 3;
+        
+        if (suspiciousScore >= 4) {
+            log.warn("检测到可疑SQL注入组合，风险评分: {} - 值: {}", suspiciousScore,
+                value.length() > 100 ? value.substring(0, 100) + "..." : value);
+            return true;
+        }
+        
+        return false;
+    }
+    
+    /**
+     * 验证查询类型与参数值的兼容性
+     * 
+     * @param queryType 查询类型
+     * @param value 参数值
+     * @throws ParameterValidationException 验证失败时抛出
+     */
+    public void validateQueryTypeCompatibility(QueryType queryType, Object value) {
+        if (value == null) {
+            return;
+        }
+        
+        switch (queryType) {
+            case IN:
+            case NOT_IN:
+                if (!(value instanceof Collection)) {
+                    throw new ParameterValidationException(
+                        String.format("%s查询类型需要集合类型参数，实际类型: %s", 
+                            queryType, value.getClass().getSimpleName()));
+                }
+                break;
+                
+            case BETWEEN:
+            case NOT_BETWEEN:
+                if (!(value instanceof List)) {
+                    throw new ParameterValidationException(
+                        String.format("%s查询类型需要List类型参数，实际类型: %s", 
+                            queryType, value.getClass().getSimpleName()));
+                }
+                List<?> list = (List<?>) value;
+                if (list.size() != 2) {
+                    throw new ParameterValidationException(
+                        String.format("%s查询类型需要包含2个元素的List，实际大小: %d", 
+                            queryType, list.size()));
+                }
+                break;
+                
+            case LIKE:
+            case LIKE_LEFT:
+            case LIKE_RIGHT:
+                if (!(value instanceof String)) {
+                    throw new ParameterValidationException(
+                        String.format("%s查询类型需要String类型参数，实际类型: %s", 
+                            queryType, value.getClass().getSimpleName()));
+                }
+                break;
+                
+            case GT:
+            case GE:
+            case LT:
+            case LE:
+                if (!(value instanceof Comparable)) {
+                    throw new ParameterValidationException(
+                        String.format("%s查询类型需要Comparable类型参数，实际类型: %s", 
+                            queryType, value.getClass().getSimpleName()));
+                }
+                break;
+                
+            default:
+                // 其他类型不做特殊检查
+                break;
+        }
+        
+        log.debug("查询类型兼容性验证通过 - 类型: {}, 参数类型: {}", 
+            queryType, value.getClass().getSimpleName());
+    }
+    
+    /**
+     * 获取安全配置信息
+     */
+    public Map<String, Object> getSecurityConfig() {
+        Map<String, Object> config = new HashMap<>();
+        config.put("maxColumnLength", maxColumnLength);
+        config.put("maxValueLength", maxValueLength);
+        config.put("strictMode", strictMode);
+        config.put("sqlKeywordsCount", SQL_KEYWORDS.size());
+        config.put("likeEscapeRulesCount", LIKE_ESCAPE_MAP.size());
+        return config;
+    }
+}
\ No newline at end of file
Index: .claude/settings.local.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.claude/settings.local.json b/.claude/settings.local.json
new file mode 100644
--- /dev/null	(date 1755514338040)
+++ b/.claude/settings.local.json	(date 1755514338040)
@@ -0,0 +1,23 @@
+{
+  "permissions": {
+    "allow": [
+      "Bash(git checkout:*)",
+      "Bash(rm:*)",
+      "Bash(grep:*)",
+      "Bash(find:*)",
+      "Bash(tail:*)",
+      "Bash(git restore:*)",
+      "mcp__my-sql-database-access-server__run_sql_query",
+      "Bash(env)",
+      "mcp__mysql__connect_db",
+      "mcp__mysql__list_tables",
+      "mcp__mysql__query",
+      "mcp__mysql__show_statement",
+      "mcp__mysql__describe_table",
+      "Bash(mkdir:*)",
+      "Bash(mv:*)",
+      "Bash(ls:*)"
+    ],
+    "deny": []
+  }
+}
\ No newline at end of file
Index: biz-service/ais-middle-center/src/main/java/get/middlecenter/controller/ReleaseInfoAndItemController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package get.middlecenter.controller;\r\n\r\n\r\nimport com.get.aisplatformcenterap.dto.GetPermissionMenuDto;\r\nimport com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;\r\nimport com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;\r\nimport com.get.aisplatformcenterap.dto.UserScopedDataDto;\r\nimport com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;\r\nimport com.get.common.consts.LoggerModulesConsts;\r\nimport com.get.common.consts.LoggerOptTypeConst;\r\nimport com.get.common.result.ListResponseBo;\r\nimport com.get.common.result.ResponseBo;\r\nimport com.get.common.result.SearchBean;\r\nimport com.get.core.log.annotation.OperationLogger;\r\nimport com.get.core.mybatis.base.BaseSelectEntity;\r\nimport com.get.core.secure.annotation.VerifyPermission;\r\nimport get.middlecenter.service.ReleaseInfoAndItemService;\r\nimport io.swagger.annotations.Api;\r\nimport io.swagger.annotations.ApiOperation;\r\nimport javax.annotation.Resource;\r\nimport org.springframework.web.bind.annotation.GetMapping;\r\nimport org.springframework.web.bind.annotation.PostMapping;\r\nimport org.springframework.web.bind.annotation.RequestBody;\r\nimport org.springframework.web.bind.annotation.RequestMapping;\r\nimport org.springframework.web.bind.annotation.RequestParam;\r\nimport org.springframework.web.bind.annotation.RestController;\r\n\r\n@Api(tags = \"发版信息项管理\")\r\n@RestController\r\n@RequestMapping(\"middle/releaseInfoAndItem\")\r\npublic class ReleaseInfoAndItemController {\r\n    /**\r\n     * 服务对象\r\n     */\r\n    @Resource\r\n    private ReleaseInfoAndItemService releaseInfoAndItemService;\r\n\r\n    @ApiOperation(value = \"平台类型下拉\", notes = \"平台类型下拉\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/平台类型下拉\")\r\n    @GetMapping(\"getPlatformTypeDropDown\")\r\n    @VerifyPermission(IsVerify = false)\r\n    public ResponseBo<BaseSelectEntity> getPlatformTypeDropDown() {\r\n        return new ListResponseBo<>(releaseInfoAndItemService.getPlatformTypeDropDown());\r\n    }\r\n\r\n\r\n    @ApiOperation(value = \"分页查询所有数据\", notes = \"分页查询所有数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/查询\")\r\n    @PostMapping(\"list\")\r\n    public ResponseBo selectAll(@RequestBody SearchBean<ReleaseInfoSearchDto> page) {\r\n        return releaseInfoAndItemService.getReleaseInfoAndItem(page);\r\n    }\r\n\r\n    @ApiOperation(value = \"根据id查询详细数据\", notes = \"根据id查询详细数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/详情\")\r\n    @GetMapping(\"/{id}\")\r\n    public ResponseBo<ReleaseInfoAndItemVo> getDetailedInformationById(@RequestBody Long id) {\r\n        return new ResponseBo<>(releaseInfoAndItemService.getDetailedInformationById(id));\r\n    }\r\n\r\n    @ApiOperation(value = \"修改数据\", notes = \"修改数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = \"中台中心/发版信息项管理/修改\")\r\n    @PostMapping(\"editReleaseInfoAndItem\")\r\n    public ResponseBo editReleaseInfoAndItem(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {\r\n        releaseInfoAndItemService.editReleaseInfoAndItem(releaseInfoAndItemDto);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    //更改发版信息的状态 status int(10) DEFAULT NULL COMMENT '枚举：0待发布/1已发布/2已撤回',\r\n    @ApiOperation(value = \"更改发版信息的状态\", notes = \"更改发版信息的状态 枚举：0待发布/1已发布/2已撤回\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = \"中台中心/发版信息项管理/更改发版信息的状态\")\r\n    @PostMapping(\"updateReleaseInfoStatus\")\r\n    public ResponseBo updateReleaseInfoStatus(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {\r\n        releaseInfoAndItemService.updateReleaseInfoStatus(releaseInfoAndItemDto);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"新增数据\", notes = \"新增数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = \"中台中心/发版信息项管理/新增\")\r\n    @PostMapping(\"addReleaseInfoAndItem\")\r\n    public ResponseBo addReleaseInfoAndItem(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {\r\n        releaseInfoAndItemService.addReleaseInfoAndItem(releaseInfoAndItemDto);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n    @ApiOperation(value = \"删除数据\", notes = \"删除数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.DELETE, description = \"中台中心/发版信息项管理/删除\")\r\n    @GetMapping(\"deleteReleaseInfoAndItem\")\r\n    public ResponseBo delete(@RequestParam Long id) {\r\n        releaseInfoAndItemService.deleteReleaseInfoAndItem(id);\r\n        return ResponseBo.ok();\r\n    }\r\n\r\n\r\n    @ApiOperation(value = \"获取AIS权限菜单\", notes = \"获取AIS权限菜单\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/获取AIS权限菜单\")\r\n    @PostMapping(\"getAisPermissionMenu\")\r\n    public ResponseBo getAisPermissionMenu() {\r\n        return new ResponseBo<>(releaseInfoAndItemService.getAisPermissionMenu());\r\n    }\r\n\r\n\r\n    @ApiOperation(value = \"获取Partner权限菜单\", notes = \"获取Partner权限菜单\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/获取Partner权限菜单\")\r\n    @PostMapping(\"getPartnerPermissionMenu\")\r\n    public ResponseBo getPartnerPermissionMenu(@RequestBody GetPermissionMenuDto getPermissionMenuDto) {\r\n        return new ResponseBo<>(releaseInfoAndItemService.getPartnerPermissionMenu(getPermissionMenuDto));\r\n    }\r\n\r\n\r\n    // 视频  公告图片上传\r\n//    @ApiOperation(value = \"上传视频/图片\", notes = \"上传视频/图片\")\r\n//    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/上传视频-图片\")\r\n//    @PostMapping(\"uploadFile\")\r\n//    public ResponseBo uploadFile(@RequestParam(\"file\") MultipartFile file) {\r\n//        return new ResponseBo<>(mReleaseInfoService.uploadFile(file));\r\n//    }\r\n\r\n    @ApiOperation(value = \"根据用户权限获取列表信息\", notes = \"根据用户权限获取列表信息\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/根据用户权限获取列表信息\")\r\n    @PostMapping(\"getUserListByResourceKeys\")\r\n    public ResponseBo getUserListByResourceKeys(@RequestBody SearchBean<UserScopedDataDto> page) {\r\n        return releaseInfoAndItemService.getUserListByResourceKeys(page);\r\n    }\r\n\r\n    //根据用户权限获取最新创建的一条数据\r\n    @ApiOperation(value = \"根据用户权限获取最新创建的一条数据\", notes = \"根据用户权限获取最新创建的一条数据\")\r\n    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = \"中台中心/发版信息项管理/根据用户权限获取最新创建的一条数据\")\r\n    @PostMapping(\"getOneByPermission\")\r\n    public ResponseBo<ReleaseInfoAndItemVo> getUserOneByPermission(@RequestBody UserScopedDataDto userScopedDataDto) {\r\n        return new ResponseBo<>(releaseInfoAndItemService.getUserOneByResourceKeys(userScopedDataDto));\r\n    }\r\n\r\n\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-middle-center/src/main/java/get/middlecenter/controller/ReleaseInfoAndItemController.java b/biz-service/ais-middle-center/src/main/java/get/middlecenter/controller/ReleaseInfoAndItemController.java
--- a/biz-service/ais-middle-center/src/main/java/get/middlecenter/controller/ReleaseInfoAndItemController.java	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/biz-service/ais-middle-center/src/main/java/get/middlecenter/controller/ReleaseInfoAndItemController.java	(date 1755514338082)
@@ -8,7 +8,6 @@
 import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
 import com.get.common.consts.LoggerModulesConsts;
 import com.get.common.consts.LoggerOptTypeConst;
-import com.get.common.result.ListResponseBo;
 import com.get.common.result.ResponseBo;
 import com.get.common.result.SearchBean;
 import com.get.core.log.annotation.OperationLogger;
@@ -17,7 +16,6 @@
 import get.middlecenter.service.ReleaseInfoAndItemService;
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiOperation;
-import javax.annotation.Resource;
 import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
@@ -25,6 +23,8 @@
 import org.springframework.web.bind.annotation.RequestParam;
 import org.springframework.web.bind.annotation.RestController;
 
+import javax.annotation.Resource;
+
 @Api(tags = "发版信息项管理")
 @RestController
 @RequestMapping("middle/releaseInfoAndItem")
@@ -40,7 +40,8 @@
     @GetMapping("getPlatformTypeDropDown")
     @VerifyPermission(IsVerify = false)
     public ResponseBo<BaseSelectEntity> getPlatformTypeDropDown() {
-        return new ListResponseBo<>(releaseInfoAndItemService.getPlatformTypeDropDown());
+//        return new ListResponseBo<>(releaseInfoAndItemService.getPlatformTypeDropDown());
+        return null;
     }
 
 
Index: common/src/main/java/com/get/common/query/exception/FieldAccessException.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/exception/FieldAccessException.java b/common/src/main/java/com/get/common/query/exception/FieldAccessException.java
new file mode 100644
--- /dev/null	(date 1755514337940)
+++ b/common/src/main/java/com/get/common/query/exception/FieldAccessException.java	(date 1755514337940)
@@ -0,0 +1,52 @@
+package com.get.common.query.exception;
+
+import java.lang.reflect.Field;
+
+/**
+ * 字段访问异常
+ * 在通过反射访问字段时发生的异常
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public class FieldAccessException extends DynamicQueryException {
+
+    public FieldAccessException(String message) {
+        super("FIELD_ACCESS_ERROR", message);
+    }
+
+    public FieldAccessException(String message, Throwable cause) {
+        super("FIELD_ACCESS_ERROR", message, cause);
+    }
+
+    public FieldAccessException(String message, Field field, Throwable cause) {
+        super("FIELD_ACCESS_ERROR", message, cause, field.getName(), field.getType().getSimpleName());
+    }
+
+    /**
+     * 创建字段访问失败异常
+     */
+    public static FieldAccessException accessFailed(Field field, Throwable cause) {
+        String message = String.format("访问字段失败 - 字段: %s, 类型: %s", 
+            field.getName(), field.getType().getSimpleName());
+        return new FieldAccessException(message, field, cause);
+    }
+
+    /**
+     * 创建字段访问失败异常（带上下文信息）
+     */
+    public static FieldAccessException accessFailed(Field field, String context, Throwable cause) {
+        String message = String.format("访问字段失败 - 字段: %s, 类型: %s, 上下文: %s", 
+            field.getName(), field.getType().getSimpleName(), context);
+        return new FieldAccessException(message, field, cause);
+    }
+
+    /**
+     * 创建分组字段访问失败异常
+     */
+    public static FieldAccessException groupAccessFailed(Field field, String groupName, Throwable cause) {
+        String message = String.format("访问分组字段失败 - 字段: %s, 分组: %s", 
+            field.getName(), groupName);
+        return new FieldAccessException(message, field, cause);
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/service/QueryUtils.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/service/QueryUtils.java b/common/src/main/java/com/get/common/query/service/QueryUtils.java
new file mode 100644
--- /dev/null	(date 1755514337950)
+++ b/common/src/main/java/com/get/common/query/service/QueryUtils.java	(date 1755514337950)
@@ -0,0 +1,214 @@
+package com.get.common.query.service;
+
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+import com.get.common.query.model.QueryResult;
+import com.get.common.query.monitor.QueryMetricsCollector;
+import com.get.common.query.support.QueryCustomizer;
+import com.get.common.result.Page;
+import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
+import org.springframework.stereotype.Service;
+
+import java.util.Collection;
+import java.util.Map;
+
+/**
+ * 查询工具服务类
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Service
+@ConditionalOnBean(DynamicQueryBuilder.class)
+@RequiredArgsConstructor
+public class QueryUtils {
+    
+    private final DynamicQueryBuilder queryBuilder;
+    private final QueryMetricsCollector metricsCollector;
+    
+    /**
+     * 构建查询条件
+     *
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类Class
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     */
+    public <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass) {
+        log.debug("开始构建查询条件 - DTO: {}, Entity: {}", 
+            queryDto.getClass().getSimpleName(), entityClass.getSimpleName());
+        
+        QueryWrapper<T> wrapper = queryBuilder.buildQueryWrapper(queryDto, entityClass);
+        
+        log.debug("查询条件构建完成 - 生成的SQL片段数量: {}", wrapper.getExpression().getNormal().size());
+        return wrapper;
+    }
+
+    /**
+     * 构建查询条件并添加自定义条件
+     *
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类Class
+     * @param customizer 自定义查询条件
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     */
+    public <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass, QueryCustomizer<T> customizer) {
+        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
+        
+        if (customizer != null) {
+            log.debug("应用自定义查询条件");
+            customizer.customize(wrapper);
+        }
+        
+        return wrapper;
+    }
+    
+    /**
+     * 构建分页查询条件
+     *
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类Class
+     * @param page 分页参数
+     * @param <T> 实体类型
+     * @return 包含分页信息的查询结果
+     */
+    public <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass, Page page) {
+        log.debug("开始构建分页查询 - 页码: {}, 页大小: {}", page.getCurrentPage(), page.getShowCount());
+        
+        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
+        return new QueryResult<>(wrapper, page);
+    }
+
+    /**
+     * 构建分页查询条件并添加自定义条件
+     *
+     * @param queryDto 查询DTO对象
+     * @param entityClass 实体类Class
+     * @param page 分页参数
+     * @param customizer 自定义查询条件
+     * @param <T> 实体类型
+     * @return 包含分页信息的查询结果
+     */
+    public <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass, 
+                                           Page page, QueryCustomizer<T> customizer) {
+        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass, customizer);
+        return new QueryResult<>(wrapper, page);
+    }
+    
+    /**
+     * 快速构建简单的等值查询
+     *
+     * @param entityClass 实体类Class
+     * @param column 数据库字段名
+     * @param value 查询值
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     */
+    public <T> QueryWrapper<T> buildSimpleQuery(Class<T> entityClass, String column, Object value) {
+        log.debug("构建简单等值查询 - 字段: {}, 值: {}", column, value);
+        
+        QueryWrapper<T> wrapper = new QueryWrapper<>();
+        if (value != null) {
+            wrapper.eq(column, value);
+        }
+        return wrapper;
+    }
+
+    /**
+     * 快速构建模糊查询
+     *
+     * @param entityClass 实体类Class
+     * @param column 数据库字段名
+     * @param value 查询值
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     */
+    public <T> QueryWrapper<T> buildLikeQuery(Class<T> entityClass, String column, String value) {
+        log.debug("构建模糊查询 - 字段: {}, 值: {}", column, value);
+        
+        QueryWrapper<T> wrapper = new QueryWrapper<>();
+        if (value != null && !value.trim().isEmpty()) {
+            wrapper.like(column, value.trim());
+        }
+        return wrapper;
+    }
+
+    /**
+     * 快速构建IN查询
+     *
+     * @param entityClass 实体类Class
+     * @param column 数据库字段名
+     * @param values 查询值集合
+     * @param <T> 实体类型
+     * @return 构建好的QueryWrapper
+     */
+    public <T> QueryWrapper<T> buildInQuery(Class<T> entityClass, String column, Collection<?> values) {
+        log.debug("构建IN查询 - 字段: {}, 值数量: {}", column, values != null ? values.size() : 0);
+        
+        QueryWrapper<T> wrapper = new QueryWrapper<>();
+        if (values != null && !values.isEmpty()) {
+            wrapper.in(column, values);
+        }
+        return wrapper;
+    }
+    
+    /**
+     * 获取查询构建器的缓存统计信息
+     *
+     * @return 缓存统计信息
+     */
+    public Map<String, Object> getCacheStats() {
+        return queryBuilder.getCacheStats();
+    }
+    
+    /**
+     * 获取指定DTO类的详细诊断信息
+     *
+     * @param queryDtoClass 查询DTO类
+     * @return 诊断信息
+     */
+    public Map<String, Object> getDiagnosticInfo(Class<?> queryDtoClass) {
+        return queryBuilder.getDiagnosticInfo(queryDtoClass);
+    }
+    
+    /**
+     * 获取性能指标
+     *
+     * @return 性能指标信息
+     */
+    public Map<String, Object> getPerformanceMetrics() {
+        return metricsCollector.getAllMetrics();
+    }
+    
+    /**
+     * 获取性能报告
+     *
+     * @return 性能报告
+     */
+    public Map<String, Object> getPerformanceReport() {
+        return metricsCollector.getPerformanceReport();
+    }
+    
+    /**
+     * 清空查询构建器缓存
+     */
+    public void clearCache() {
+        log.info("清空动态查询缓存");
+        queryBuilder.clearCache();
+    }
+    
+    /**
+     * 验证查询DTO类的配置
+     *
+     * @param queryDtoClass 查询DTO类
+     * @return 验证结果信息
+     */
+    public Map<String, Object> validateQueryDto(Class<?> queryDtoClass) {
+        log.debug("验证查询DTO配置 - 类: {}", queryDtoClass.getSimpleName());
+        return queryBuilder.getDiagnosticInfo(queryDtoClass);
+    }
+    
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/cache/CacheKeyGenerator.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/cache/CacheKeyGenerator.java b/common/src/main/java/com/get/common/query/cache/CacheKeyGenerator.java
new file mode 100644
--- /dev/null	(date 1755514338107)
+++ b/common/src/main/java/com/get/common/query/cache/CacheKeyGenerator.java	(date 1755514338107)
@@ -0,0 +1,174 @@
+package com.get.common.query.cache;
+
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
+import org.springframework.stereotype.Component;
+
+/**
+ * 缓存键生成器
+ * 统一管理动态查询框架中所有缓存和监控的key生成逻辑
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Component
+@ConditionalOnProperty(
+    name = "dynamic.query.cache.enabled",
+    havingValue = "true",
+    matchIfMissing = true
+)
+public class CacheKeyGenerator {
+    
+    /**
+     * 字段缓存key分隔符
+     */
+    private static final String FIELD_CACHE_PREFIX = "field_cache:";
+    
+    /**
+     * 监控指标key分隔符
+     */
+    private static final String METRICS_SEPARATOR = "->";
+    
+    /**
+     * 生成字段信息缓存的key
+     * 
+     * @param dtoClass 查询DTO类
+     * @return 缓存key
+     */
+    public String generateFieldCacheKey(Class<?> dtoClass) {
+        if (dtoClass == null) {
+            throw new IllegalArgumentException("DTO类不能为null");
+        }
+        
+        String key = FIELD_CACHE_PREFIX + dtoClass.getName();
+        log.debug("生成字段缓存key: {}", key);
+        return key;
+    }
+    
+    /**
+     * 生成性能监控指标的key
+     * 
+     * @param dtoClass 查询DTO类
+     * @param entityClass 实体类（可以为null）
+     * @return 监控指标key
+     */
+    public String generateMetricsKey(Class<?> dtoClass, Class<?> entityClass) {
+        if (dtoClass == null) {
+            throw new IllegalArgumentException("DTO类不能为null");
+        }
+        
+        String key;
+        if (entityClass != null) {
+            key = dtoClass.getSimpleName() + METRICS_SEPARATOR + entityClass.getSimpleName();
+        } else {
+            key = dtoClass.getSimpleName();
+        }
+        
+        log.debug("生成监控指标key: {}", key);
+        return key;
+    }
+    
+    /**
+     * 生成完整的查询上下文key
+     * 包含DTO类和实体类的完整信息
+     * 
+     * @param dtoClass 查询DTO类
+     * @param entityClass 实体类
+     * @return 查询上下文key
+     */
+    public String generateQueryContextKey(Class<?> dtoClass, Class<?> entityClass) {
+        if (dtoClass == null || entityClass == null) {
+            throw new IllegalArgumentException("DTO类和实体类都不能为null");
+        }
+        
+        String key = dtoClass.getName() + METRICS_SEPARATOR + entityClass.getName();
+        log.debug("生成查询上下文key: {}", key);
+        return key;
+    }
+    
+    /**
+     * 从字段缓存key中提取DTO类名
+     * 
+     * @param cacheKey 缓存key
+     * @return DTO类名
+     */
+    public String extractDtoClassNameFromCacheKey(String cacheKey) {
+        if (cacheKey == null || !cacheKey.startsWith(FIELD_CACHE_PREFIX)) {
+            throw new IllegalArgumentException("无效的缓存key格式: " + cacheKey);
+        }
+        
+        return cacheKey.substring(FIELD_CACHE_PREFIX.length());
+    }
+    
+    /**
+     * 从监控指标key中提取DTO类名
+     * 
+     * @param metricsKey 监控指标key
+     * @return DTO类名
+     */
+    public String extractDtoClassNameFromMetricsKey(String metricsKey) {
+        if (metricsKey == null || metricsKey.isEmpty()) {
+            throw new IllegalArgumentException("监控指标key不能为空");
+        }
+        
+        int separatorIndex = metricsKey.indexOf(METRICS_SEPARATOR);
+        if (separatorIndex > 0) {
+            return metricsKey.substring(0, separatorIndex);
+        } else {
+            return metricsKey;
+        }
+    }
+    
+    /**
+     * 从监控指标key中提取实体类名
+     * 
+     * @param metricsKey 监控指标key
+     * @return 实体类名，如果不存在则返回null
+     */
+    public String extractEntityClassNameFromMetricsKey(String metricsKey) {
+        if (metricsKey == null || metricsKey.isEmpty()) {
+            return null;
+        }
+        
+        int separatorIndex = metricsKey.indexOf(METRICS_SEPARATOR);
+        if (separatorIndex > 0 && separatorIndex < metricsKey.length() - METRICS_SEPARATOR.length()) {
+            return metricsKey.substring(separatorIndex + METRICS_SEPARATOR.length());
+        } else {
+            return null;
+        }
+    }
+    
+    /**
+     * 验证缓存key的格式
+     * 
+     * @param cacheKey 缓存key
+     * @return 是否为有效格式
+     */
+    public boolean isValidCacheKey(String cacheKey) {
+        return cacheKey != null && 
+               cacheKey.startsWith(FIELD_CACHE_PREFIX) && 
+               cacheKey.length() > FIELD_CACHE_PREFIX.length();
+    }
+    
+    /**
+     * 验证监控指标key的格式
+     * 
+     * @param metricsKey 监控指标key
+     * @return 是否为有效格式
+     */
+    public boolean isValidMetricsKey(String metricsKey) {
+        return metricsKey != null && !metricsKey.trim().isEmpty();
+    }
+    
+    /**
+     * 获取key生成器的配置信息
+     */
+    public java.util.Map<String, Object> getKeyGeneratorInfo() {
+        java.util.Map<String, Object> info = new java.util.HashMap<>();
+        info.put("fieldCachePrefix", FIELD_CACHE_PREFIX);
+        info.put("metricsSeparator", METRICS_SEPARATOR);
+        info.put("supportedKeyTypes", java.util.Arrays.asList("fieldCache", "metrics", "queryContext"));
+        return info;
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/cache/QueryCacheManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/cache/QueryCacheManager.java b/common/src/main/java/com/get/common/query/cache/QueryCacheManager.java
new file mode 100644
--- /dev/null	(date 1755514338114)
+++ b/common/src/main/java/com/get/common/query/cache/QueryCacheManager.java	(date 1755514338114)
@@ -0,0 +1,380 @@
+package com.get.common.query.cache;
+
+import com.get.common.query.model.FieldInfo;
+import lombok.Getter;
+import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Value;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
+import org.springframework.stereotype.Component;
+
+import javax.annotation.PostConstruct;
+import javax.annotation.PreDestroy;
+import java.time.LocalDateTime;
+import java.util.*;
+import java.util.concurrent.ConcurrentHashMap;
+import java.util.concurrent.Executors;
+import java.util.concurrent.ScheduledExecutorService;
+import java.util.concurrent.TimeUnit;
+import java.util.concurrent.atomic.AtomicLong;
+
+/**
+ * 查询缓存管理器
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Component
+@ConditionalOnProperty(
+    name = "dynamic.query.cache.enabled",
+    havingValue = "true",
+    matchIfMissing = true
+)
+@RequiredArgsConstructor
+public class QueryCacheManager {
+    
+    private final CacheKeyGenerator keyGenerator;
+    
+    /**
+     * 字段信息缓存（使用字符串key代替Class key）
+     */
+    private final Map<String, CachedFieldInfo> fieldCache = new ConcurrentHashMap<>();
+    
+    /**
+     * 缓存统计信息
+     */
+    private final Map<String, AtomicLong> stats = new ConcurrentHashMap<>();
+    
+    /**
+     * 定时清理任务执行器
+     */
+    private ScheduledExecutorService cleanupExecutor;
+    
+    /**
+     * 缓存最大大小
+     */
+    @Value("${dynamic.query.cache.max-size:1000}")
+    private int maxCacheSize;
+    
+    /**
+     * 缓存过期时间（小时）
+     */
+    @Value("${dynamic.query.cache.expire-hours:24}")
+    private int expireHours;
+    
+    /**
+     * 清理任务执行间隔（分钟）
+     */
+    @Value("${dynamic.query.cache.cleanup-interval:60}")
+    private int cleanupIntervalMinutes;
+    
+    @PostConstruct
+    public void init() {
+        // 初始化统计信息
+        initStats();
+        
+        // 启动定时清理任务
+        startCleanupTask();
+        
+        log.info("查询缓存管理器初始化完成 - 最大缓存数: {}, 过期时间: {}小时, 清理间隔: {}分钟", 
+            maxCacheSize, expireHours, cleanupIntervalMinutes);
+    }
+    
+    @PreDestroy
+    public void destroy() {
+        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
+            cleanupExecutor.shutdown();
+            try {
+                if (!cleanupExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
+                    cleanupExecutor.shutdownNow();
+                }
+            } catch (InterruptedException e) {
+                cleanupExecutor.shutdownNow();
+                Thread.currentThread().interrupt();
+            }
+            log.info("查询缓存管理器清理任务已停止");
+        }
+    }
+    
+    /**
+     * 获取字段信息
+     */
+    public List<FieldInfo> getFieldInfos(Class<?> clazz) {
+        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
+        CachedFieldInfo cachedInfo = fieldCache.get(cacheKey);
+        
+        if (cachedInfo == null) {
+            incrementStat("cacheMiss");
+            return null;
+        }
+        
+        // 检查是否过期
+        if (isExpired(cachedInfo)) {
+            fieldCache.remove(cacheKey);
+            incrementStat("cacheExpired");
+            incrementStat("cacheMiss");
+            log.debug("缓存已过期并移除 - 类: {}, key: {}", clazz.getSimpleName(), cacheKey);
+            return null;
+        }
+        
+        // 更新访问时间和访问次数
+        cachedInfo.updateAccess();
+        incrementStat("cacheHit");
+        
+        return cachedInfo.getFieldInfos();
+    }
+    
+    /**
+     * 缓存字段信息
+     */
+    public void cacheFieldInfos(Class<?> clazz, List<FieldInfo> fieldInfos) {
+        // 检查缓存大小限制
+        if (fieldCache.size() >= maxCacheSize) {
+            evictLeastRecentlyUsed();
+        }
+        
+        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
+        CachedFieldInfo cachedInfo = new CachedFieldInfo(fieldInfos);
+        fieldCache.put(cacheKey, cachedInfo);
+        incrementStat("cachePut");
+        
+        log.debug("缓存字段信息 - 类: {}, key: {}, 字段数: {}", 
+            clazz.getSimpleName(), cacheKey, fieldInfos.size());
+    }
+    
+    /**
+     * 清空所有缓存
+     */
+    public void clearAll() {
+        int size = fieldCache.size();
+        fieldCache.clear();
+        incrementStat("cacheCleared");
+        log.info("清空所有缓存 - 清理数量: {}", size);
+    }
+    
+    /**
+     * 清空指定类的缓存
+     */
+    public void clearClass(Class<?> clazz) {
+        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
+        CachedFieldInfo removed = fieldCache.remove(cacheKey);
+        if (removed != null) {
+            incrementStat("cacheRemoved");
+            log.debug("清空指定类缓存 - 类: {}, key: {}", clazz.getSimpleName(), cacheKey);
+        }
+    }
+    
+    /**
+     * 获取缓存统计信息
+     */
+    public Map<String, Object> getCacheStats() {
+        Map<String, Object> result = new HashMap<>();
+        
+        // 基础统计
+        result.put("cacheSize", fieldCache.size());
+        result.put("maxCacheSize", maxCacheSize);
+        result.put("expireHours", expireHours);
+        
+        // 访问统计
+        stats.forEach((key, value) -> result.put(key, value.get()));
+        
+        // 命中率计算
+        long hits = stats.getOrDefault("cacheHit", new AtomicLong(0)).get();
+        long misses = stats.getOrDefault("cacheMiss", new AtomicLong(0)).get();
+        long total = hits + misses;
+        if (total > 0) {
+            result.put("hitRate", String.format("%.2f%%", (double) hits / total * 100));
+        } else {
+            result.put("hitRate", "0.00%");
+        }
+        
+        // 缓存详情
+        Map<String, Object> cacheDetails = new HashMap<>();
+        fieldCache.forEach((cacheKey, cachedInfo) -> {
+            Map<String, Object> detail = new HashMap<>();
+            detail.put("fieldCount", cachedInfo.getFieldInfos().size());
+            detail.put("createTime", cachedInfo.getCreateTime());
+            detail.put("lastAccessTime", cachedInfo.getLastAccessTime());
+            detail.put("accessCount", cachedInfo.getAccessCount());
+            detail.put("cacheKey", cacheKey);
+            
+            // 从缓存key中提取类名作为显示名
+            try {
+                String className = keyGenerator.extractDtoClassNameFromCacheKey(cacheKey);
+                String simpleName = className.substring(className.lastIndexOf('.') + 1);
+                cacheDetails.put(simpleName, detail);
+            } catch (Exception e) {
+                // 如果提取失败，使用原始key
+                cacheDetails.put(cacheKey, detail);
+            }
+        });
+        result.put("cacheDetails", cacheDetails);
+        
+        return result;
+    }
+    
+    /**
+     * 获取缓存健康状态
+     */
+    public Map<String, Object> getHealthStatus() {
+        Map<String, Object> health = new HashMap<>();
+        
+        long hits = stats.getOrDefault("cacheHit", new AtomicLong(0)).get();
+        long misses = stats.getOrDefault("cacheMiss", new AtomicLong(0)).get();
+        long total = hits + misses;
+        
+        // 健康状态判断
+        String status;
+        if (total == 0) {
+            status = "UNKNOWN";
+        } else {
+            double hitRate = (double) hits / total;
+            if (hitRate >= 0.8) {
+                status = "EXCELLENT";
+            } else if (hitRate >= 0.6) {
+                status = "GOOD";
+            } else if (hitRate >= 0.4) {
+                status = "FAIR";
+            } else {
+                status = "POOR";
+            }
+        }
+        
+        health.put("status", status);
+        health.put("cacheSize", fieldCache.size());
+        health.put("maxSize", maxCacheSize);
+        health.put("utilization", String.format("%.2f%%", (double) fieldCache.size() / maxCacheSize * 100));
+        
+        return health;
+    }
+    
+    /**
+     * 初始化统计信息
+     */
+    private void initStats() {
+        stats.put("cacheHit", new AtomicLong(0));
+        stats.put("cacheMiss", new AtomicLong(0));
+        stats.put("cachePut", new AtomicLong(0));
+        stats.put("cacheExpired", new AtomicLong(0));
+        stats.put("cacheEvicted", new AtomicLong(0));
+        stats.put("cacheRemoved", new AtomicLong(0));
+        stats.put("cacheCleared", new AtomicLong(0));
+    }
+    
+    /**
+     * 递增统计计数
+     */
+    private void incrementStat(String key) {
+        stats.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
+    }
+    
+    /**
+     * 启动定时清理任务
+     */
+    private void startCleanupTask() {
+        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
+            Thread thread = new Thread(r, "QueryCacheCleanup");
+            thread.setDaemon(true);
+            return thread;
+        });
+        
+        cleanupExecutor.scheduleWithFixedDelay(
+            this::cleanupExpiredCache,
+            cleanupIntervalMinutes,
+            cleanupIntervalMinutes,
+            TimeUnit.MINUTES
+        );
+    }
+    
+    /**
+     * 清理过期缓存
+     */
+    private void cleanupExpiredCache() {
+        int cleanedCount = 0;
+        
+        Iterator<Map.Entry<String, CachedFieldInfo>> iterator = fieldCache.entrySet().iterator();
+        while (iterator.hasNext()) {
+            Map.Entry<String, CachedFieldInfo> entry = iterator.next();
+            if (isExpired(entry.getValue())) {
+                iterator.remove();
+                cleanedCount++;
+                incrementStat("cacheExpired");
+            }
+        }
+        
+        if (cleanedCount > 0) {
+            log.debug("定时清理过期缓存 - 清理数量: {}, 剩余数量: {}", cleanedCount, fieldCache.size());
+        }
+    }
+    
+    /**
+     * 检查缓存是否过期
+     */
+    private boolean isExpired(CachedFieldInfo cachedInfo) {
+        return cachedInfo.getCreateTime().plusHours(expireHours).isBefore(LocalDateTime.now());
+    }
+    
+    /**
+     * 驱逐最近最少使用的缓存项
+     */
+    private void evictLeastRecentlyUsed() {
+        if (fieldCache.isEmpty()) {
+            return;
+        }
+        
+        // 找到最久未访问的缓存项
+        String lruKey = null;
+        LocalDateTime oldestAccess = LocalDateTime.now();
+        
+        for (Map.Entry<String, CachedFieldInfo> entry : fieldCache.entrySet()) {
+            LocalDateTime lastAccess = entry.getValue().getLastAccessTime();
+            if (lastAccess.isBefore(oldestAccess)) {
+                oldestAccess = lastAccess;
+                lruKey = entry.getKey();
+            }
+        }
+        
+        if (lruKey != null) {
+            fieldCache.remove(lruKey);
+            incrementStat("cacheEvicted");
+            
+            // 尝试从缓存key中提取类名用于日志
+            try {
+                String className = keyGenerator.extractDtoClassNameFromCacheKey(lruKey);
+                String simpleName = className.substring(className.lastIndexOf('.') + 1);
+                log.debug("驱逐LRU缓存项 - 类: {}, key: {}, 最后访问时间: {}", 
+                    simpleName, lruKey, oldestAccess);
+            } catch (Exception e) {
+                log.debug("驱逐LRU缓存项 - key: {}, 最后访问时间: {}", lruKey, oldestAccess);
+            }
+        }
+    }
+    
+    /**
+     * 缓存字段信息封装类
+     */
+    @Getter
+    private static class CachedFieldInfo {
+        private final List<FieldInfo> fieldInfos;
+        private final LocalDateTime createTime;
+        private volatile LocalDateTime lastAccessTime;
+        private final AtomicLong accessCount;
+        
+        public CachedFieldInfo(List<FieldInfo> fieldInfos) {
+            this.fieldInfos = new ArrayList<>(fieldInfos);
+            this.createTime = LocalDateTime.now();
+            this.lastAccessTime = this.createTime;
+            this.accessCount = new AtomicLong(1);
+        }
+        
+        public long getAccessCount() {
+            return accessCount.get();
+        }
+        
+        public void updateAccess() {
+            this.lastAccessTime = LocalDateTime.now();
+            this.accessCount.incrementAndGet();
+        }
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/DynamicQueryConfiguration.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/DynamicQueryConfiguration.java b/common/src/main/java/com/get/common/query/DynamicQueryConfiguration.java
new file mode 100644
--- /dev/null	(date 1755514338059)
+++ b/common/src/main/java/com/get/common/query/DynamicQueryConfiguration.java	(date 1755514338059)
@@ -0,0 +1,15 @@
+package com.get.common.query;
+
+import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
+import org.springframework.context.annotation.ComponentScan;
+import org.springframework.context.annotation.Configuration;
+
+@Configuration
+@ConditionalOnProperty(
+    name = "dynamic.query.enabled", 
+    havingValue = "true", 
+    matchIfMissing = false
+)
+@ComponentScan(basePackages = "com.get.common.query")
+public class DynamicQueryConfiguration {
+}
Index: biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/aaa.html
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/aaa.html b/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/aaa.html
new file mode 100644
--- /dev/null	(date 1755514338101)
+++ b/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/aaa.html	(date 1755514338101)
@@ -0,0 +1,31 @@
+<div class="content"><h2 class="title">代理续签审批意见通知(${personalName})</h2>
+    <div class="border"></div>
+    </br>
+    <div class="desc">
+        <div>${personalName}，您好:</br>申请代理:${name}</br>审批意见如下：${rejectMessage}</br>在线表单修改：<a href="http://192.168.2.31:9005/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}">Link</a></br>小程序二维码：<img src="${qrcode}" alt="微信小程序二维码" width="120" height="120"></br></div>
+    </div>
+    <div style="text-align: right;" class="desc">华通国际</br>${currentDate}</br></div>
+</div>
+
+
+personalName
+name
+id
+renewalToken
+
+
+
+
+<div class="content">
+    <h2 class="title">华通国际代理合同续签</h2>
+    <div class="border"></div></br>
+    <div class="desc">
+        <div>
+            ${name}，您好:</br>
+            申请代理:${name}</br>
+            现诚挚邀请您办理合同续签手续，请使用手机微信扫二维码或点击链接进行续签操作。</br>
+            在线表单链接：<a href="http://192.168.2.31:9005/apply-agent-online-form/renewal?id=${id}&renewalToken=${renewalToken}">https://u23ijweflkms</a></br>
+            小程序二维码：<img src="${qrcode}" alt="hit" width="120" height="120"></br>
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/support/QueryCustomizer.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/support/QueryCustomizer.java b/common/src/main/java/com/get/common/query/support/QueryCustomizer.java
new file mode 100644
--- /dev/null	(date 1755514338092)
+++ b/common/src/main/java/com/get/common/query/support/QueryCustomizer.java	(date 1755514338092)
@@ -0,0 +1,21 @@
+package com.get.common.query.support;
+
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+
+/**
+ * 查询自定义器接口
+ * 用于在构建基础查询条件后添加自定义逻辑
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@FunctionalInterface
+public interface QueryCustomizer<T> {
+    
+    /**
+     * 自定义查询条件
+     *
+     * @param wrapper QueryWrapper实例
+     */
+    void customize(QueryWrapper<T> wrapper);
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/model/FieldInfo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/model/FieldInfo.java b/common/src/main/java/com/get/common/query/model/FieldInfo.java
new file mode 100644
--- /dev/null	(date 1755514338145)
+++ b/common/src/main/java/com/get/common/query/model/FieldInfo.java	(date 1755514338145)
@@ -0,0 +1,117 @@
+package com.get.common.query.model;
+
+import com.get.common.query.annotation.QueryField;
+import lombok.AllArgsConstructor;
+import lombok.Data;
+
+import java.lang.reflect.Field;
+
+/**
+ * 字段信息封装类
+ * 用于缓存反射获取的字段信息和注解信息
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Data
+@AllArgsConstructor
+public class FieldInfo {
+    
+    /**
+     * 反射字段对象
+     */
+    private Field field;
+    
+    /**
+     * 查询字段注解
+     */
+    private QueryField queryField;
+    
+    /**
+     * 数据库字段名（已处理过的）
+     */
+    private String columnName;
+    
+    /**
+     * 字段类型
+     */
+    private Class<?> fieldType;
+    
+    /**
+     * 是否为集合类型
+     */
+    private boolean isCollection;
+    
+    /**
+     * 构造函数
+     */
+    public FieldInfo(Field field, QueryField queryField) {
+        this.field = field;
+        this.queryField = queryField;
+        this.fieldType = field.getType();
+        this.isCollection = java.util.Collection.class.isAssignableFrom(fieldType);
+        
+        // 处理数据库字段名
+        if (queryField.column().isEmpty()) {
+            // 将驼峰命名转换为下划线命名
+            this.columnName = camelToUnderscore(field.getName());
+        } else {
+            this.columnName = queryField.column();
+        }
+    }
+    
+    /**
+     * 驼峰命名转下划线命名
+     * 正确处理连续大写字母，如：XMLHttpRequest -> xml_http_request
+     * 
+     * @param camelCase 驼峰命名字符串
+     * @return 下划线命名字符串
+     */
+    private String camelToUnderscore(String camelCase) {
+        if (camelCase == null || camelCase.isEmpty()) {
+            return camelCase;
+        }
+        
+        StringBuilder result = new StringBuilder();
+        
+        for (int i = 0; i < camelCase.length(); i++) {
+            char current = camelCase.charAt(i);
+            
+            // 第一个字符，直接添加小写形式
+            if (i == 0) {
+                result.append(Character.toLowerCase(current));
+                continue;
+            }
+            
+            char previous = camelCase.charAt(i - 1);
+            
+            if (Character.isUpperCase(current)) {
+                // 当前字符是大写字母
+                boolean needUnderscore = false;
+                
+                if (Character.isLowerCase(previous)) {
+                    // 前一个字符是小写，需要添加下划线（如：userId -> user_id）
+                    needUnderscore = true;
+                } else if (Character.isUpperCase(previous) && i + 1 < camelCase.length()) {
+                    // 前一个字符也是大写，检查下一个字符
+                    char next = camelCase.charAt(i + 1);
+                    if (Character.isLowerCase(next)) {
+                        // 连续大写字母后跟小写字母，当前大写字母是新单词的开始
+                        // 如：XMLHttp -> xml_http（在H前添加下划线）
+                        needUnderscore = true;
+                    }
+                }
+                
+                if (needUnderscore) {
+                    result.append('_');
+                }
+                result.append(Character.toLowerCase(current));
+            } else {
+                // 当前字符不是大写字母，直接添加
+                result.append(current);
+            }
+        }
+        
+        return result.toString();
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/exception/DynamicQueryException.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/exception/DynamicQueryException.java b/common/src/main/java/com/get/common/query/exception/DynamicQueryException.java
new file mode 100644
--- /dev/null	(date 1755514338175)
+++ b/common/src/main/java/com/get/common/query/exception/DynamicQueryException.java	(date 1755514338175)
@@ -0,0 +1,59 @@
+package com.get.common.query.exception;
+
+import lombok.Getter;
+
+/**
+ * 动态查询基础异常类
+ * 所有动态查询相关异常的父类
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Getter
+public class DynamicQueryException extends RuntimeException {
+
+    private final String errorCode;
+    private final Object[] params;
+
+    public DynamicQueryException(String message) {
+        super(message);
+        this.errorCode = "DYNAMIC_QUERY_ERROR";
+        this.params = new Object[0];
+    }
+
+    public DynamicQueryException(String message, Throwable cause) {
+        super(message, cause);
+        this.errorCode = "DYNAMIC_QUERY_ERROR";
+        this.params = new Object[0];
+    }
+
+    public DynamicQueryException(String errorCode, String message, Object... params) {
+        super(message);
+        this.errorCode = errorCode;
+        this.params = params != null ? params : new Object[0];
+    }
+
+    public DynamicQueryException(String errorCode, String message, Throwable cause, Object... params) {
+        super(message, cause);
+        this.errorCode = errorCode;
+        this.params = params != null ? params : new Object[0];
+    }
+
+    /**
+     * 获取详细的错误信息
+     */
+    public String getDetailedMessage() {
+        StringBuilder sb = new StringBuilder();
+        sb.append("[").append(errorCode).append("] ").append(getMessage());
+        
+        if (params.length > 0) {
+            sb.append(" - 参数: ");
+            for (int i = 0; i < params.length; i++) {
+                if (i > 0) sb.append(", ");
+                sb.append(params[i]);
+            }
+        }
+        
+        return sb.toString();
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/annotation/QueryField.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/annotation/QueryField.java b/common/src/main/java/com/get/common/query/annotation/QueryField.java
new file mode 100644
--- /dev/null	(date 1755514338167)
+++ b/common/src/main/java/com/get/common/query/annotation/QueryField.java	(date 1755514338167)
@@ -0,0 +1,67 @@
+package com.get.common.query.annotation;
+
+import com.get.common.query.enums.LogicType;
+import com.get.common.query.enums.QueryType;
+
+import java.lang.annotation.ElementType;
+import java.lang.annotation.Retention;
+import java.lang.annotation.RetentionPolicy;
+import java.lang.annotation.Target;
+
+/**
+ * 查询字段注解
+ * 用于标记DTO字段的查询方式，支持动态查询条件构建
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Target(ElementType.FIELD)
+@Retention(RetentionPolicy.RUNTIME)
+public @interface QueryField {
+    
+    /**
+     * 查询类型
+     */
+    QueryType type() default QueryType.EQ;
+    
+    /**
+     * 数据库字段名（默认使用字段名转下划线）
+     * 如果指定了column，则使用指定的字段名
+     */
+    String column() default "";
+    
+    /**
+     * 是否忽略空值
+     * true: 当字段值为null、空字符串、空集合时忽略该条件
+     * false: 不忽略空值
+     */
+    boolean ignoreEmpty() default true;
+    
+    /**
+     * 条件组合方式
+     */
+    LogicType logic() default LogicType.AND;
+    
+    /**
+     * 排序字段（当type为ORDER_BY时使用）
+     * true: 升序，false: 降序
+     */
+    boolean asc() default true;
+    
+    /**
+     * 查询条件的优先级，数字越小优先级越高
+     * 用于控制查询条件的执行顺序
+     */
+    int priority() default 0;
+    
+    /**
+     * 查询条件分组，用于实现复杂的条件组合
+     * 相同分组内的条件使用AND连接，不同分组间使用OR连接
+     * 默认为"default"，表示不分组，所有条件按logic()参数执行
+     * 示例：
+     * - group="group1"的条件会用AND连接成一组
+     * - group="group2"的条件会用AND连接成另一组
+     * - 然后两组之间用OR连接：(group1_conditions) OR (group2_conditions)
+     */
+    String group() default "default";
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/enums/QueryType.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/enums/QueryType.java b/common/src/main/java/com/get/common/query/enums/QueryType.java
new file mode 100644
--- /dev/null	(date 1755514338131)
+++ b/common/src/main/java/com/get/common/query/enums/QueryType.java	(date 1755514338131)
@@ -0,0 +1,114 @@
+package com.get.common.query.enums;
+
+/**
+ * 查询类型枚举
+ * 定义支持的查询条件类型
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public enum QueryType {
+    
+    /**
+     * 等于查询
+     * SQL: column = value
+     */
+    EQ,
+    
+    /**
+     * 不等于查询
+     * SQL: column != value
+     */
+    NE,
+    
+    /**
+     * 模糊查询（两边模糊）
+     * SQL: column LIKE '%value%'
+     */
+    LIKE,
+    
+    /**
+     * 左模糊查询
+     * SQL: column LIKE '%value'
+     */
+    LIKE_LEFT,
+    
+    /**
+     * 右模糊查询
+     * SQL: column LIKE 'value%'
+     */
+    LIKE_RIGHT,
+    
+    /**
+     * 大于查询
+     * SQL: column > value
+     */
+    GT,
+    
+    /**
+     * 大于等于查询
+     * SQL: column >= value
+     */
+    GE,
+    
+    /**
+     * 小于查询
+     * SQL: column < value
+     */
+    LT,
+    
+    /**
+     * 小于等于查询
+     * SQL: column <= value
+     */
+    LE,
+    
+    /**
+     * IN查询
+     * SQL: column IN (value1, value2, ...)
+     * 适用于Collection类型的字段
+     */
+    IN,
+    
+    /**
+     * NOT IN查询
+     * SQL: column NOT IN (value1, value2, ...)
+     * 适用于Collection类型的字段
+     */
+    NOT_IN,
+    
+    /**
+     * 为空查询
+     * SQL: column IS NULL
+     */
+    IS_NULL,
+    
+    /**
+     * 不为空查询
+     * SQL: column IS NOT NULL
+     */
+    IS_NOT_NULL,
+    
+    /**
+     * 范围查询
+     * SQL: column BETWEEN value1 AND value2
+     * 适用于List类型的字段，List必须包含两个元素
+     */
+    BETWEEN,
+    
+    /**
+     * 不在范围内查询
+     * SQL: column NOT BETWEEN value1 AND value2
+     */
+    NOT_BETWEEN,
+    
+    /**
+     * 排序字段（不生成WHERE条件，用于ORDER BY）
+     */
+    ORDER_BY,
+    
+    /**
+     * 忽略该字段（不生成任何SQL条件）
+     */
+    IGNORE
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/enums/LogicType.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/enums/LogicType.java b/common/src/main/java/com/get/common/query/enums/LogicType.java
new file mode 100644
--- /dev/null	(date 1755514338122)
+++ b/common/src/main/java/com/get/common/query/enums/LogicType.java	(date 1755514338122)
@@ -0,0 +1,23 @@
+package com.get.common.query.enums;
+
+/**
+ * 逻辑类型枚举
+ * 定义查询条件之间的逻辑关系
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public enum LogicType {
+    
+    /**
+     * AND逻辑
+     * SQL: condition1 AND condition2
+     */
+    AND,
+    
+    /**
+     * OR逻辑
+     * SQL: condition1 OR condition2
+     */
+    OR
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/model/QueryResult.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/model/QueryResult.java b/common/src/main/java/com/get/common/query/model/QueryResult.java
new file mode 100644
--- /dev/null	(date 1755514338137)
+++ b/common/src/main/java/com/get/common/query/model/QueryResult.java	(date 1755514338137)
@@ -0,0 +1,49 @@
+package com.get.common.query.model;
+
+import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
+import com.get.common.result.Page;
+import lombok.AllArgsConstructor;
+import lombok.Data;
+
+/**
+ * 查询结果封装类
+ * 包含QueryWrapper和分页信息
+ * 
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Data
+@AllArgsConstructor
+public class QueryResult<T> {
+    
+    /**
+     * 查询条件包装器
+     */
+    private QueryWrapper<T> wrapper;
+    
+    /**
+     * 分页信息
+     */
+    private Page page;
+    
+    /**
+     * 获取分页的当前页码
+     */
+    public Integer getCurrentPage() {
+        return page != null ? page.getCurrentPage() : null;
+    }
+    
+    /**
+     * 获取分页的页大小
+     */
+    public Integer getShowCount() {
+        return page != null ? page.getShowCount() : null;
+    }
+    
+    /**
+     * 是否需要分页
+     */
+    public boolean needPaging() {
+        return page != null && page.getShowCount() != null && page.getShowCount() > 0;
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/exception/QueryBuildException.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/exception/QueryBuildException.java b/common/src/main/java/com/get/common/query/exception/QueryBuildException.java
new file mode 100644
--- /dev/null	(date 1755514338211)
+++ b/common/src/main/java/com/get/common/query/exception/QueryBuildException.java	(date 1755514338211)
@@ -0,0 +1,44 @@
+package com.get.common.query.exception;
+
+/**
+ * 查询构建异常
+ * 在构建查询条件过程中发生的异常
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public class QueryBuildException extends DynamicQueryException {
+
+    public QueryBuildException(String message) {
+        super("QUERY_BUILD_ERROR", message);
+    }
+
+    public QueryBuildException(String message, Throwable cause) {
+        super("QUERY_BUILD_ERROR", message, cause);
+    }
+
+    public QueryBuildException(String message, Class<?> dtoClass, Class<?> entityClass) {
+        super("QUERY_BUILD_ERROR", message, dtoClass.getSimpleName(), entityClass.getSimpleName());
+    }
+
+    public QueryBuildException(String message, Throwable cause, Class<?> dtoClass, Class<?> entityClass) {
+        super("QUERY_BUILD_ERROR", message, cause, dtoClass.getSimpleName(), entityClass.getSimpleName());
+    }
+
+    /**
+     * 创建查询构建失败异常
+     */
+    public static QueryBuildException buildFailed(Class<?> dtoClass, Class<?> entityClass, Throwable cause) {
+        String message = String.format("构建查询条件失败 - 查询DTO: %s, 实体类: %s", 
+            dtoClass.getSimpleName(), entityClass.getSimpleName());
+        return new QueryBuildException(message, cause, dtoClass, entityClass);
+    }
+
+    /**
+     * 创建分组查询构建失败异常
+     */
+    public static QueryBuildException groupBuildFailed(String groupName, Throwable cause) {
+        String message = String.format("构建分组查询条件失败 - 分组: %s", groupName);
+        return new QueryBuildException(message, cause);
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/exception/ParameterValidationException.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/exception/ParameterValidationException.java b/common/src/main/java/com/get/common/query/exception/ParameterValidationException.java
new file mode 100644
--- /dev/null	(date 1755514338185)
+++ b/common/src/main/java/com/get/common/query/exception/ParameterValidationException.java	(date 1755514338185)
@@ -0,0 +1,70 @@
+package com.get.common.query.exception;
+
+import com.get.common.query.enums.QueryType;
+
+/**
+ * 参数验证异常
+ * 在验证查询参数时发生的异常
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public class ParameterValidationException extends DynamicQueryException {
+
+    public ParameterValidationException(String message) {
+        super("PARAMETER_VALIDATION_ERROR", message);
+    }
+
+    public ParameterValidationException(String message, Object... params) {
+        super("PARAMETER_VALIDATION_ERROR", message, params);
+    }
+
+    /**
+     * 创建集合参数类型错误异常
+     */
+    public static ParameterValidationException invalidCollectionType(QueryType queryType, Object value) {
+        String message = String.format("%s查询参数不是集合类型", queryType);
+        return new ParameterValidationException(message, queryType, value.getClass().getSimpleName());
+    }
+
+    /**
+     * 创建集合参数为空异常
+     */
+    public static ParameterValidationException emptyCollection(QueryType queryType) {
+        String message = String.format("%s查询的集合参数为空", queryType);
+        return new ParameterValidationException(message, queryType);
+    }
+
+    /**
+     * 创建范围参数类型错误异常
+     */
+    public static ParameterValidationException invalidRangeType(QueryType queryType, Object value) {
+        String message = String.format("%s查询参数不是List类型", queryType);
+        return new ParameterValidationException(message, queryType, value.getClass().getSimpleName());
+    }
+
+    /**
+     * 创建范围参数大小错误异常
+     */
+    public static ParameterValidationException invalidRangeSize(QueryType queryType, int actualSize) {
+        String message = String.format("%s查询参数List大小不为2", queryType);
+        return new ParameterValidationException(message, queryType, actualSize);
+    }
+
+    /**
+     * 创建范围参数包含null值异常
+     */
+    public static ParameterValidationException rangeContainsNull(QueryType queryType, Object start, Object end) {
+        String message = String.format("%s查询参数包含null值", queryType);
+        return new ParameterValidationException(message, queryType, start, end);
+    }
+
+    /**
+     * 创建范围值类型不兼容异常
+     */
+    public static ParameterValidationException incompatibleRangeTypes(Object start, Object end) {
+        String message = "范围值类型不兼容，无法比较大小";
+        return new ParameterValidationException(message, 
+            start.getClass().getSimpleName(), end.getClass().getSimpleName());
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/exception/QueryTypeNotSupportedException.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/exception/QueryTypeNotSupportedException.java b/common/src/main/java/com/get/common/query/exception/QueryTypeNotSupportedException.java
new file mode 100644
--- /dev/null	(date 1755514338202)
+++ b/common/src/main/java/com/get/common/query/exception/QueryTypeNotSupportedException.java	(date 1755514338202)
@@ -0,0 +1,42 @@
+package com.get.common.query.exception;
+
+import com.get.common.query.enums.QueryType;
+
+/**
+ * 查询类型不支持异常
+ * 当使用了不支持的查询类型时抛出
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+public class QueryTypeNotSupportedException extends DynamicQueryException {
+
+    public QueryTypeNotSupportedException(String message) {
+        super("QUERY_TYPE_NOT_SUPPORTED", message);
+    }
+
+    public QueryTypeNotSupportedException(String message, Object... params) {
+        super("QUERY_TYPE_NOT_SUPPORTED", message, params);
+    }
+
+    /**
+     * 创建不支持的查询类型异常
+     */
+    public static QueryTypeNotSupportedException unsupported(QueryType queryType, String fieldName, String columnName) {
+        String message = String.format("不支持的查询类型 - 类型: %s, 字段: %s, 数据库字段: %s", 
+            queryType, fieldName, columnName);
+        return new QueryTypeNotSupportedException(message, queryType, fieldName, columnName);
+    }
+
+    /**
+     * 创建查询类型与字段类型不匹配异常
+     */
+    public static QueryTypeNotSupportedException typeMismatch(QueryType queryType, String fieldName, 
+                                                             Class<?> fieldType, Object value) {
+        String message = String.format("查询类型与字段类型不匹配 - 查询类型: %s, 字段: %s, 字段类型: %s, 值类型: %s", 
+            queryType, fieldName, fieldType.getSimpleName(), 
+            value != null ? value.getClass().getSimpleName() : "null");
+        return new QueryTypeNotSupportedException(message, queryType, fieldName, 
+            fieldType.getSimpleName(), value != null ? value.getClass().getSimpleName() : "null");
+    }
+}
\ No newline at end of file
Index: common/src/main/java/com/get/common/query/monitor/QueryMetricsCollector.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/common/src/main/java/com/get/common/query/monitor/QueryMetricsCollector.java b/common/src/main/java/com/get/common/query/monitor/QueryMetricsCollector.java
new file mode 100644
--- /dev/null	(date 1755514338194)
+++ b/common/src/main/java/com/get/common/query/monitor/QueryMetricsCollector.java	(date 1755514338194)
@@ -0,0 +1,468 @@
+package com.get.common.query.monitor;
+
+import com.get.common.query.cache.CacheKeyGenerator;
+import lombok.AllArgsConstructor;
+import lombok.Getter;
+import lombok.RequiredArgsConstructor;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Value;
+import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
+import org.springframework.stereotype.Component;
+
+import javax.annotation.PostConstruct;
+import java.time.LocalDateTime;
+import java.time.format.DateTimeFormatter;
+import java.util.*;
+import java.util.concurrent.ConcurrentHashMap;
+import java.util.concurrent.atomic.AtomicLong;
+import java.util.concurrent.atomic.LongAdder;
+import java.util.stream.Collectors;
+
+/**
+ * 查询指标收集器
+ *
+ * <AUTHOR>
+ * @since 2025-01-30
+ */
+@Slf4j
+@Component
+@ConditionalOnProperty(
+    name = "dynamic.query.metrics.enabled",
+    havingValue = "true",
+    matchIfMissing = true
+)
+@RequiredArgsConstructor
+public class QueryMetricsCollector {
+    
+    private final CacheKeyGenerator keyGenerator;
+    
+    /**
+     * 性能指标存储
+     */
+    private final Map<String, QueryMetrics> metricsMap = new ConcurrentHashMap<>();
+    
+    /**
+     * 慢查询阈值（毫秒）
+     */
+    @Value("${dynamic.query.metrics.slow-query-threshold:1000}")
+    private long slowQueryThreshold;
+    
+    /**
+     * 是否启用详细监控
+     */
+    @Value("${dynamic.query.metrics.detailed-monitoring:true}")
+    private boolean detailedMonitoring;
+    
+    /**
+     * 慢查询记录（使用环形缓冲区）
+     */
+    private volatile SlowQueryRecord[] slowQueryBuffer;
+    private final AtomicLong slowQueryIndex = new AtomicLong(0);
+    
+    /**
+     * 最大慢查询记录数
+     */
+    @Value("${dynamic.query.metrics.max-slow-queries:100}")
+    private int maxSlowQueries;
+    
+    /**
+     * 监控数据清理间隔（分钟）
+     */
+    @Value("${dynamic.query.metrics.cleanup-interval:60}")
+    private int cleanupIntervalMinutes;
+    
+    @PostConstruct
+    public void init() {
+        // 初始化慢查询环形缓冲区
+        slowQueryBuffer = new SlowQueryRecord[maxSlowQueries];
+        
+        log.info("查询指标收集器初始化完成 - 慢查询阈值: {}ms, 详细监控: {}, 最大慢查询记录: {}, 清理间隔: {}分钟", 
+            slowQueryThreshold, detailedMonitoring, maxSlowQueries, cleanupIntervalMinutes);
+    }
+    
+    /**
+     * 记录查询执行开始
+     */
+    public QueryExecutionContext startQuery(Class<?> dtoClass, Class<?> entityClass) {
+        if (!detailedMonitoring) {
+            return new QueryExecutionContext(dtoClass, entityClass, System.currentTimeMillis(), false);
+        }
+        
+        String key = getMetricsKey(dtoClass, entityClass);
+        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, entityClass));
+        
+        metrics.incrementTotalQueries();
+        
+        return new QueryExecutionContext(dtoClass, entityClass, System.currentTimeMillis(), true);
+    }
+    
+    /**
+     * 记录查询执行结束
+     */
+    public void endQuery(QueryExecutionContext context, boolean success, String errorMessage) {
+        if (!context.isMonitored()) {
+            return;
+        }
+        
+        long executionTime = System.currentTimeMillis() - context.getStartTime();
+        String key = getMetricsKey(context.getDtoClass(), context.getEntityClass());
+        QueryMetrics metrics = metricsMap.get(key);
+        
+        if (metrics != null) {
+            metrics.addExecutionTime(executionTime);
+            
+            if (success) {
+                metrics.incrementSuccessQueries();
+            } else {
+                metrics.incrementFailedQueries();
+                if (errorMessage != null) {
+                    metrics.addErrorMessage(errorMessage);
+                }
+            }
+            
+            // 记录慢查询
+            if (executionTime >= slowQueryThreshold) {
+                recordSlowQuery(context, executionTime, errorMessage);
+                metrics.incrementSlowQueries();
+            }
+        }
+        
+        // 记录性能日志
+        if (executionTime >= slowQueryThreshold) {
+            log.warn("慢查询检测 - DTO: {}, Entity: {}, 耗时: {}ms{}", 
+                context.getDtoClass().getSimpleName(), 
+                context.getEntityClass().getSimpleName(),
+                executionTime,
+                success ? "" : ", 错误: " + errorMessage);
+        } else if (log.isDebugEnabled()) {
+            log.debug("查询完成 - DTO: {}, Entity: {}, 耗时: {}ms, 结果: {}", 
+                context.getDtoClass().getSimpleName(), 
+                context.getEntityClass().getSimpleName(),
+                executionTime,
+                success ? "成功" : "失败");
+        }
+    }
+    
+    /**
+     * 记录缓存命中
+     */
+    public void recordCacheHit(Class<?> dtoClass) {
+        if (!detailedMonitoring) {
+            return;
+        }
+        
+        String key = getMetricsKey(dtoClass, null);
+        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, null));
+        metrics.incrementCacheHits();
+    }
+    
+    /**
+     * 记录缓存未命中
+     */
+    public void recordCacheMiss(Class<?> dtoClass) {
+        if (!detailedMonitoring) {
+            return;
+        }
+        
+        String key = getMetricsKey(dtoClass, null);
+        QueryMetrics metrics = metricsMap.computeIfAbsent(key, k -> new QueryMetrics(dtoClass, null));
+        metrics.incrementCacheMisses();
+    }
+    
+    /**
+     * 获取所有指标
+     */
+    public Map<String, Object> getAllMetrics() {
+        Map<String, Object> result = new HashMap<>();
+        
+        // 总体统计
+        long totalQueries = metricsMap.values().stream().mapToLong(m -> m.getTotalQueries().sum()).sum();
+        long totalSuccess = metricsMap.values().stream().mapToLong(m -> m.getSuccessQueries().sum()).sum();
+        long totalFailed = metricsMap.values().stream().mapToLong(m -> m.getFailedQueries().sum()).sum();
+        long totalSlowQueries = metricsMap.values().stream().mapToLong(m -> m.getSlowQueries().sum()).sum();
+        
+        Map<String, Object> overall = new HashMap<>();
+        overall.put("totalQueries", totalQueries);
+        overall.put("successQueries", totalSuccess);
+        overall.put("failedQueries", totalFailed);
+        overall.put("slowQueries", totalSlowQueries);
+        overall.put("successRate", totalQueries > 0 ? String.format("%.2f%%", (double) totalSuccess / totalQueries * 100) : "0.00%");
+        overall.put("slowQueryRate", totalQueries > 0 ? String.format("%.2f%%", (double) totalSlowQueries / totalQueries * 100) : "0.00%");
+        
+        result.put("overall", overall);
+        
+        // 详细指标
+        Map<String, Object> detailed = new HashMap<>();
+        metricsMap.forEach((key, metrics) -> {
+            Map<String, Object> detail = new HashMap<>();
+            detail.put("dtoClass", metrics.getDtoClass().getSimpleName());
+            if (metrics.getEntityClass() != null) {
+                detail.put("entityClass", metrics.getEntityClass().getSimpleName());
+            }
+            detail.put("totalQueries", metrics.getTotalQueries().sum());
+            detail.put("successQueries", metrics.getSuccessQueries().sum());
+            detail.put("failedQueries", metrics.getFailedQueries().sum());
+            detail.put("slowQueries", metrics.getSlowQueries().sum());
+            detail.put("cacheHits", metrics.getCacheHits().sum());
+            detail.put("cacheMisses", metrics.getCacheMisses().sum());
+            detail.put("avgExecutionTime", metrics.getAverageExecutionTime());
+            detail.put("maxExecutionTime", metrics.getMaxExecutionTime());
+            detail.put("minExecutionTime", metrics.getMinExecutionTime());
+            detail.put("recentErrors", new ArrayList<>(metrics.getRecentErrors()));
+            
+            detailed.put(key, detail);
+        });
+        result.put("detailed", detailed);
+        
+        // 慢查询记录（从环形缓冲区读取）
+        List<Map<String, Object>> slowQueryDetails = new ArrayList<>();
+        SlowQueryRecord[] bufferSnapshot = slowQueryBuffer; // 快照，避免并发修改
+        
+        if (bufferSnapshot != null) {
+            for (SlowQueryRecord record : bufferSnapshot) {
+                if (record != null) { // 缓冲区可能还未完全填充
+                    Map<String, Object> slowQuery = new HashMap<>();
+                    slowQuery.put("dtoClass", record.getDtoClass().getSimpleName());
+                    slowQuery.put("entityClass", record.getEntityClass().getSimpleName());
+                    slowQuery.put("executionTime", record.getExecutionTime());
+                    slowQuery.put("timestamp", record.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
+                    if (record.getErrorMessage() != null) {
+                        slowQuery.put("errorMessage", record.getErrorMessage());
+                    }
+                    slowQueryDetails.add(slowQuery);
+                }
+            }
+        }
+        result.put("slowQueries", slowQueryDetails);
+        
+        return result;
+    }
+    
+    /**
+     * 获取性能报告
+     */
+    public Map<String, Object> getPerformanceReport() {
+        Map<String, Object> report = new HashMap<>();
+        
+        // 性能等级评估
+        long totalQueries = metricsMap.values().stream().mapToLong(m -> m.getTotalQueries().sum()).sum();
+        long totalSlowQueries = metricsMap.values().stream().mapToLong(m -> m.getSlowQueries().sum()).sum();
+        double slowQueryRate = totalQueries > 0 ? (double) totalSlowQueries / totalQueries : 0;
+        
+        String performanceGrade;
+        if (slowQueryRate <= 0.01) {
+            performanceGrade = "EXCELLENT";
+        } else if (slowQueryRate <= 0.05) {
+            performanceGrade = "GOOD";
+        } else if (slowQueryRate <= 0.1) {
+            performanceGrade = "FAIR";
+        } else {
+            performanceGrade = "POOR";
+        }
+        
+        report.put("performanceGrade", performanceGrade);
+        report.put("slowQueryRate", String.format("%.4f%%", slowQueryRate * 100));
+        report.put("slowQueryThreshold", slowQueryThreshold + "ms");
+        
+        // Top 慢查询类型
+        List<Map<String, Object>> topSlowQueries = metricsMap.entrySet().stream()
+                .filter(entry -> entry.getValue().getSlowQueries().sum() > 0)
+                .sorted((e1, e2) -> Long.compare(e2.getValue().getSlowQueries().sum(), e1.getValue().getSlowQueries().sum()))
+                .limit(10)
+                .map(entry -> {
+                    Map<String, Object> item = new HashMap<>();
+                    item.put("key", entry.getKey());
+                    item.put("slowQueries", entry.getValue().getSlowQueries().sum());
+                    item.put("avgExecutionTime", entry.getValue().getAverageExecutionTime());
+                    return item;
+                })
+                .collect(Collectors.toList());
+        report.put("topSlowQueries", topSlowQueries);
+        
+        // 建议
+        List<String> recommendations = new ArrayList<>();
+        if (slowQueryRate > 0.1) {
+            recommendations.add("慢查询比例过高，建议优化查询条件或添加数据库索引");
+        }
+        if (totalQueries > 10000 && slowQueryRate > 0.05) {
+            recommendations.add("高并发场景下建议启用查询结果缓存");
+        }
+        if (recommendations.isEmpty()) {
+            recommendations.add("查询性能良好，继续保持");
+        }
+        report.put("recommendations", recommendations);
+        
+        return report;
+    }
+    
+    /**
+     * 清空所有指标
+     */
+    public void clearAllMetrics() {
+        metricsMap.clear();
+        
+        // 清空环形缓冲区
+        if (slowQueryBuffer != null) {
+            for (int i = 0; i < slowQueryBuffer.length; i++) {
+                slowQueryBuffer[i] = null;
+            }
+        }
+        slowQueryIndex.set(0);
+        
+        log.info("所有查询指标已清空");
+    }
+    
+    /**
+     * 清空指定类的指标
+     */
+    public void clearMetrics(Class<?> dtoClass, Class<?> entityClass) {
+        String key = getMetricsKey(dtoClass, entityClass);
+        metricsMap.remove(key);
+        log.debug("清空指标 - key: {}", key);
+    }
+    
+    /**
+     * 生成指标键（使用统一的key生成器）
+     */
+    private String getMetricsKey(Class<?> dtoClass, Class<?> entityClass) {
+        return keyGenerator.generateMetricsKey(dtoClass, entityClass);
+    }
+    
+    /**
+     * 记录慢查询（使用无锁环形缓冲区）
+     */
+    private void recordSlowQuery(QueryExecutionContext context, long executionTime, String errorMessage) {
+        SlowQueryRecord record = new SlowQueryRecord(
+            context.getDtoClass(),
+            context.getEntityClass(),
+            executionTime,
+            LocalDateTime.now(),
+            errorMessage
+        );
+        
+        // 使用原子操作获取下一个索引位置
+        long index = slowQueryIndex.getAndIncrement();
+        int bufferIndex = (int) (index % maxSlowQueries);
+        
+        // 无锁写入环形缓冲区
+        slowQueryBuffer[bufferIndex] = record;
+        
+        log.debug("记录慢查询 - 索引: {}, 缓冲区位置: {}, 执行时间: {}ms", 
+            index, bufferIndex, executionTime);
+    }
+    
+    /**
+     * 查询执行上下文
+     */
+    @Getter
+    @AllArgsConstructor
+    public static class QueryExecutionContext {
+        private final Class<?> dtoClass;
+        private final Class<?> entityClass;
+        private final long startTime;
+        private final boolean monitored;
+    }
+    
+    /**
+     * 慢查询记录
+     */
+    @Getter
+    @AllArgsConstructor
+    private static class SlowQueryRecord {
+        private final Class<?> dtoClass;
+        private final Class<?> entityClass;
+        private final long executionTime;
+        private final LocalDateTime timestamp;
+        private final String errorMessage;
+    }
+    
+    /**
+     * 查询指标
+     */
+    @Getter
+    private static class QueryMetrics {
+        private final Class<?> dtoClass;
+        private final Class<?> entityClass;
+        private final LongAdder totalQueries = new LongAdder();
+        private final LongAdder successQueries = new LongAdder();
+        private final LongAdder failedQueries = new LongAdder();
+        private final LongAdder slowQueries = new LongAdder();
+        private final LongAdder cacheHits = new LongAdder();
+        private final LongAdder cacheMisses = new LongAdder();
+        private final LongAdder totalExecutionTime = new LongAdder();
+        private final AtomicLong maxExecutionTime = new AtomicLong(0);
+        private final AtomicLong minExecutionTime = new AtomicLong(Long.MAX_VALUE);
+        private final String[] recentErrorsBuffer = new String[10]; // 固定大小的错误环形缓冲区
+        private final AtomicLong errorIndex = new AtomicLong(0);
+        
+        public QueryMetrics(Class<?> dtoClass, Class<?> entityClass) {
+            this.dtoClass = dtoClass;
+            this.entityClass = entityClass;
+        }
+        
+        public void incrementTotalQueries() {
+            totalQueries.increment();
+        }
+        
+        public void incrementSuccessQueries() {
+            successQueries.increment();
+        }
+        
+        public void incrementFailedQueries() {
+            failedQueries.increment();
+        }
+        
+        public void incrementSlowQueries() {
+            slowQueries.increment();
+        }
+        
+        public void incrementCacheHits() {
+            cacheHits.increment();
+        }
+        
+        public void incrementCacheMisses() {
+            cacheMisses.increment();
+        }
+        
+        public void addExecutionTime(long time) {
+            totalExecutionTime.add(time);
+            maxExecutionTime.updateAndGet(current -> Math.max(current, time));
+            minExecutionTime.updateAndGet(current -> current == Long.MAX_VALUE ? time : Math.min(current, time));
+        }
+        
+        public void addErrorMessage(String errorMessage) {
+            if (errorMessage != null) {
+                // 使用无锁环形缓冲区
+                long index = errorIndex.getAndIncrement();
+                int bufferIndex = (int) (index % recentErrorsBuffer.length);
+                recentErrorsBuffer[bufferIndex] = errorMessage;
+            }
+        }
+        
+        public double getAverageExecutionTime() {
+            long total = totalQueries.sum();
+            return total > 0 ? (double) totalExecutionTime.sum() / total : 0;
+        }
+        
+        public long getMaxExecutionTime() {
+            return maxExecutionTime.get() == 0 ? 0 : maxExecutionTime.get();
+        }
+        
+        public long getMinExecutionTime() {
+            return minExecutionTime.get() == Long.MAX_VALUE ? 0 : minExecutionTime.get();
+        }
+        
+        public List<String> getRecentErrors() {
+            List<String> errors = new ArrayList<>();
+            // 快照读取，避免并发修改
+            String[] bufferSnapshot = recentErrorsBuffer.clone();
+            
+            for (String error : bufferSnapshot) {
+                if (error != null) {
+                    errors.add(error);
+                }
+            }
+            return errors;
+        }
+    }
+}
\ No newline at end of file
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>Jenkinsfile\r\n.idea/*\r\ntarget/*\r\nDockerfile\r\napplication-dev.yml\r\napplication-gray.yml\r\napplication-iae.yml\r\napplication-prod.yml\r\napplication-test.yml\r\napplication-tw.yml\r\n\r\nHELP.md\r\nlogs\r\ntarget/\r\n!.mvn/wrapper/maven-wrapper.jar\r\n!**/src/main/**/target/\r\n!**/src/test/**/target/\r\n.mvn\r\nmvnw.cmd\r\n\r\n### STS ###\r\n.apt_generated\r\n.classpath\r\n.factorypath\r\n.project\r\n.settings\r\n.springBeans\r\n.sts4-cache\r\n\r\n### IntelliJ IDEA ###\r\n.idea\r\n*.iws\r\n*.iml\r\n*.ipr\r\nmvnw\r\n\r\n### NetBeans ###\r\n/nbproject/private/\r\n/nbbuild/\r\n/dist/\r\n/nbdist/\r\n/.nb-gradle/\r\nbuild/\r\n!**/src/main/**/build/\r\n!**/src/test/**/build/\r\n\r\n### VS Code ###\r\n.vscode/\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 77fba8fa1fdba78c31d963007385bbec2b5eaf99)
+++ b/.gitignore	(date 1755514338155)
@@ -1,4 +1,4 @@
-Jenkinsfile
+cJenkinsfile
 .idea/*
 target/*
 Dockerfile
@@ -47,3 +47,9 @@
 ### VS Code ###
 .vscode/
 
+*.log
+*.md
+claude-data/**
+*.properties
+rebel.xml
+Test**
\ No newline at end of file
