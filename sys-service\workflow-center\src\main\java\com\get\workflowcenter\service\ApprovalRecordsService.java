package com.get.workflowcenter.service;

import com.get.common.result.Page;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.dto.BatchApproveDto;
import com.get.workflowcenter.vo.ApprovalRecordListVo;

import java.util.List;

public interface ApprovalRecordsService {

    /**
     * 审批列表
     *
     * @param approvalRecordDto
     * @return
     */
    List<ApprovalRecordListVo> datas(ApprovalRecordDto approvalRecordDto, Page page);

    /**
     * 批量审批
     * @param batchApproveDtoList
     */
    void batchApprove(List<BatchApproveDto> batchApproveDtoList);
}
