<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventMapper">
    <select id="getEventTargetList" resultType="java.lang.String">
        SELECT DISTINCT substring_index(substring_index(e.event_target, ',', b.help_topic_id + 1), ',',
                                        - 1) AS shareholder
        FROM m_event e
                 JOIN mysql.help_topic b
                      ON b.help_topic_id <![CDATA[ < ]]> (length(e.event_target) - length(REPLACE(e.event_target, ',', '')) + 1)
        where event_target != ""
    and
     fk_company_id = #{companyId}
        ORDER BY
            CONVERT (shareholder USING GBK) asc;
    </select>

    <select id="getEventThemeList" resultType="java.lang.String">
SELECT combined_data.event_theme
FROM (
         SELECT DISTINCT event_theme
         FROM m_event e
         WHERE e.fk_company_id = #{companyId}
         UNION ALL
         (
         SELECT DISTINCT event_theme
         FROM u_event_theme u
         WHERE u.fk_company_id = #{companyId})
     ) AS combined_data
GROUP BY event_theme
ORDER BY CONVERT(event_theme USING GBK) ASC;
    </select>

   <select id="getEventList" resultType="com.get.salecenter.entity.Event">
        SELECT
        a.*
        FROM
        m_event a
        left join u_event_type b on b.id = a.fk_event_type_id
        <where>

            <if test="eventDto.fkCompanyId != null">
                and a.fk_company_id = #{eventDto.fkCompanyId}
            </if>
            <!--不选所属公司时-->
            <if test="eventDto.fkCompanyIdList != null and eventDto.fkCompanyIdList.size() > 0">
                and a.fk_company_id in
                <foreach collection="eventDto.fkCompanyIdList" item="companyId" index="index" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <!--查询条件-所属公司-->

<!--            <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">-->
<!--                and a.fk_company_id = #{eventDto.fkCompanyId}-->
<!--            </if>-->

            <!--查询条件-活动类型-->
            <if test="eventDto.fkEventTypeId != null and eventDto.fkEventTypeId !=''">
                and a.fk_event_type_id = #{eventDto.fkEventTypeId}
            </if>
            <!--查询条件-活动对象国家-->
            <if test="eventDto.eventIds != null and eventDto.eventIds.size() > 0">
                and
                a.id
                IN
                <foreach collection="eventDto.eventIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--查询条件-开始日期-->
            <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
                and DATE_FORMAT(a.event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
            </if>
            <!--查询条件-结束日期-->
            <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
                and DATE_FORMAT(a.event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
            </if>

            <!--查询条件-创建开始日期-->
            <if test="eventDto.eventCreateTimeBeg != null and eventDto.eventCreateTimeBeg.toString() !=''">
                and DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventCreateTimeBeg},'%Y-%m-%d')
            </if>
            <!--查询条件-创建结束日期-->
            <if test="eventDto.eventCreateTimeEnd != null and eventDto.eventCreateTimeEnd.toString() !=''">
                and DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventCreateTimeEnd},'%Y-%m-%d')
            </if>


            <!--查询条件-修改开始日期-->
            <if test="eventDto.eventUpdateTimeBeg != null and eventDto.eventUpdateTimeBeg.toString() !=''">
                and DATE_FORMAT(a.gmt_modified,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventUpdateTimeBeg},'%Y-%m-%d')
            </if>
            <!--查询条件-修改结束日期-->
            <if test="eventDto.eventUpdateTimeEnd != null and eventDto.eventUpdateTimeEnd.toString() !=''">
                and DATE_FORMAT(a.gmt_modified,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventUpdateTimeEnd},'%Y-%m-%d')
            </if>

            <!--查询条件-国家-->
            <if test="eventDto.fkAreaCountryIdHold != null and eventDto.fkAreaCountryIdHold !=''">
                and a.fk_area_country_id_hold = #{eventDto.fkAreaCountryIdHold}
            </if>
            <!--查询条件-州省-->
            <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">
                and a.fk_area_state_id_hold = #{eventDto.fkAreaStateIdHold}
            </if>
            <!--查询条件-城市-->
            <if test="eventDto.fkAreaCityIdHold != null and eventDto.fkAreaCityIdHold !=''">
                and a.fk_area_city_id_hold = #{eventDto.fkAreaCityIdHold}
            </if>
            <!--查询条件-活动编号-->
            <if test="eventDto.num != null and eventDto.num !=''">
                and position(#{eventDto.num} in a.num)
            </if>
            <!--查询条件-活动主题-->
            <if test="eventDto.eventTheme != null and eventDto.eventTheme !=''">
                and position(#{eventDto.eventTheme} in a.event_theme)
                OR a.event_theme_note LIKE CONCAT('%', #{eventDto.eventTheme}, '%')
            </if>
            <!--查询条件-负责人-->
            <if test="eventDto.staffIds != null and eventDto.staffIds.size() > 0">
                and
                (a.fk_staff_id_leader1
                IN
                <foreach collection="eventDto.staffIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                a.fk_staff_id_leader2
                IN
                <foreach collection="eventDto.staffIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <!--查询条件-状态-->
            <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
                and
                a.status
                IN
                <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--查询条件-状态-->
            <if test="eventDto.status != null and eventDto.status !='' or eventDto.status == 0">
                and a.status = #{eventDto.status}
            </if>
            <!--查询条件-公开对象-->
            <if test="eventDto.publicLevel != null and eventDto.publicLevel !=''">
                AND (
                <foreach collection="eventDto.publicLevel.split(',')" item="status" separator=" AND ">
                    FIND_IN_SET(#{status}, a.public_level) > 0
                </foreach>
                )
            </if>
            <!--查询条件-活动类型多选-->
            <if test="eventDto.eventTypeIds != null and eventDto.eventTypeIds.size() > 0">
                and
                a.fk_event_type_id
                IN
                <foreach collection="eventDto.eventTypeIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="eventDto.isEventRegistrationStatus != null">
                <if test="eventDto.isEventRegistrationStatus">
                    AND EXISTS
                </if>
                <if test="!eventDto.isEventRegistrationStatus">
                    AND NOT EXISTS
                </if>
                 (SELECT 1 FROM m_event_registration b WHERE b.status = 2 AND b.fk_event_id = a.id)
            </if>
            <if test="fkDepartIds !=null">
                and (
                b.fk_department_ids is null
                <foreach collection="fkDepartIds" item="item" >
                    or FIND_IN_SET(#{item},b.fk_department_ids)
                </foreach>
                )
            </if>

        </where>
        ORDER BY
        if(isnull(a.event_time),0,1),a.event_time ${eventDto.orderType}
    </select>
    <select id="getEventDatasGroupByCountry" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_area_country_id_hold,
        count( id ) as eventCount,
        sum( attended_count ) as sumAttendedCount,
        sum( budget_amount ) as sumBudgetAmount,
        sum( actual_amount ) as sumActualAmount
        FROM
        m_event where fk_area_country_id_hold is not null
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        GROUP BY
        fk_area_country_id_hold
        ORDER BY
        count( id ) desc
    </select>
    <select id="getEventDatasGroupByAreaState" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_area_state_id_hold,
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event where 1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-国家-->
        <if test="eventDto.fkAreaCountryIdHold != null and eventDto.fkAreaCountryIdHold !=''">
            and fk_area_country_id_hold = #{eventDto.fkAreaCountryIdHold}
        </if>
        <!--查询条件-州省-->
        <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">
            and fk_area_state_id_hold = #{eventDto.fkAreaStateIdHold}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动举办区域多选-->
        <if test="eventDto.areaStateHoldIds != null and eventDto.areaStateHoldIds.size() > 0">
            and
            fk_area_state_id_hold
            IN
            <foreach collection="eventDto.areaStateHoldIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        fk_area_state_id_hold
        ORDER BY
        count( id ) desc,fk_area_state_id_hold asc
    </select>
    <select id="getEventDatasGroupByAreaCity" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_area_city_id_hold,
        count( id ) as eventCount,
        sum( attended_count ) as sumAttendedCount,
        sum( budget_amount ) as sumBudgetAmount,
        sum( actual_amount ) as sumActualAmount
        FROM
        m_event
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-州省-->
        <!--        <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">-->
        <!--            and fk_area_state_id_hold = #{eventDto.fkAreaStateIdHold}-->
        <!--        </if>-->
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <if test="eventDto.areaCityHoldIds != null and eventDto.areaCityHoldIds.size()>0">
            and fk_area_city_id_hold in
            <foreach collection="eventDto.areaCityHoldIds" item="eventCityId" index="index" open="(" separator="," close=")">
                #{eventCityId}
            </foreach>
            and  fk_area_city_id_hold is not null
        </if>
        <if test="eventDto.areaStateHoldIds != null and eventDto.areaStateHoldIds.size()>0">
            and (fk_area_city_id_hold in
            (SELECT c.id FROM ais_institution_center.u_area_city AS c WHERE
            c.fk_area_state_id in
            <foreach collection="eventDto.areaStateHoldIds" item="eventStateId" index="index" open="(" separator=","
                     close=")">
                #{eventStateId}
            </foreach>
            )
            OR ( fk_area_state_id_hold in
            <foreach collection="eventDto.areaStateHoldIds" item="eventStateId" index="index" open="(" separator=","
                     close=")">
                #{eventStateId}
            </foreach>
            and fk_area_city_id_hold is null) )
        </if>
        GROUP BY
        fk_area_city_id_hold
        ORDER BY
        count( id ) desc,fk_area_city_id_hold asc
    </select>

    <select id="getEventDatas" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        <if test="eventDto.fkAreaCountryIdHold != null and eventDto.fkAreaCountryIdHold !=''">
            fk_area_country_id_hold,
        </if>
        <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">
            fk_area_state_id_hold,
        </if>
        <if test="eventDto.fkStaffIdLeader1 != null and eventDto.fkStaffIdLeader1 !=''">
            fk_staff_id_leader1,
        </if>
        <if test="eventDto.fkEventTypeId != null and eventDto.fkEventTypeId !=''">
            fk_event_type_id,
        </if>
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event where 1=1
        <!--查询条件-所属公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <if test="eventDto.fkAreaCountryIdHold != null and eventDto.fkAreaCountryIdHold !=''">
            and  fk_area_country_id_hold = #{eventDto.fkAreaCountryIdHold}
        </if>
        <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">
            and  fk_area_state_id_hold = #{eventDto.fkAreaStateIdHold}
        </if>
        <if test="eventDto.fkStaffIdLeader1 != null and eventDto.fkStaffIdLeader1 !=''">
            and  fk_staff_id_leader1 = #{eventDto.fkStaffIdLeader1}
        </if>
        <if test="eventDto.fkEventTypeId != null and eventDto.fkEventTypeId !=''">
            and  fk_event_type_id = #{eventDto.fkEventTypeId}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getEventDatasGroupByStaffIdLeader" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_staff_id_leader1,
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-负责人-->
        <if test="eventDto.fkStaffIdLeader1 != null and eventDto.fkStaffIdLeader1 !=''">
            and fk_staff_id_leader1 = #{eventDto.fkStaffIdLeader1}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动负责人多选-->
        <if test="eventDto.fkStaffIdLeader1Ids != null and eventDto.fkStaffIdLeader1Ids.size() > 0">
            and
            fk_staff_id_leader1
            IN
            <foreach collection="eventDto.fkStaffIdLeader1Ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        fk_staff_id_leader1
        ORDER BY
        count( id ) desc,fk_staff_id_leader1 asc
    </select>

    <select id="getEventDatasGroupByStaffIdLeader2" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_staff_id_leader2,
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-第二负责人-->
        <if test="eventDto.fkStaffIdLeader2 != null and eventDto.fkStaffIdLeader2 !=''">
            and fk_staff_id_leader2 = #{eventDto.fkStaffIdLeader2}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动负责人多选-->
        <if test="eventDto.fkStaffIdLeader2Ids != null and eventDto.fkStaffIdLeader2Ids.size() > 0">
            and
            fk_staff_id_leader2
            IN
            <foreach collection="eventDto.fkStaffIdLeader2Ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        fk_staff_id_leader2
        ORDER BY
        count( id ) desc,fk_staff_id_leader2 asc
    </select>

    <select id="getEventDatasGroupByEventTypeId" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        fk_event_type_id,
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-活动类型-->
        <if test="eventDto.fkEventTypeId != null and eventDto.fkEventTypeId !=''">
            and fk_event_type_id = #{eventDto.fkEventTypeId}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动类型-->
        <if test="eventDto.eventTypeIds != null and eventDto.eventTypeIds.size() > 0">
            and
            fk_event_type_id
            IN
            <foreach collection="eventDto.eventTypeIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        fk_event_type_id
        ORDER BY
        count( id ) desc,fk_event_type_id asc
    </select>
    <select id="getEventStateList" resultType="java.lang.Long">
        SELECT
            fk_area_state_id_hold
        FROM
            m_event
        WHERE
            fk_area_state_id_hold IS NOT NULL and fk_company_id = #{companyId}
        GROUP BY
            fk_area_state_id_hold
    </select>

    <select id="getEventTargetCountryList" resultType="java.lang.Long">
        SELECT
            et.fk_area_country_id
        FROM
            r_event_target_area_country AS et
                INNER JOIN m_event AS e ON e.id = et.fk_event_id
        where et.fk_area_country_id IS NOT NULL and e.fk_company_id = #{companyId}
        GROUP BY et.fk_area_country_id
    </select>

    <select id="getEventStaffList" resultType="java.lang.Long">
        SELECT
            fk_staff_id_leader1
        FROM
            m_event
        WHERE
            fk_staff_id_leader1 IS NOT NULL and fk_company_id = #{companyId}
        GROUP BY
            fk_staff_id_leader1
    </select>
    <select id="getEventStaff2List" resultType="java.lang.Long">
        SELECT
            fk_staff_id_leader2
        FROM
            m_event
        WHERE
            fk_staff_id_leader2 IS NOT NULL and fk_company_id = #{companyId}
        GROUP BY
            fk_staff_id_leader2
    </select>


    <select id="getEventDatasGroupByTargetCountry" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        et.fk_area_country_id as fkAreaCountryIdHold,
        case when isnull(count( et.fk_area_country_id )) then 0 else count( et.fk_area_country_id ) end AS eventCount,
        case when isnull(sum( e.attended_count )) then 0 else sum( e.attended_count )  end AS sumAttendedCount,
        case when isnull(sum( e.budget_amount )) then 0 else sum( e.budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( e.actual_amount )) then 0 else sum(e.actual_amount)  end AS sumActualAmount
        FROM
        r_event_target_area_country AS et
        INNER JOIN m_event AS e ON e.id = et.fk_event_id
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and e.fk_company_id = #{eventDto.fkCompanyId}
        </if>

        <!--查询条件-对象国家-->
        <if test="eventDto.eventTargetCountryList != null and eventDto.eventTargetCountryList.size() > 0">
            and et.fk_area_country_id in
            <foreach collection="eventDto.eventTargetCountryList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(e.event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(e.event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            e.status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY et.fk_area_country_id
        ORDER BY eventCount desc,fkAreaCountryIdHold asc
    </select>

    <select id="getEventTypeList" resultType="java.lang.Long">
        SELECT
            a.fk_event_type_id
        FROM
            m_event a
            LEFT JOIN u_event_type b ON a.fk_event_type_id = b.id
        WHERE
             a.fk_event_type_id IS NOT NULL
             AND a.fk_company_id = #{companyId}
             <if test="fkDepartIds != null">
             and (
                 b.fk_department_ids is null
                 <foreach collection="fkDepartIds" item="item" >
                     or FIND_IN_SET(#{item},b.fk_department_ids)
                 </foreach>
             )
             </if>
        GROUP BY
            fk_event_type_id
    </select>
    <select id="getEventDataStatisticsByTargetCountry" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        case when isnull(count( et.fk_area_country_id )) then 0 else count( et.fk_area_country_id ) end AS eventCount,
        case when isnull(sum( e.attended_count )) then 0 else sum( e.attended_count )  end AS sumAttendedCount,
        case when isnull(sum( e.budget_amount )) then 0 else sum( e.budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( e.actual_amount )) then 0 else sum(e.actual_amount)  end AS sumActualAmount
        FROM
        r_event_target_area_country AS et
        INNER JOIN m_event AS e ON e.id = et.fk_event_id
        WHERE
        1=1
        <!--查询条件-公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and e.fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <!--查询条件-对象国家-->
        <if test="eventDto.eventTargetCountryList != null and eventDto.eventTargetCountryList.size() > 0">
            and et.fk_area_country_id in
            <foreach collection="eventDto.eventTargetCountryList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(e.event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(e.event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            e.status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getEventDataStatistics" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        case when isnull(count( id )) then 0 else count( id ) end AS eventCount,
        case when isnull(sum( attended_count )) then 0 else sum( attended_count )  end AS sumAttendedCount,
        case when isnull(sum( budget_amount )) then 0 else sum( budget_amount )  end AS sumBudgetAmount,
        case when isnull(sum( actual_amount )) then 0 else sum(actual_amount)  end AS sumActualAmount
        FROM
        m_event where 1=1
        <!--查询条件-所属公司-->
        <if test="eventDto.fkCompanyId != null and eventDto.fkCompanyId !=''">
            and fk_company_id = #{eventDto.fkCompanyId}
        </if>
        <if test="eventDto.fkAreaCountryIdHold != null and eventDto.fkAreaCountryIdHold !=''">
            and  fk_area_country_id_hold = #{eventDto.fkAreaCountryIdHold}
        </if>
        <if test="eventDto.fkAreaStateIdHold != null and eventDto.fkAreaStateIdHold !=''">
            and  fk_area_state_id_hold = #{eventDto.fkAreaStateIdHold}
        </if>
        <if test="eventDto.fkStaffIdLeader1 != null and eventDto.fkStaffIdLeader1 !=''">
            and  fk_staff_id_leader1 = #{eventDto.fkStaffIdLeader1}
        </if>
        <if test="eventDto.fkEventTypeId != null and eventDto.fkEventTypeId !=''">
            and  fk_event_type_id = #{eventDto.fkEventTypeId}
        </if>
        <!--查询条件-开始日期-->
        <if test="eventDto.eventTimeBeg != null and eventDto.eventTimeBeg.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventDto.eventTimeBeg},'%Y-%m-%d')
        </if>
        <!--查询条件-结束日期-->
        <if test="eventDto.eventTimeEnd != null and eventDto.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventDto.eventTimeEnd},'%Y-%m-%d')
        </if>
        <!--查询条件-状态-->
        <if test="eventDto.statusList != null and eventDto.statusList.size() > 0">
            and
            status
            IN
            <foreach collection="eventDto.statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-对象国家多选-->
        <if test="eventDto.eventTargetCountryList != null and eventDto.eventTargetCountryList.size() > 0">
            and et.fk_area_country_id in
            <foreach collection="eventDto.eventTargetCountryList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动举办区域多选-->
        <if test="eventDto.areaStateHoldIds != null and eventDto.areaStateHoldIds.size() > 0">
            <!--            <if test="eventDto.areaCityHoldIds != null and eventDto.areaCityHoldIds.size() > 0">-->
            <!--                and-->
            <!--                fk_area_city_id_hold-->
            <!--                IN-->
            <!--                <foreach collection="eventDto.areaCityHoldIds" item="item" index="index" open="(" separator="," close=")">-->
            <!--                    #{item}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <!--            <if test="eventDto.areaCityHoldIds == null and eventDto.areaCityHoldIds.size() == 0">-->
            and
            fk_area_state_id_hold
            IN
            <foreach collection="eventDto.areaStateHoldIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            <!--            </if>-->
        </if>
        <!--查询条件-活动举办城市多选-->
        <if test="eventDto.areaCityHoldIds != null and eventDto.areaCityHoldIds.size() > 0">
            and
            fk_area_city_id_hold
            IN
            <foreach collection="eventDto.areaCityHoldIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动负责人多选-->
        <if test="eventDto.fkStaffIdLeader1Ids != null and eventDto.fkStaffIdLeader1Ids.size() > 0">
            and
            fk_staff_id_leader1
            IN
            <foreach collection="eventDto.fkStaffIdLeader1Ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动第二负责人多选-->
        <if test="eventDto.fkStaffIdLeader2Ids != null and eventDto.fkStaffIdLeader2Ids.size() > 0">
            and
            fk_staff_id_leader2
            IN
            <foreach collection="eventDto.fkStaffIdLeader2Ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--查询条件-活动类型-->
        <if test="eventDto.eventTypeIds != null and eventDto.eventTypeIds.size() > 0">
            and
            fk_event_type_id
            IN
            <foreach collection="eventDto.eventTypeIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getEventCityList" resultType="java.lang.Long">
        SELECT
        fk_area_city_id_hold
        FROM
        m_event
        WHERE
        fk_area_city_id_hold IS NOT NULL and fk_company_id = #{companyId}
        <if test="eventStateIds != null and eventStateIds.size()>0">
            and fk_area_state_id_hold in
            <foreach collection="eventStateIds" item="eventStateId" index="index" open="(" separator="," close=")">
                #{eventStateId}
            </foreach>
        </if>
        GROUP BY
        fk_area_city_id_hold
    </select>

    <select id="getEventCitySelectList" resultType="java.lang.Long">
        SELECT
        fk_area_city_id_hold
        FROM
        m_event
        WHERE
        fk_area_city_id_hold IS NOT NULL and fk_company_id = #{companyId}
        GROUP BY
        fk_area_city_id_hold
    </select>
    <select id="getCostList" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        me.id,
        me.fk_company_id,
        me.fk_event_type_id,
        me.num,
        me.event_time,
        me.event_time_end,
        me.event_theme,
        me.event_target,
        me.event_target_leader,
        me.fk_area_country_id_hold,
        me.fk_area_state_id_hold,
        me.fk_area_city_id_hold,
        me.fk_staff_id_leader1,
        me.fk_staff_id_leader2,
        me.attended_count,
        me.fk_currency_type_num,
        me.`status`,
        me.remark,
        mec.id as fkEventCostId,
        mec.fk_receipt_form_id,
        mec.fk_currency_type_num AS eventCostCurrencyTypeNum,
        mec.amount AS eventCostAmount,
        mec.remark AS eventCostRemark,
        mec.amount_receivable
        FROM
        m_event AS me
        RIGHT JOIN
        m_event_cost AS mec
        ON
        me.id = mec.fk_event_id
        where 1=1
        <if test="eventCostDto.fkReceiptFormId != null">
            AND mec.fk_receipt_form_id = #{eventCostDto.fkReceiptFormId}
        </if>
        <if test="eventCostDto.fkEventBillId != null">
            AND mec.fk_event_bill_id = #{eventCostDto.fkEventBillId}
        </if>
    </select>
    <select id="getEventsByName" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        a.id,
        a.event_theme,
        a.num,
        CONCAT("【",a.num,"】",IF(a.event_theme is null or a.event_theme = "","无活动主题",a.event_theme),"，",IFNULL(b.name_chn,"无举办城市")) fullName
        FROM
        m_event a
        left join ais_institution_center.u_area_city b on a.fk_area_city_id_hold = b.id
        WHERE 1=1
        <if test="companyId !=null and companyId !=''">
            and a.fk_company_id =#{companyId}
        </if>
<!--        <if test="eventName !=null and eventName !=''">-->
<!--            and event_theme like CONCAT("%",#{eventName},"%")-->
<!--        </if>-->
        <if test="eventName !=null and eventName !=''">
        HAVING fullName like CONCAT("%",#{eventName},"%")
        </if>
        ORDER BY a.gmt_create desc
        <if test="eventName !=null and eventName !=''">
        limit 20
        </if>
    </select>
    <select id="getEventSelect" resultType="com.get.salecenter.vo.EventVo">
        SELECT
        a.id,
        a.event_theme,
        a.num,
        CONCAT("【",a.num,"】",IF(a.event_theme is null or a.event_theme = "","无活动主题",a.event_theme),"，",IFNULL(b.name_chn,"无举办城市")) fullName
        FROM
        m_event a
        left join ais_institution_center.u_area_city b on a.fk_area_city_id_hold = b.id
        left join ais_permission_center.m_staff c on c.login_id = a.gmt_create_user
        WHERE 1=1
        <if test="companyId !=null and companyId !=''">
            and a.fk_company_id =#{companyId}
        </if>
        <if test="staffFollowerIds !=null and staffFollowerIds.size()>0">
            and c.id in
          <foreach collection="staffFollowerIds" item="staffFollowerId" open="(" separator="," close=")">
            #{staffFollowerId}
          </foreach>
        </if>
        ORDER BY a.gmt_create desc
    </select>
    <select id="getEventRegistrationStatistics"
            resultType="com.get.salecenter.vo.EventRegistrationStatisticsVo">
        SELECT
            a.*,
            b.fk_company_id,
            b.event_time,
            b.event_time_end,
            b.event_theme,
            b.`status` as eventStatus,
            b.fk_area_city_id_hold
        FROM
            `m_event_registration` a
                LEFT JOIN m_event b on a.fk_event_id = b.id
        where 1=1
        <if test="eventRegistrationStatisticsVo.fkCompanyId != null">
            and b.fk_company_id = #{eventRegistrationStatisticsVo.fkCompanyId}
        </if>
        <if test="eventRegistrationStatisticsVo.fkInstitutionProviderName !=null and eventRegistrationStatisticsVo.fkInstitutionProviderName !=''">
        and EXISTS (
                    SELECT
                        1
                    FROM
                        ais_institution_center.m_institution_provider a1
                    WHERE
                        a1.id = a.fk_institution_provider_id
                      AND (
                            a1.`name` like CONCAT("%",#{eventRegistrationStatisticsVo.fkInstitutionProviderName},"%")
                            or
                            a1.name_chn like CONCAT("%",#{eventRegistrationStatisticsVo.fkInstitutionProviderName},"%")
                            or
                            CONCAT(a1.`name`,"（",a1.name_chn,"）") like CONCAT("%",#{eventRegistrationStatisticsVo.fkInstitutionProviderName},"%")
                        )
                )
        </if>
        <if test="eventRegistrationStatisticsVo.eventTimeStart != null and eventRegistrationStatisticsVo.eventTimeStart.toString() !=''">
            and DATE_FORMAT(b.event_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventRegistrationStatisticsVo.eventTimeStart},'%Y-%m-%d')
            and DATE_FORMAT(b.event_time_end,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventRegistrationStatisticsVo.eventTimeStart},'%Y-%m-%d')
        </if>
        <if test="eventRegistrationStatisticsVo.eventTimeEnd != null and eventRegistrationStatisticsVo.eventTimeEnd.toString() !=''">
            and DATE_FORMAT(b.event_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventRegistrationStatisticsVo.eventTimeEnd},'%Y-%m-%d')
            and DATE_FORMAT(b.event_time_end,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventRegistrationStatisticsVo.eventTimeEnd},'%Y-%m-%d')
        </if>
        <if test="eventRegistrationStatisticsVo.eventTheme !=null and eventRegistrationStatisticsVo.eventTheme !=''">
            and b.event_theme like CONCAT("%",#{eventRegistrationStatisticsVo.eventTheme},"%")
        </if>
        <if test="eventRegistrationStatisticsVo.eventStatusList !=null and eventRegistrationStatisticsVo.eventStatusList.size()>0">
            and b.`status` in
            <foreach collection="eventRegistrationStatisticsVo.eventStatusList" item="eventStatus" open="(" separator="," close=")">
                #{eventStatus}
            </foreach>
        </if>
        ORDER BY a.gmt_create desc
    </select>

</mapper>