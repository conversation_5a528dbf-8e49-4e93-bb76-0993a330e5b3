package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 15:15
 * @verison: 1.0
 * @description:
 */
@Data
public class EventDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 活动类型Id
     */
    @NotNull(message = "活动类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动类型Id")
    private Long fkEventTypeId;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String num;

    /**
     * 活动时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "活动时间")
    private Date eventTime;

    /**
     * 活动主题
     */
    @NotBlank(message = "活动主题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动主题")
    private String eventTheme;

    @ApiModelProperty(value = "主题补充说明")
    private String eventThemeNote;
    /**
     * 活动目标对象
     */
    @ApiModelProperty(value = "活动目标对象")
    private String eventTarget;

    /**
     * 活动目标对象负责人
     */
    @ApiModelProperty(value = "活动目标对象负责人")
    private String eventTargetLeader;

    /**
     * 活动举办国家Id
     */
    @ApiModelProperty(value = "活动举办国家Id")
    private Long fkAreaCountryIdHold;

    /**
     * 活动举办州省Id
     */
    @ApiModelProperty(value = "活动举办州省Id")
    private Long fkAreaStateIdHold;

    /**
     * 活动举办城市Id
     */
    @ApiModelProperty(value = "活动举办城市Id")
    private Long fkAreaCityIdHold;

    /**
     * 员工Id（负责人1）
     */
    @ApiModelProperty(value = "员工Id（负责人1）")
    private Long fkStaffIdLeader1;

    /**
     * 员工Id（负责人2）
     */
    @ApiModelProperty(value = "员工Id（负责人2）")
    private Long fkStaffIdLeader2;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * BD预算场地费用
     */
    @ApiModelProperty(value = "BD预算场地费用")
    @Column(name = "bd_venue_amount")
    private BigDecimal bdVenueAmount;

    /**
     * BD预算餐饮费用
     */
    @ApiModelProperty(value = "BD预算餐饮费用")
    @Column(name = "bd_food_amount")
    private BigDecimal bdFoodAmount;

    /**
     * BD预算奖品费用
     */
    @ApiModelProperty(value = "BD预算奖品费用")
    @Column(name = "bd_prize_amount")
    private BigDecimal bdPrizeAmount;

    /**
     * BD预算其他费用
     */
    @ApiModelProperty(value = "BD预算其他费用")
    @Column(name = "bd_other_amount")
    private BigDecimal bdOtherAmount;
    /**
     * 预算金额
     */
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;

    /**
     * 实际金额
     */
    @ApiModelProperty(value = "实际金额")
    private BigDecimal actualAmount;

    /**
     * 预算人数
     */
    @ApiModelProperty(value = "预算人数")
    @NotNull(message = "预算人数不能为空", groups = {Add.class, Update.class})
    private Integer budgetCount;

    /**
     * 参加人数
     */
    @ApiModelProperty(value = "参加人数")
    private Integer attendedCount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态：0计划/1结束/2取消
     */
    @NotNull(message = "状态不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "状态：0计划/1结束/2取消")
    private Integer status;

    //自定义内容
    /**
     * 开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期")
    private Date eventTimeBeg;

    /**
     * 结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private Date eventTimeEnd;

    /**
     * 活动对象国家(多选)
     */
    @ApiModelProperty(value = "活动对象国家(多选)")
    private List<Long> eventTargetCountryList;

    /**
     * 负责人姓名搜索关键字
     */
    @ApiModelProperty(value = "负责人搜索关键字")
    private String staffNameLeaderKey;

    /**
     * 查询条件-不选公司时的公司ids
     */
    @JsonIgnore
    private List<Long> companyIds;

    /**
     * 查询条件-活动对象国家
     */
    @JsonIgnore
    private List<Long> eventIds;

    /**
     * 查询条件-负责人
     */
    @JsonIgnore
    private List<Long> staffIds;

    /**
     * 分组：0国家/1区域/2负责人/3类型
     */
    @ApiModelProperty(value = "分组：0国家/1区域/2负责人/3类型")
    private Integer groupBy;

    /**
     * 查询条件-状态：0计划/1结束/2取消
     */
    @ApiModelProperty(value = "查询条件-状态：0计划/1结束/2取消")
    private List<Integer> statusList;

    /**
     * 活动举办区域ids
     */
    @ApiModelProperty(value = "活动举办区域ids")
    private List<Integer> areaStateHoldIds;

    /**
     * 活动举办城市ids
     */
    @ApiModelProperty(value = "活动举办城市ids")
    private List<Integer> areaCityHoldIds;

    /**
     * 活动负责人ids
     */
    @ApiModelProperty(value = "活动负责人ids")
    private List<Integer> fkStaffIdLeader1Ids;

    /**
     * 活动第二负责人ids
     */
    @ApiModelProperty(value = "活动第二负责人ids")
    private List<Integer> fkStaffIdLeader2Ids;

    /**
     * 活动类型Ids
     */
    @ApiModelProperty(value = "活动类型Ids")
    private List<Integer> eventTypeIds;

    /**
     * 活动时间排序方式
     */
    @ApiModelProperty(value = "活动时间排序方式（正序:asc 倒序:desc）")
    private String orderType;
    /**
     * 创建开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建开始日期")
    private Date eventCreateTimeBeg;

    /**
     * 创建结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建结束日期")
    private Date eventCreateTimeEnd;


    /**
     * 修改开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改开始日期")
    private Date eventUpdateTimeBeg;

    /**
     * 修改结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改结束日期")
    private Date eventUpdateTimeEnd;

    @ApiModelProperty(value = "是否待定：true是/false否")
    private Boolean isEventRegistrationStatus;

    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选")
    private String publicLevel;

    @ApiModelProperty(value = "已分配活动费用")
    private BigDecimal allocatedAmount;

}
