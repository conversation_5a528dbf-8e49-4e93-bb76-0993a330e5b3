package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.MReleaseInfoService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发版信息
 */
@Api(tags = "发版信息项管理")
@RestController
@RequestMapping("platform/releaseInfo")
public class MReleaseInfoController {
    /**
     * 服务对象
     */
    @Resource
    private MReleaseInfoService mReleaseInfoService;

    @ApiOperation(value = "平台类型下拉", notes = "平台类型下拉")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/平台类型下拉")
    @GetMapping("getPlatformTypeDropDown")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<PlatFormTypeVo> getPlatformTypeDropDown() {
        return new ListResponseBo<>(mReleaseInfoService.getPlatformTypeDropDown());
    }


    @ApiOperation(value = "分页查询所有数据", notes = "分页查询所有数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/查询")
    @PostMapping("list")
    public ResponseBo selectAll(@RequestBody SearchBean<ReleaseInfoSearchDto> page) {
        List<ReleaseInfoAndItemVo> datas = mReleaseInfoService.getReleaseInfoAndItem(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "根据id查询详细数据", notes = "根据id查询详细数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/详情")
    @GetMapping("detail/{id}")
    public ResponseBo<ReleaseInfoAndItemVo> getDetailedInformationById(@PathVariable("id") Long id) {
        return new ResponseBo<>(mReleaseInfoService.getDetailedInformationById(id));
    }

    @ApiOperation(value = "修改数据", notes = "修改数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = "中台中心/发版信息项管理/修改")
    @PostMapping("editReleaseInfoAndItem")
    public ResponseBo editReleaseInfoAndItem(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.editReleaseInfoAndItem(releaseInfoAndItemDto);
        return ResponseBo.ok();
    }

    //更改发版信息的状态 status int(10) DEFAULT NULL COMMENT '枚举：0待发布/1已发布/2已撤回',
    @ApiOperation(value = "更改发版信息的状态", notes = "更改发版信息的状态 枚举：0待发布/1已发布/2已撤回")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.EDIT, description = "中台中心/发版信息项管理/更改发版信息的状态")
    @PostMapping("updateReleaseInfoStatus")
    public ResponseBo updateReleaseInfoStatus(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.updateReleaseInfoStatus(releaseInfoAndItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "新增数据", notes = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.ADD, description = "中台中心/发版信息项管理/新增")
    @PostMapping("addReleaseInfoAndItem")
    public ResponseBo addReleaseInfoAndItem(@RequestBody ReleaseInfoAndItemDto releaseInfoAndItemDto) {
       mReleaseInfoService.addReleaseInfoAndItem(releaseInfoAndItemDto);
       return ResponseBo.ok();
    }

    @ApiOperation(value = "删除数据", notes = "删除数据")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.DELETE, description = "中台中心/发版信息项管理/删除")
    @GetMapping("deleteReleaseInfoAndItem")
    public ResponseBo delete(@RequestParam("id") Long id) {
        mReleaseInfoService.deleteReleaseInfoAndItem(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "获取AIS权限菜单", notes = "获取AIS权限菜单")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/获取AIS权限菜单")
    @PostMapping("getAisPermissionMenu")
    public ResponseBo getAisPermissionMenu() {
        return new ResponseBo<>(mReleaseInfoService.getAisPermissionMenu());
    }


    @ApiOperation(value = "获取Partner权限菜单", notes = "获取Partner权限菜单")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/获取Partner权限菜单")
    @PostMapping("getPartnerPermissionMenu")
    public ResponseBo getPartnerPermissionMenu(@RequestBody GetPermissionMenuDto getPermissionMenuDto) {
        return new ResponseBo<>(mReleaseInfoService.getPartnerPermissionMenu(getPermissionMenuDto));
    }



    @ApiOperation(value = "根据用户权限获取列表信息", notes = "根据用户权限获取列表信息")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/根据用户权限获取列表信息")
    @PostMapping("getUserListByResourceKeys")
    public ResponseBo getUserListByResourceKeys(@RequestBody SearchBean<UserScopedDataDto> page) {
        List<ReleaseInfoAndItemVo> datas = mReleaseInfoService.getUserListByResourceKeys(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "根据用户权限获取列表详情信息", notes = "根据用户权限获取列表详情信息")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/根据用户权限获取列表详情信息")
    @PostMapping("getDetailByResourceKeys")
    public ResponseBo<ReleaseInfoAndItemVo> getDetailByResourceKeys(@RequestBody UserScopedDataDto userScopedDataDto) {
        return new ResponseBo<>(mReleaseInfoService.getDetailByResourceKeys(userScopedDataDto));
    }


    //根据用户权限获取最新创建的一条数据
    @ApiOperation(value = "根据用户权限获取最新创建的一条数据", notes = "根据用户权限获取最新创建的一条数据")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/根据用户权限获取最新创建的一条数据")
    @PostMapping("getOneByPermission")
    public ResponseBo<ReleaseInfoAndItemVo> getUserOneByPermission(@RequestBody UserScopedDataDto userScopedDataDto) {
        return new ResponseBo<>(mReleaseInfoService.getUserOneByResourceKeys(userScopedDataDto));
    }


}

