package com.get.workflowcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BatchApproveDto {

    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;

    @ApiModelProperty(value = "流程实例ID")
    private String processInstId;

    @ApiModelProperty("任务版本号")
    private Integer taskVersion;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("审批意见")
    private String msg;

    @ApiModelProperty("审批状态: 1同意 0驳回")
    private String approvalStatus;

}
